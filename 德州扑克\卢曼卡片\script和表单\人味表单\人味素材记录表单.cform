{
  "id": "human-touch-material-form",
  "fields": [
    {
      "id": "date",
      "label": "记录日期",
      "type": "text",
      "defaultValue": "{{date:YYYY-MM-DD}}",
      "description": "素材记录的日期"
    },
    {
      "id": "materialType",
      "label": "素材类型",
      "type": "select",
      "options": [
        {"id": "conflict-scene", "label": "冲突现场（中景）", "value": "冲突现场"},
        {"id": "industry-undercurrent", "label": "行业暗流（大景）", "value": "行业暗流"},
        {"id": "soul-questioning", "label": "灵魂拷问（特写）", "value": "灵魂拷问"},
        {"id": "evidence-archive", "label": "证据存档", "value": "证据存档"},
        {"id": "life-metaphor", "label": "生活隐喻", "value": "生活隐喻"},
        {"id": "custom", "label": "其他类型", "value": "custom"}
      ],
      "description": "选择人味素材的类型"
    },
    {
      "id": "customType",
      "label": "自定义类型（如选择其他）",
      "type": "text",
      "description": "当选择其他类型时填写具体内容"
    },
    {
      "id": "sceneLocation",
      "label": "场景地点",
      "type": "select",
      "options": [
        {"id": "client-office", "label": "客户办公室", "value": "客户办公室"},
        {"id": "meeting-room", "label": "会议室", "value": "会议室"},
        {"id": "coffee-shop", "label": "咖啡厅", "value": "咖啡厅"},
        {"id": "phone-call", "label": "电话沟通", "value": "电话沟通"},
        {"id": "industry-event", "label": "行业活动", "value": "行业活动"},
        {"id": "daily-life", "label": "日常生活", "value": "日常生活"},
        {"id": "online-meeting", "label": "线上会议", "value": "线上会议"}
      ],
      "description": "选择素材发生的场景地点"
    },
    {
      "id": "originalWords",
      "label": "原话记录",
      "type": "textarea",
      "rows": 3,
      "description": "记录客户/同事的原话（带行业黑话）"
    },
    {
      "id": "bodyLanguage",
      "label": "身体语言/环境细节",
      "type": "textarea",
      "rows": 2,
      "description": "记录说话时的身体语言和环境细节"
    },
    {
      "id": "industryBehavior",
      "label": "行业集体行为",
      "type": "textarea",
      "rows": 2,
      "description": "记录行业内的集体行为或趋势"
    },
    {
      "id": "regulatoryGap",
      "label": "监管落地变形",
      "type": "textarea",
      "rows": 2,
      "description": "记录监管政策在实际执行中的变形"
    },
    {
      "id": "counterIntuitive",
      "label": "反常识洞察",
      "type": "textarea",
      "rows": 3,
      "description": "记录你的反常识洞察和独特观点"
    },
    {
      "id": "metaphor",
      "label": "神吐槽/比喻",
      "type": "textarea",
      "rows": 2,
      "description": "记录生动的比喻或神吐槽"
    },
    {
      "id": "lifeConnection",
      "label": "生活连接点",
      "type": "select",
      "options": [
        {"id": "food-cooking", "label": "美食烹饪", "value": "美食烹饪"},
        {"id": "family-relationship", "label": "家庭关系", "value": "家庭关系"},
        {"id": "childhood-memory", "label": "童年记忆", "value": "童年记忆"},
        {"id": "daily-routine", "label": "日常习惯", "value": "日常习惯"},
        {"id": "nature-season", "label": "自然季节", "value": "自然季节"},
        {"id": "travel-experience", "label": "旅行经历", "value": "旅行经历"},
        {"id": "reading-movie", "label": "读书观影", "value": "读书观影"}
      ],
      "description": "选择与生活的连接点"
    },
    {
      "id": "emotionalCore",
      "label": "情感内核",
      "type": "select",
      "options": [
        {"id": "helplessness", "label": "无奈", "value": "无奈"},
        {"id": "irony", "label": "讽刺", "value": "讽刺"},
        {"id": "warmth", "label": "温暖", "value": "温暖"},
        {"id": "anger", "label": "愤怒", "value": "愤怒"},
        {"id": "hope", "label": "希望", "value": "希望"},
        {"id": "confusion", "label": "困惑", "value": "困惑"},
        {"id": "enlightenment", "label": "顿悟", "value": "顿悟"}
      ],
      "description": "选择这个素材的情感内核"
    },
    {
      "id": "evidenceFile",
      "label": "证据文件",
      "type": "text",
      "description": "可脱敏使用的文件截图或对话记录"
    },
    {
      "id": "applicationScene",
      "label": "应用场景",
      "type": "select",
      "options": [
        {"id": "article-opening", "label": "文章开头", "value": "文章开头"},
        {"id": "case-analysis", "label": "案例分析", "value": "案例分析"},
        {"id": "industry-observation", "label": "行业观察", "value": "行业观察"},
        {"id": "client-story", "label": "客户故事", "value": "客户故事"},
        {"id": "personal-reflection", "label": "个人反思", "value": "个人反思"},
        {"id": "social-media", "label": "社交媒体", "value": "社交媒体"}
      ],
      "description": "选择这个素材的主要应用场景"
    }
  ],
  "action": {
    "type": "runScript",
    "scriptSource": "inline",
    "code": "async function entry() {\n  const { form, app, requestUrl, Notice } = this.$context;\n  \n  try {\n    // 调试：打印所有可能的form属性\n    console.log('=== 人味素材表单数据调试 ===');\n    console.log('form对象:', form);\n    console.log('form的所有属性:', Object.keys(form));\n    for (let key in form) {\n      console.log(`${key}: ${form[key]}`);\n    }\n    \n    // 获取表单数据\n    const date = form.date || new Date().toISOString().split('T')[0];\n    const materialType = form.materialType || form['materialType'] || '冲突现场';\n    const customType = form.customType || form['customType'] || '';\n    const sceneLocation = form.sceneLocation || form['sceneLocation'] || '客户办公室';\n    const originalWords = form.originalWords || form['originalWords'] || '记录的原话内容';\n    const bodyLanguage = form.bodyLanguage || form['bodyLanguage'] || '身体语言和环境细节';\n    const industryBehavior = form.industryBehavior || form['industryBehavior'] || '行业集体行为观察';\n    const regulatoryGap = form.regulatoryGap || form['regulatoryGap'] || '监管落地变形记录';\n    const counterIntuitive = form.counterIntuitive || form['counterIntuitive'] || '反常识洞察内容';\n    const metaphor = form.metaphor || form['metaphor'] || '生动的比喻或吐槽';\n    const lifeConnection = form.lifeConnection || form['lifeConnection'] || '美食烹饪';\n    const emotionalCore = form.emotionalCore || form['emotionalCore'] || '无奈';\n    const evidenceFile = form.evidenceFile || form['evidenceFile'] || '相关证据';\n    const applicationScene = form.applicationScene || form['applicationScene'] || '文章开头';\n    \n    const finalType = materialType === 'custom' ? (customType || '自定义类型') : materialType;\n    \n    // 生成当前日期（20250803格式）\n    const today = new Date();\n    const year = today.getFullYear();\n    const month = (today.getMonth() + 1).toString().padStart(2, '0');\n    const day = today.getDate().toString().padStart(2, '0');\n    const dateStr = `${year}${month}${day}`;\n    const timeStr = today.getHours().toString().padStart(2, '0') + today.getMinutes().toString().padStart(2, '0');\n    \n    // 使用YAML前置元数据结构\n    const yamlFrontmatter = `---\ndate: ${dateStr}\nmaterialType: ${finalType}\nsceneLocation: ${sceneLocation}\nlifeConnection: ${lifeConnection}\nemotionalCore: ${emotionalCore}\napplicationScene: ${applicationScene}\ntags:\n  - 人味素材\n  - ${finalType}\n  - ${emotionalCore}\n  - ${lifeConnection}\n  - 合规创作\ncreatedBy: 人味素材记录系统\naiModel: DeepSeek\n---`;\n    \n    // 生成基础内容模板\n    const baseTemplate = `# 人味素材：${finalType}\n\n## 📍 现场还原\n\n**时间地点**：${dateStr} | ${sceneLocation}\n\n**原话记录**：\n> "${originalWords}"\n\n**身体语言/环境细节**：\n${bodyLanguage}\n\n## 🌊 行业观察\n\n**集体行为**：\n${industryBehavior}\n\n**监管落地变形**：\n${regulatoryGap}\n\n## 💡 洞察提炼\n\n**反常识观点**：\n${counterIntuitive}\n\n**神吐槽/比喻**：\n> ${metaphor}\n\n## 🔗 生活连接\n\n**连接点**：${lifeConnection}\n**情感内核**：${emotionalCore}\n\n## 📎 证据存档\n\n${evidenceFile}\n\n## 🎯 应用场景\n\n**主要用途**：${applicationScene}`;\n\n    // AI人味化改造\n    let aiEnhancedContent = '';\n    const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec';\n    \n    const enhancedPrompt = `你是一位擅长人味创作的写作专家。请基于以下素材，进行人味化改造：\n\n原始素材：\n${baseTemplate}\n\n请你运用人味创作技巧，对这个素材进行深度加工：\n\n1. **生活化表达**：将专业术语转化为生活化的表达，用具体的生活场景做比喻\n2. **情感共鸣**：挖掘素材背后的人性洞察，找到能引起共鸣的情感点\n3. **故事化包装**：将干巴巴的记录转化为有画面感的小故事\n4. **细节放大**：抓住最有张力的细节进行放大和渲染\n5. **反差制造**：通过对比和反差增强表达效果\n\n要求：\n- 保持真实性，不虚构事实\n- 语言要有温度和质感\n- 避免AI化的表达方式\n- 体现专业深度但用人话表达\n- 形成可直接使用的创作素材\n\n请以人味创作专家的身份，将这个素材改造成有温度、有故事、有洞察的创作材料。`;\n    \n    try {\n      const aiResponse = await requestUrl({\n        url: 'https://api.deepseek.com/v1/chat/completions',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: 'deepseek-chat',\n          messages: [{ role: 'user', content: enhancedPrompt }],\n          temperature: 0.9,\n          max_tokens: 2000\n        })\n      });\n      \n      if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n        aiEnhancedContent = aiResponse.json.choices[0].message.content;\n      }\n    } catch (e) {\n      aiEnhancedContent = '(AI人味化改造暂时不可用，请手动补充创作加工)';\n    }\n\n    // 生成完整的markdown内容\n    const fullTemplate = `${yamlFrontmatter}\n\n${baseTemplate}\n\n---\n\n## ✨ AI人味化改造\n\n${aiEnhancedContent}\n\n---\n\n## 📝 创作应用记录\n\n<!-- 记录这个素材在实际创作中的使用情况 -->\n\n## 🔄 素材迭代优化\n\n<!-- 记录素材的进一步加工和优化 -->\n\n---\n\n*记录时间：${new Date().toLocaleString('zh-CN')} | AI人味化改造：DeepSeek | 创作素材库*`;\n    \n    // 生成文件名（使用20250803格式）\n    const fileName = `人味素材-${finalType}-${dateStr}-${timeStr}.md`;\n    const filePath = `工作室/肌肉/生成笔记/人味素材/${fileName}`;\n    \n    // 确保文件夹存在\n    const folderPath = '工作室/肌肉/生成笔记/人味素材';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    // 创建文件\n    const file = await app.vault.create(filePath, fullTemplate);\n    new Notice(`人味素材已创建: ${fileName}`);\n    \n    // 打开新创建的文件\n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 人味素材已生成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('生成人味素材失败:', error);\n    new Notice('生成失败: ' + error.message);\n    return `❌ 生成失败: ${error.message}`;\n  }\n}\n\nexports.default = {\n  entry: entry\n};"
  },
  "title": "人味素材记录表单"
}
