﻿/**
 * AI批量处理脚本
 * 功能：批量处理多个文件，一次性对多个笔记文件执行相同的AI操作
 * 作者：Builder
 * 版本�?.0
 */

/**
 * AI批量处理函数
 * @param {string} token - API密钥
 * @param {string} operation - 要执行的操作类型（如'summary', 'translate', 'keywords'等）
 * @param {string} filePattern - 文件匹配模式（如'*.md', '2024-*.md', 'project/**/*.md'�? * @param {string} modelType - AI模型类型，默认为'GLM-4-Flash'
 * @returns {Promise<Object>} 处理结果
 */
async function aiBatch(token, operation, filePattern, modelType = 'GLM-4-Flash') {
    try {
        // 验证参数
        if (!token || !operation || !filePattern) {
            throw new Error('缺少必要参数：token, operation, filePattern');
        }

        console.log(`开始批量处理：操作类型=${operation}, 文件模式=${filePattern}`);
        
        // 获取当前vault
        const vault = app.vault;
        const files = vault.getMarkdownFiles();
        
        // 根据文件模式筛选文�?        const matchedFiles = filterFilesByPattern(files, filePattern);
        
        if (matchedFiles.length === 0) {
            new Notice('没有找到匹配的文�?);
            return { success: false, message: '没有找到匹配的文�? };
        }
        
        new Notice(`找到 ${matchedFiles.length} 个文件，开始批量处�?..`);
        
        const results = [];
        let successCount = 0;
        let failCount = 0;
        
        // 批量处理文件
        for (let i = 0; i < matchedFiles.length; i++) {
            const file = matchedFiles[i];
            try {
                console.log(`处理文件 ${i + 1}/${matchedFiles.length}: ${file.name}`);
                
                const content = await vault.read(file);
                const result = await processFileWithAI(content, operation, token, modelType);
                
                // 根据操作类型保存结果
                await saveProcessResult(file, result, operation);
                
                results.push({
                    file: file.name,
                    success: true,
                    result: result
                });
                
                successCount++;
                
                // 添加延迟避免API限制
                await new Promise(resolve => setTimeout(resolve, 1000));
                
            } catch (error) {
                console.error(`处理文件 ${file.name} 失败:`, error);
                results.push({
                    file: file.name,
                    success: false,
                    error: error.message
                });
                failCount++;
            }
        }
        
        new Notice(`批量处理完成！成功：${successCount}，失败：${failCount}`);
        
        return {
            success: true,
            totalFiles: matchedFiles.length,
            successCount,
            failCount,
            results
        };
        
    } catch (error) {
        console.error('批量处理失败:', error);
        new Notice(`批量处理失败: ${error.message}`);
        return { success: false, error: error.message };
    }
}

/**
 * 根据模式筛选文�? * @param {Array} files - 文件列表
 * @param {string} pattern - 匹配模式
 * @returns {Array} 匹配的文�? */
function filterFilesByPattern(files, pattern) {
    // 简单的模式匹配实现
    if (pattern === '*.md') {
        return files;
    }
    
    if (pattern.includes('*')) {
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        return files.filter(file => regex.test(file.path));
    }
    
    return files.filter(file => file.path.includes(pattern));
}

/**
 * 使用AI处理文件内容
 * @param {string} content - 文件内容
 * @param {string} operation - 操作类型
 * @param {string} token - API密钥
 * @param {string} modelType - 模型类型
 * @returns {Promise<string>} 处理结果
 */
async function processFileWithAI(content, operation, token, modelType) {
    const prompts = {
        'summary': '请为以下内容生成简洁的摘要�?,
        'translate': '请将以下内容翻译成英文：',
        'keywords': '请为以下内容提取5-10个关键词�?,
        'format': '请优化以下内容的格式和结构：',
        'expand': '请扩展以下内容，添加更多细节�?
    };
    
    const prompt = prompts[operation] || `请对以下内容执行${operation}操作：`;
    
    const requestBody = {
        model: modelType,
        messages: [
            {
                role: "user",
                content: `${prompt}\n\n${content}`
            }
        ],
        temperature: 0.7,
        max_tokens: 2000
    };
    
    const response = await fetch('https://open.bigmodel.cn/api/paas/v4/chat/completions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
    }
    
    const data = await response.json();
    return data.choices[0].message.content;
}

/**
 * 保存处理结果
 * @param {Object} file - 文件对象
 * @param {string} result - 处理结果
 * @param {string} operation - 操作类型
 */
async function saveProcessResult(file, result, operation) {
    const vault = app.vault;
    
    // 根据操作类型决定保存方式
    switch (operation) {
        case 'summary':
            // 在文件开头添加摘�?            const content = await vault.read(file);
            const newContent = `## AI摘要\n${result}\n\n---\n\n${content}`;
            await vault.modify(file, newContent);
            break;
            
        case 'keywords':
            // 添加标签
            await app.fileManager.processFrontMatter(file, (frontmatter) => {
                frontmatter.tags = frontmatter.tags || [];
                const keywords = result.split(/[,，、\n]/).map(k => k.trim()).filter(k => k);
                frontmatter.tags.push(...keywords);
            });
            break;
            
        default:
            // 创建新文件保存结�?            const resultFileName = `${file.basename}_${operation}_result.md`;
            const resultPath = file.parent ? `${file.parent.path}/${resultFileName}` : resultFileName;
            await vault.create(resultPath, result);
    }
}

// 导出函数
module.exports = aiBatch;

// 使用说明
console.log(`
AI批量处理脚本已加载！

使用方法�?aiBatch(token, operation, filePattern, modelType)

参数说明�?- token: API密钥
- operation: 操作类型（summary/translate/keywords/format/expand�?- filePattern: 文件匹配模式
- modelType: AI模型类型（可选，默认GLM-4-Flash�?
示例�?// 为所�?024年的笔记生成摘要
aiBatch('your-token', 'summary', '2024-*.md')

// 批量提取关键�?aiBatch('your-token', 'keywords', '*.md', 'GLM-4-Flash')
`);
