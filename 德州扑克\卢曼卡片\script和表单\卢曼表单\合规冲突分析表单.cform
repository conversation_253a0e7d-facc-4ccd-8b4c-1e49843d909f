{"id": "compliance-conflict-analysis-form", "fields": [{"id": "scenario", "label": "具体场景", "type": "text", "description": "描述具体的合规冲突场景"}, {"id": "party-a", "label": "甲方（法规要求/监管观点）", "type": "textarea", "rows": 3, "description": "法规要求或监管观点，包含法条出处"}, {"id": "party-b", "label": "乙方（业务需求/技术限制）", "type": "textarea", "rows": 3, "description": "业务需求或技术限制，包含实际案例"}, {"id": "legal-risk", "label": "法律风险评估", "type": "select", "options": [{"id": "high-risk", "label": "高风险", "value": "高风险"}, {"id": "medium-risk", "label": "中风险", "value": "中风险"}, {"id": "low-risk", "label": "低风险", "value": "低风险"}], "description": "评估法律风险等级"}, {"id": "business-impact", "label": "业务影响分析", "type": "textarea", "rows": 3, "description": "分析对业务的具体影响"}, {"id": "balance-solution", "label": "平衡方案", "type": "textarea", "rows": 5, "description": "提供可执行的平衡建议"}, {"id": "related-regulations", "label": "相关法规", "type": "text", "description": "相关法规链接或标识"}, {"id": "similar-cases", "label": "类似案例", "type": "text", "description": "类似案例链接或标识"}], "action": {"id": "generate-compliance-conflict-analysis", "type": "runScript", "targetFolder": "", "fileName": "", "scriptSource": "inline", "code": "async function entry() {\n  const { form, $selection } = this.$context;\n  \n  const template = `### 合规战场：${form.scenario}\n**对阵双方**：\n- 甲方：${form['party-a']}\n- 乙方：${form['party-b']}\n**我的合规判断**：\n- 法律风险评估：${form['legal-risk']}\n- 业务影响分析：${form['business-impact']}\n- 平衡方案：${form['balance-solution']}\n→ 关联：[[${form['related-regulations']}]] [[${form['similar-cases']}]]`;\n  \n  return template;\n}\n\nexports.default = {\n  entry: entry,\n};"}, "title": "合规冲突分析表单"}