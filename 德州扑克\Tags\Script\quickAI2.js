// 快速AI助手 - 极简版本，支持直接传入提示词和选择保存位置
async function quickAI(token, promptText, saveOptions = "property") {
  if (!promptText || !token) {
    new Notice("❌ 请设置密钥和提示词");
    return null;
  }

  const { currentFile } = this;
  const file = currentFile;
  const fileContent = await app.vault.cachedRead(file);
  const title = file.basename;

  // 构建完整提示词
  const fullPrompt = `${promptText}\n\n文件标题：${title}\n文件内容：\n${fileContent}\n\n请直接输出结果，不需要额外解释。`;

  // 显示处理提示
  new Notice("🤖 AI处理中...", 2000);

  try {
    // 调用智谱清言API
    const response = await obsidian.requestUrl({
      method: "POST",
      url: "https://open.bigmodel.cn/api/paas/v4/chat/completions",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "GLM-4-Flash",
        messages: [{ role: "user", content: fullPrompt }],
        temperature: 0.7,
        max_tokens: 2000,
      }),
    });

    const result = response.json;
    const content = result.choices?.[0]?.message?.content;

    if (!content) {
      new Notice("❌ AI返回结果为空");
      return null;
    }

    // 根据保存选项处理结果
    await handleSaveOptions(saveOptions, content, file, promptText);

    return content;

  } catch (error) {
    new Notice(`❌ AI处理失败: ${error.message}`);
    console.error("AI处理错误:", error);
    return null;
  }

  // 处理保存选项
  async function handleSaveOptions(saveOptions, content, file, promptText) {
    const timestamp = new Date().toLocaleString();

    switch (saveOptions) {
      case "property":
        // 保存到属性
        app.fileManager.processFrontMatter(file, (frontmatter) => {
          frontmatter["AI结果"] = content.trim();
          frontmatter["处理时间"] = timestamp;
          frontmatter["提示词"] = promptText;
        });
        new Notice("✅ AI结果已保存到文件属性");
        break;

      case "content":
        // 保存到正文
        const currentContent = await app.vault.read(file);
        const newContent = currentContent + `\n\n## AI处理结果\n\n**提示词**: ${promptText}\n**处理时间**: ${timestamp}\n\n${content}`;
        await app.vault.modify(file, newContent);
        new Notice("✅ AI结果已追加到文件正文");
        break;

      case "both":
        // 同时保存到属性和正文
        // 1. 保存到属性
        app.fileManager.processFrontMatter(file, (frontmatter) => {
          frontmatter["AI结果"] = content.trim();
          frontmatter["处理时间"] = timestamp;
          frontmatter["提示词"] = promptText;
        });

        // 2. 保存到正文
        const currentContent2 = await app.vault.read(file);
        const newContent2 = currentContent2 + `\n\n## AI处理结果\n\n**提示词**: ${promptText}\n**处理时间**: ${timestamp}\n\n${content}`;
        await app.vault.modify(file, newContent2);

        new Notice("✅ AI结果已同时保存到属性和正文");
        break;

      case "copy":
        // 复制到剪贴板
        await navigator.clipboard.writeText(content);
        new Notice("✅ AI结果已复制到剪贴板");
        break;

      case "ask":
        // 询问用户选择
        const userChoice = await showSaveOptionsModal(content);
        if (userChoice && userChoice !== "ask") {
          await handleSaveOptions(userChoice, content, file, promptText);
        }
        break;

      default:
        // 默认保存到属性
        app.fileManager.processFrontMatter(file, (frontmatter) => {
          frontmatter["AI结果"] = content.trim();
          frontmatter["处理时间"] = timestamp;
          frontmatter["提示词"] = promptText;
        });
        new Notice("✅ AI结果已保存到文件属性");
    }
  }

  // 显示保存选项模态框
  async function showSaveOptionsModal(content) {
    return new Promise((resolve) => {
      const modal = new obsidian.Modal(app);
      modal.titleEl.setText("🤖 AI处理完成 - 选择保存方式");

      const container = modal.contentEl.createDiv();

      // 结果预览
      container.createEl("h4", { text: "处理结果预览：" });
      const preview = container.createEl("div", {
        attr: {
          style: "max-height: 200px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; margin: 10px 0; background: #f9f9f9; border-radius: 5px; font-family: monospace; white-space: pre-wrap; font-size: 12px;"
        }
      });
      preview.textContent = content.length > 500 ? content.substring(0, 500) + "..." : content;

      // 保存选项
      container.createEl("h4", { text: "请选择保存方式：" });

      const buttonContainer = container.createDiv();
      buttonContainer.style.display = "grid";
      buttonContainer.style.gridTemplateColumns = "1fr 1fr";
      buttonContainer.style.gap = "8px";
      buttonContainer.style.marginTop = "15px";

      const buttons = [
        { text: "🏷️ 保存到属性", action: "property", style: "background: #4CAF50; color: white;" },
        { text: "📝 保存到正文", action: "content", style: "background: #2196F3; color: white;" },
        { text: "📋 同时保存", action: "both", style: "background: #FF9800; color: white;" },
        { text: "📄 复制到剪贴板", action: "copy", style: "background: #9C27B0; color: white;" }
      ];

      buttons.forEach(btn => {
        const button = buttonContainer.createEl("button", { text: btn.text });
        button.style.cssText = btn.style + " padding: 8px; border: none; border-radius: 5px; cursor: pointer; font-size: 12px; font-weight: bold;";
        button.onclick = () => {
          modal.close();
          resolve(btn.action);
        };
      });

      // 取消按钮
      const cancelContainer = container.createDiv();
      cancelContainer.style.textAlign = "center";
      cancelContainer.style.marginTop = "10px";

      const cancelBtn = cancelContainer.createEl("button", { text: "❌ 取消" });
      cancelBtn.style.cssText = "background: #f44336; color: white; padding: 6px 15px; border: none; border-radius: 5px; cursor: pointer; font-size: 12px;";
      cancelBtn.onclick = () => {
        modal.close();
        resolve(null);
      };

      modal.open();
    });
  }
}

// 预设模板函数 - 支持保存选项
async function aiSummarize(token, saveOptions = "property") {
  return await quickAI.call(this, token, "请用3-5个要点总结这篇文章的核心内容", saveOptions);
}

async function aiTranslate(token, saveOptions = "property") {
  return await quickAI.call(this, token, "请将这篇文章翻译成英文，保持原意和语调", saveOptions);
}

async function aiRewrite(token, saveOptions = "content") {
  return await quickAI.call(this, token, "请改写这篇文章，使其更加生动有趣，适合社交媒体分享", saveOptions);
}

async function aiKeywords(token, saveOptions = "property") {
  return await quickAI.call(this, token, "请提取这篇文章的5-10个关键词，用逗号分隔", saveOptions);
}

async function aiOutline(token, saveOptions = "content") {
  return await quickAI.call(this, token, "请为这篇文章生成详细的大纲结构", saveOptions);
}

async function aiQuestions(token, saveOptions = "content") {
  return await quickAI.call(this, token, "请基于这篇文章生成5个深度思考问题", saveOptions);
}

async function aiSEO(token, saveOptions = "property") {
  return await quickAI.call(this, token, "请为这篇文章生成SEO友好的标题、描述和关键词", saveOptions);
}

async function aiAction(token, saveOptions = "content") {
  return await quickAI.call(this, token, "请基于这篇文章的内容，生成具体的行动计划和执行步骤", saveOptions);
}

// 批量处理当前文件夹
async function batchProcessFolder(token, promptText, saveOptions = "property") {
  const { currentFile } = this;
  const currentFolder = currentFile.parent;
  const files = currentFolder.children.filter(f => f.extension === 'md');

  new Notice(`🚀 开始批量处理文件夹中的 ${files.length} 个文件...`);

  let successCount = 0;
  let failCount = 0;

  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    new Notice(`📝 处理中 (${i + 1}/${files.length}): ${file.basename}`);

    try {
      // 临时切换当前文件
      const originalFile = this.currentFile;
      this.currentFile = file;

      // 处理文件
      await quickAI.call(this, token, promptText, saveOptions);
      successCount++;

      // 恢复原文件
      this.currentFile = originalFile;

      // 延迟1秒避免API限制
      if (i < files.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

    } catch (error) {
      failCount++;
      console.error(`处理文件 ${file.basename} 失败:`, error);
    }
  }

  new Notice(`✅ 批量处理完成！成功: ${successCount}, 失败: ${failCount}`);
}

// 使用示例和说明
const examples = {
  basic: `quickAI("你的密钥", "请总结这篇文章")                    // 默认保存到属性`,
  saveOptions: `
quickAI("你的密钥", "请总结这篇文章", "property")           // 保存到属性
quickAI("你的密钥", "请总结这篇文章", "content")            // 保存到正文
quickAI("你的密钥", "请总结这篇文章", "both")               // 同时保存
quickAI("你的密钥", "请总结这篇文章", "copy")               // 复制到剪贴板
quickAI("你的密钥", "请总结这篇文章", "ask")                // 询问用户选择
`,
  templates: `
aiSummarize("你的密钥")                    // 总结 → 属性
aiSummarize("你的密钥", "content")         // 总结 → 正文
aiTranslate("你的密钥", "both")            // 翻译 → 同时保存
aiKeywords("你的密钥")                     // 关键词 → 属性
aiSEO("你的密钥", "ask")                   // SEO → 询问选择
`,
  batch: `
batchProcessFolder("你的密钥", "请总结这篇文章")                    // 批量 → 属性
batchProcessFolder("你的密钥", "请总结这篇文章", "content")         // 批量 → 正文
batchProcessFolder("你的密钥", "请总结这篇文章", "both")            // 批量 → 同时保存
`
};

exports.default = {
  entry: quickAI,
  name: "quickAI",
  description: `快速AI助手 - 极简版智谱清言API工具

  ==请先在 https://open.bigmodel.cn/ 注册并获取 API 密钥==

  🎯 核心特点：
  - ⚡ 极简调用：直接传入提示词，一行代码搞定
  - 🔄 自动保存：默认保存到文件属性，可选复制到剪贴板
  - 📦 预设模板：内置8个常用AI处理模板
  - 📁 批量处理：支持批量处理当前文件夹所有文件
  - 🚀 高效快速：专为批量操作优化

  📖 基础用法：
  \`quickAI('你的密钥', '提示词')\`                         // 默认保存到属性
  \`quickAI('你的密钥', '提示词', 'content')\`              // 保存到正文
  \`quickAI('你的密钥', '提示词', 'both')\`                 // 同时保存
  \`quickAI('你的密钥', '提示词', 'copy')\`                 // 复制到剪贴板
  \`quickAI('你的密钥', '提示词', 'ask')\`                  // 询问用户选择

  💾 保存选项：
  - \`"property"\` - 保存到文件属性（frontmatter）
  - \`"content"\`  - 追加到文件正文末尾
  - \`"both"\`     - 同时保存到属性和正文
  - \`"copy"\`     - 复制到剪贴板
  - \`"ask"\`      - 弹窗询问用户选择

  🎨 预设模板：
  \`aiSummarize('密钥')\`              // 文章总结 → 属性
  \`aiSummarize('密钥', 'content')\`   // 文章总结 → 正文
  \`aiTranslate('密钥', 'both')\`      // 中英翻译 → 同时保存
  \`aiRewrite('密钥')\`                // 内容改写 → 正文
  \`aiKeywords('密钥')\`               // 关键词提取 → 属性
  \`aiOutline('密钥', 'content')\`     // 大纲生成 → 正文
  \`aiQuestions('密钥')\`              // 思考问题 → 正文
  \`aiSEO('密钥')\`                    // SEO优化 → 属性
  \`aiAction('密钥', 'ask')\`          // 行动计划 → 询问选择

  📁 批量处理：
  \`batchProcessFolder('密钥', '请总结这篇文章')\`              // 批量 → 属性
  \`batchProcessFolder('密钥', '请总结这篇文章', 'content')\`   // 批量 → 正文

  💡 使用场景：
  - 快速处理单个文件：一行代码完成AI任务
  - 批量处理文件夹：自动处理所有markdown文件
  - 预设模板：常用AI任务一键执行
  - 工作流集成：可与其他脚本组合使用

  ⚙️ 技术细节：
  - 使用GLM-4-Flash模型（免费额度）
  - 自动处理API限制（1秒延迟）
  - 结果保存到frontmatter属性
  - 支持错误处理和进度提示
  `,
};
