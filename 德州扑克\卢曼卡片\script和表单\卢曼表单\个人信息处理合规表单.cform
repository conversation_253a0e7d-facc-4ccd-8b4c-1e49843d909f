{"id": "personal-info-processing-form", "fields": [{"id": "business-scenario", "label": "业务场景", "type": "text", "description": "个人信息处理的业务场景"}, {"id": "general-info", "label": "一般个人信息", "type": "textarea", "rows": 2, "description": "涉及的一般个人信息类型"}, {"id": "sensitive-info", "label": "敏感个人信息", "type": "textarea", "rows": 2, "description": "涉及的敏感个人信息类型"}, {"id": "special-category", "label": "特殊类别信息", "type": "textarea", "rows": 2, "description": "生物识别、宗教信仰等特殊类别信息"}, {"id": "consent-basis", "label": "同意基础", "type": "textarea", "rows": 2, "description": "具体同意场景和撤回机制"}, {"id": "contract-basis", "label": "合同履行基础", "type": "textarea", "rows": 2, "description": "基于合同履行的处理情况"}, {"id": "legal-obligation", "label": "法定义务", "type": "textarea", "rows": 2, "description": "基于法定义务的处理情况"}, {"id": "public-interest", "label": "重大公共利益", "type": "textarea", "rows": 2, "description": "基于重大公共利益的处理情况"}, {"id": "anonymization", "label": "匿名化/去标识化", "type": "textarea", "rows": 2, "description": "匿名化或去标识化措施"}, {"id": "encryption", "label": "加密传输存储", "type": "textarea", "rows": 2, "description": "加密传输和存储措施"}, {"id": "access-control", "label": "访问控制", "type": "textarea", "rows": 2, "description": "访问控制措施"}, {"id": "data-minimization", "label": "数据最小化", "type": "textarea", "rows": 2, "description": "数据最小化原则的实施"}, {"id": "right-to-know", "label": "知情权保障", "type": "textarea", "rows": 2, "description": "知情权的保障措施"}, {"id": "access-right", "label": "访问权保障", "type": "textarea", "rows": 2, "description": "访问权的保障措施"}, {"id": "correction-deletion", "label": "更正删除权保障", "type": "textarea", "rows": 2, "description": "更正删除权的保障措施"}, {"id": "portability-right", "label": "可携带权保障", "type": "textarea", "rows": 2, "description": "可携带权的保障措施"}, {"id": "similar-activities", "label": "同类处理活动", "type": "text", "description": "同类处理活动的链接"}, {"id": "technical-standards", "label": "技术标准", "type": "text", "description": "相关技术标准的链接"}, {"id": "rights-response", "label": "权利响应流程", "type": "text", "description": "权利响应流程的链接"}], "action": {"id": "generate-personal-info-processing", "type": "runScript", "targetFolder": "", "fileName": "", "scriptSource": "inline", "code": "async function entry() {\n  const { form, app, requestUrl, Notice } = this.$context;\n  \n  try {\n    // 生成AI优化的内容\n    let aiEnhancedContent = '';\n    const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec';\n    \n    const prompt = `你是个人信息保护专家。请基于以下信息，提供专业的合规分析：\n\n业务场景：${form['business-scenario']}\n一般个人信息：${form['general-info']}\n敏感个人信息：${form['sensitive-info']}\n特殊类别信息：${form['special-category']}\n同意基础：${form['consent-basis']}\n合同基础：${form['contract-basis']}\n法定义务：${form['legal-obligation']}\n公共利益：${form['public-interest']}\n技术措施：匿名化(${form.anonymization})、加密(${form.encryption})、访问控制(${form['access-control']})、最小化(${form['data-minimization']})\n权利保障：知情权(${form['right-to-know']})、访问权(${form['access-right']})、更正删除权(${form['correction-deletion']})、可携带权(${form['portability-right']})\n\n请提供：\n1. 个人信息处理合规性评估\n2. 技术和管理措施的完善建议\n3. 个人权利保障机制的优化方案\n4. 潜在合规风险和应对策略\n\n要求：专业、详细、可操作。`;\n    \n    try {\n      const aiResponse = await requestUrl({\n        url: 'https://api.deepseek.com/v1/chat/completions',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: \"deepseek-chat\",\n          messages: [{ role: \"user\", content: prompt }],\n          temperature: 0.7,\n          max_tokens: 1800\n        })\n      });\n      \n      if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n        aiEnhancedContent = aiResponse.json.choices[0].message.content;\n      }\n    } catch (e) {\n      aiEnhancedContent = '(AI分析暂时不可用，请手动补充深度分析)';\n    }\n    \n    // 生成完整内容\n    const template = `### 个人信息处理：${form['business-scenario']}\n**数据分类**：\n- 一般个人信息：${form['general-info']}\n- 敏感个人信息：${form['sensitive-info']}\n- 特殊类别：${form['special-category']}\n**处理目的与法律基础**：\n- 同意：${form['consent-basis']}\n- 合同履行：${form['contract-basis']}\n- 法定义务：${form['legal-obligation']}\n- 重大公共利益：${form['public-interest']}\n**技术保护措施**：\n- 匿名化/去标识化：${form.anonymization}\n- 加密传输存储：${form.encryption}\n- 访问控制：${form['access-control']}\n- 数据最小化：${form['data-minimization']}\n**个人权利保障**：\n- 知情权：${form['right-to-know']}\n- 访问权：${form['access-right']}\n- 更正删除权：${form['correction-deletion']}\n- 可携带权：${form['portability-right']}\n→ 关联：[[${form['similar-activities']}]] [[${form['technical-standards']}]] [[${form['rights-response']}]]\n\n---\n\n## 🤖 AI专业评估\n\n${aiEnhancedContent}\n\n---\n\n## 📝 合规检查清单\n<!-- 基于分析结果制定的具体检查清单 -->\n\n\n## 🏷️ 标签\n#个人信息处理 #隐私保护 #数据合规 #${new Date().toISOString().split('T')[0]}\n\n---\n*生成时间：${new Date().toLocaleString()} | AI助手：DeepSeek*`;\n    \n    // 创建文件\n    const today = new Date();\n    const dateStr = today.toISOString().split('T')[0];\n    const timeStr = today.toTimeString().split(' ')[0].substring(0, 5).replace(':', '');\n    const fileName = `个人信息处理-${form['business-scenario']}-${dateStr}-${timeStr}.md`;\n    const filePath = `工作室/肌肉/生成笔记/个人信息处理/${fileName}`;\n    \n    // 检查并创建目录\n    const folderPath = '工作室/肌肉/生成笔记/个人信息处理';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    // 创建文件\n    const file = await app.vault.create(filePath, template);\n    new Notice(`个人信息处理分析已创建: ${fileName}`);\n    \n    // 打开新创建的文件\n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 个人信息处理分析已生成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('生成个人信息处理分析失败:', error);\n    new Notice('生成失败: ' + error.message);\n    return '❌ 生成失败，请检查配置';\n  }\n}\n\nexports.default = {\n  entry: entry,\n};"}, "title": "个人信息处理合规表单"}