{"id": "data-compliance-qa-form", "fields": [{"id": "business-scenario", "label": "具体业务场景", "type": "text", "description": "描述具体的业务场景"}, {"id": "data-usage-need", "label": "数据利用需求", "type": "textarea", "rows": 2, "description": "具体的数据利用需求"}, {"id": "privacy-protection-requirement", "label": "隐私保护要求", "type": "textarea", "rows": 2, "description": "相关的隐私保护要求"}, {"id": "legal-basis", "label": "法规依据", "type": "textarea", "rows": 2, "description": "GDPR/PIPL等相关法条"}, {"id": "interpretation", "label": "我的解读", "type": "textarea", "rows": 3, "description": "用技术/管理手段重构的解读"}, {"id": "operation-guide", "label": "操作指南", "type": "textarea", "rows": 4, "description": "具体到可执行的合规checklist"}, {"id": "related-regulations", "label": "相关法条解读", "type": "text", "description": "相关法条解读的链接"}, {"id": "similar-scenarios", "label": "类似场景处理", "type": "text", "description": "类似场景处理的链接"}, {"id": "risk-warning", "label": "风险提醒", "type": "textarea", "rows": 2, "description": "当前方案可能忽略的监管趋势变化"}], "action": {"id": "generate-data-compliance-qa", "type": "runScript", "targetFolder": "", "fileName": "", "scriptSource": "inline", "code": "async function entry() {\n  const { form, app, requestUrl, Notice } = this.$context;\n  \n  try {\n    // 生成AI优化的内容\n    let aiEnhancedContent = '';\n    const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec';\n    \n    const prompt = `你是数据合规领域的专家。请基于以下问答信息，提供深度分析和补充：\n\n业务场景：${form['business-scenario']}\n数据利用需求：${form['data-usage-need']}\n隐私保护要求：${form['privacy-protection-requirement']}\n法规依据：${form['legal-basis']}\n解读：${form.interpretation}\n操作指南：${form['operation-guide']}\n风险提醒：${form['risk-warning']}\n\n请提供：\n1. 对当前解读和操作指南的专业评估\n2. 补充可能遗漏的合规要点\n3. 提供更详细的实施步骤\n4. 分析潜在的合规陷阱和应对策略\n\n要求：专业、详细、可操作。`;\n    \n    try {\n      const aiResponse = await requestUrl({\n        url: 'https://api.deepseek.com/v1/chat/completions',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: \"deepseek-chat\",\n          messages: [{ role: \"user\", content: prompt }],\n          temperature: 0.7,\n          max_tokens: 1500\n        })\n      });\n      \n      if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n        aiEnhancedContent = aiResponse.json.choices[0].message.content;\n      }\n    } catch (e) {\n      aiEnhancedContent = '(AI分析暂时不可用，请手动补充深度分析)';\n    }\n    \n    // 生成完整内容\n    const template = `### Q：在${form['business-scenario']}中，如何平衡${form['data-usage-need']}与${form['privacy-protection-requirement']}？\n\nA：\n- **法规依据**：${form['legal-basis']}\n- **我的解读**：${form.interpretation}\n- **操作指南**：${form['operation-guide']}\n\n→ 关联：[[${form['related-regulations']}]] [[${form['similar-scenarios']}]]\n! 风险：当前方案可能忽略${form['risk-warning']}\n\n---\n\n## 🤖 AI专业补充\n\n${aiEnhancedContent}\n\n---\n\n## 📝 实践记录\n<!-- 记录实际应用中的效果和调整 -->\n\n\n## 🏷️ 标签\n#数据合规问答 #隐私保护 #数据合规 #${new Date().toISOString().split('T')[0]}\n\n---\n*生成时间：${new Date().toLocaleString()} | AI助手：DeepSeek*`;\n    \n    // 创建文件\n    const today = new Date();\n    const dateStr = today.toISOString().split('T')[0];\n    const timeStr = today.toTimeString().split(' ')[0].substring(0, 5).replace(':', '');\n    const fileName = `数据合规问答-${form['business-scenario']}-${dateStr}-${timeStr}.md`;\n    const filePath = `工作室/肌肉/生成笔记/数据合规问答/${fileName}`;\n    \n    // 检查并创建目录\n    const folderPath = '工作室/肌肉/生成笔记/数据合规问答';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    // 创建文件\n    const file = await app.vault.create(filePath, template);\n    new Notice(`数据合规问答已创建: ${fileName}`);\n    \n    // 打开新创建的文件\n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 数据合规问答已生成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('生成数据合规问答失败:', error);\n    new Notice('生成失败: ' + error.message);\n    return '❌ 生成失败，请检查配置';\n  }\n}\n\nexports.default = {\n  entry: entry,\n};"}, "title": "数据合规问答表单"}