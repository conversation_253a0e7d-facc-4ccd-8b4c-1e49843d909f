#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
原始知识卡片生成器
将现有笔记拆分成原子化知识点，生成知识卡片（保留所有内容，不过滤）
"""

import os
import re
import json
from pathlib import Path
from datetime import datetime
import hashlib

class KnowledgeCardGenerator:
    def __init__(self, source_dir, output_dir):
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        self.cards_dir = self.output_dir / "知识卡片"
        self.index_dir = self.output_dir / "索引"
        self.cards_dir.mkdir(exist_ok=True)
        self.index_dir.mkdir(exist_ok=True)
        
        # 存储所有卡片信息
        self.all_cards = []
        self.card_links = {}  # 存储卡片间的链接关系
        
    def clean_title(self, title):
        """清理标题，去掉序号和特殊符号"""
        # 去掉开头的序号、特殊符号
        title = re.sub(r'^[✅🏆📌⚡️💡🔍🌰❌📊🎯🚀💰⭐️\d\.\s\-\*\#]+', '', title)
        # 去掉结尾的特殊符号
        title = re.sub(r'[：:]+$', '', title)
        # 去掉多余空格
        title = title.strip()
        return title
    
    def extract_sections(self, content, file_path):
        """从内容中提取各个章节作为知识点"""
        sections = []
        lines = content.split('\n')
        current_section = None
        current_content = []
        
        for line in lines:
            # 检测标题行（以#开头或者特殊符号开头的行）
            if (line.startswith('#') or 
                re.match(r'^[✅🏆📌⚡️💡🔍🌰❌📊🎯🚀💰⭐️\d\.\s]*[\*\-]*\s*\*\*.*\*\*', line) or
                re.match(r'^[✅🏆📌⚡️💡🔍🌰❌📊🎯🚀💰⭐️\d\.\s]*.*[:：]$', line)):
                
                # 保存上一个章节
                if current_section and current_content:
                    sections.append({
                        'title': current_section,
                        'content': '\n'.join(current_content).strip(),
                        'source_file': file_path.name
                    })
                
                # 开始新章节
                current_section = self.clean_title(line.replace('#', '').replace('*', '').strip())
                current_content = [line]  # 包含标题行
                
            else:
                if current_section:
                    current_content.append(line)
        
        # 保存最后一个章节
        if current_section and current_content:
            sections.append({
                'title': current_section,
                'content': '\n'.join(current_content).strip(),
                'source_file': file_path.name
            })
        
        return sections
    
    def extract_links(self, content):
        """提取内容中的链接"""
        # 提取 [[链接]] 格式的内部链接
        internal_links = re.findall(r'\[\[([^\]]+)\]\]', content)
        
        # 提取 [文本](链接) 格式的外部链接
        external_links = re.findall(r'\[([^\]]+)\]\(([^)]+)\)', content)
        
        return {
            'internal_links': internal_links,
            'external_links': external_links
        }
    
    def generate_tags(self, title, content, source_file):
        """为知识卡片生成标签"""
        tags = []
        
        # 基于源文件生成标签
        if '法则' in source_file:
            tags.append('法则')
        if '案例' in source_file:
            tags.append('案例')
        if '股权' in source_file or '股权' in content:
            tags.append('股权')
        if '融资' in content:
            tags.append('融资')
        if '对赌' in content:
            tags.append('对赌')
        if '投资' in content:
            tags.append('投资')
        if '创始人' in content:
            tags.append('创始人')
        if '风险' in content:
            tags.append('风险')
        if '控制权' in content:
            tags.append('控制权')
        
        # 基于标题生成标签
        if any(word in title for word in ['法则', '规律', '原则']):
            tags.append('核心法则')
        if any(word in title for word in ['案例', '例子', '故事']):
            tags.append('实战案例')
        if any(word in title for word in ['风险', '陷阱', '坑']):
            tags.append('风险警示')
        
        return list(set(tags))  # 去重
    
    def create_knowledge_card(self, section, card_id):
        """创建单个知识卡片"""
        title = section['title']
        content = section['content']
        source_file = section['source_file']
        
        # 生成标签
        tags = self.generate_tags(title, content, source_file)
        
        # 提取链接
        links = self.extract_links(content)
        
        # 创建卡片元数据
        card_metadata = {
            'id': card_id,
            'title': title,
            'source_file': source_file,
            'tags': tags,
            'created_time': datetime.now().isoformat(),
            'internal_links': links['internal_links'],
            'external_links': links['external_links']
        }
        
        # 生成卡片内容
        card_content = f"""---
id: {card_id}
title: {title}
source: [[{source_file}]]
tags: {', '.join(f'#{tag}' for tag in tags)}
created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
---

# {title}

{content}

---

## 相关链接
- 来源笔记: [[{source_file}]]
"""
        
        # 添加内部链接
        if links['internal_links']:
            card_content += "\n## 相关知识点\n"
            for link in links['internal_links']:
                card_content += f"- [[{link}]]\n"
        
        return card_metadata, card_content
    
    def safe_filename(self, title):
        """生成安全的文件名"""
        # 替换不安全的字符
        safe_title = re.sub(r'[<>:"/\\|?*]', '_', title)
        # 限制长度
        if len(safe_title) > 50:
            safe_title = safe_title[:50] + '...'
        return safe_title
    
    def process_file(self, file_path):
        """处理单个笔记文件"""
        print(f"处理文件: {file_path.name}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"读取文件失败 {file_path}: {e}")
            return
        
        # 提取章节
        sections = self.extract_sections(content, file_path)
        
        if not sections:
            print(f"  未找到可提取的章节")
            return
        
        print(f"  提取到 {len(sections)} 个知识点")
        
        # 为每个章节创建知识卡片
        for i, section in enumerate(sections):
            if not section['title'] or len(section['content'].strip()) < 10:
                continue  # 跳过太短的内容
                
            # 生成唯一ID
            card_id = hashlib.md5(f"{file_path.name}_{section['title']}".encode()).hexdigest()[:8]
            
            # 创建知识卡片
            metadata, card_content = self.create_knowledge_card(section, card_id)
            
            # 保存卡片
            safe_title = self.safe_filename(section['title'])
            card_filename = f"{safe_title}.md"
            card_path = self.cards_dir / card_filename
            
            with open(card_path, 'w', encoding='utf-8') as f:
                f.write(card_content)
            
            # 记录卡片信息
            self.all_cards.append(metadata)
            print(f"    创建卡片: {safe_title}")
    
    def create_index_files(self):
        """创建索引文件"""
        # 按标签分类索引
        tags_index = {}
        for card in self.all_cards:
            for tag in card['tags']:
                if tag not in tags_index:
                    tags_index[tag] = []
                tags_index[tag].append(card)
        
        # 生成标签索引文件
        tags_content = "# 知识卡片标签索引\n\n"
        for tag, cards in sorted(tags_index.items()):
            tags_content += f"## #{tag}\n\n"
            for card in cards:
                safe_title = self.safe_filename(card['title'])
                tags_content += f"- [[{safe_title}]] - {card['title']}\n"
            tags_content += "\n"
        
        with open(self.index_dir / "标签索引.md", 'w', encoding='utf-8') as f:
            f.write(tags_content)
        
        # 生成源文件索引
        source_index = {}
        for card in self.all_cards:
            source = card['source_file']
            if source not in source_index:
                source_index[source] = []
            source_index[source].append(card)
        
        source_content = "# 知识卡片来源索引\n\n"
        for source, cards in sorted(source_index.items()):
            source_content += f"## {source}\n\n"
            for card in cards:
                safe_title = self.safe_filename(card['title'])
                source_content += f"- [[{safe_title}]] - {card['title']}\n"
            source_content += "\n"
        
        with open(self.index_dir / "来源索引.md", 'w', encoding='utf-8') as f:
            f.write(source_content)
        
        # 生成总索引
        total_content = f"""# 知识卡片总索引

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
总卡片数: {len(self.all_cards)}

## 所有知识卡片

"""
        for card in sorted(self.all_cards, key=lambda x: x['title']):
            safe_title = self.safe_filename(card['title'])
            tags_str = ', '.join(f'#{tag}' for tag in card['tags'])
            total_content += f"- [[{safe_title}]] - {card['title']} ({tags_str})\n"
        
        with open(self.index_dir / "总索引.md", 'w', encoding='utf-8') as f:
            f.write(total_content)
    
    def run(self):
        """运行主程序"""
        print(f"开始处理目录: {self.source_dir}")
        print(f"输出目录: {self.output_dir}")
        
        # 遍历所有markdown文件
        md_files = list(self.source_dir.rglob("*.md"))
        print(f"找到 {len(md_files)} 个markdown文件")
        
        for file_path in md_files:
            self.process_file(file_path)
        
        # 创建索引文件
        print("\n创建索引文件...")
        self.create_index_files()
        
        print(f"\n完成！共生成 {len(self.all_cards)} 个知识卡片")
        print(f"知识卡片保存在: {self.cards_dir}")
        print(f"索引文件保存在: {self.index_dir}")

if __name__ == "__main__":
    # 尝试找到默写本文件夹
    possible_paths = [
        r"c:\Users\<USER>\OneDrive\obsidian笔记系统\工作室\读书会\默写本",
        r"c:\Users\<USER>\OneDrive\obsidian笔记系统\默写本",
        "默写本"
    ]

    source_directory = None
    for path in possible_paths:
        test_path = Path(path)
        if test_path.exists():
            source_directory = test_path
            break

    if source_directory is None:
        print("找不到默写本文件夹")
        exit(1)

    # 在默写本下创建输出目录
    output_directory = source_directory / "原子化知识库"

    print(f"源目录: {source_directory}")
    print(f"输出目录: {output_directory}")

    # 创建生成器并运行
    generator = KnowledgeCardGenerator(source_directory, output_directory)
    generator.run()
