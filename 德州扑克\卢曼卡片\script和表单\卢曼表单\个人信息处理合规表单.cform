{"id": "personal-info-processing-form", "fields": [{"id": "business-scenario", "label": "业务场景", "type": "text", "description": "个人信息处理的业务场景"}, {"id": "general-info", "label": "一般个人信息", "type": "textarea", "rows": 2, "description": "涉及的一般个人信息类型"}, {"id": "sensitive-info", "label": "敏感个人信息", "type": "textarea", "rows": 2, "description": "涉及的敏感个人信息类型"}, {"id": "special-category", "label": "特殊类别信息", "type": "textarea", "rows": 2, "description": "生物识别、宗教信仰等特殊类别信息"}, {"id": "consent-basis", "label": "同意基础", "type": "textarea", "rows": 2, "description": "具体同意场景和撤回机制"}, {"id": "contract-basis", "label": "合同履行基础", "type": "textarea", "rows": 2, "description": "基于合同履行的处理情况"}, {"id": "legal-obligation", "label": "法定义务", "type": "textarea", "rows": 2, "description": "基于法定义务的处理情况"}, {"id": "public-interest", "label": "重大公共利益", "type": "textarea", "rows": 2, "description": "基于重大公共利益的处理情况"}, {"id": "anonymization", "label": "匿名化/去标识化", "type": "textarea", "rows": 2, "description": "匿名化或去标识化措施"}, {"id": "encryption", "label": "加密传输存储", "type": "textarea", "rows": 2, "description": "加密传输和存储措施"}, {"id": "access-control", "label": "访问控制", "type": "textarea", "rows": 2, "description": "访问控制措施"}, {"id": "data-minimization", "label": "数据最小化", "type": "textarea", "rows": 2, "description": "数据最小化原则的实施"}, {"id": "right-to-know", "label": "知情权保障", "type": "textarea", "rows": 2, "description": "知情权的保障措施"}, {"id": "access-right", "label": "访问权保障", "type": "textarea", "rows": 2, "description": "访问权的保障措施"}, {"id": "correction-deletion", "label": "更正删除权保障", "type": "textarea", "rows": 2, "description": "更正删除权的保障措施"}, {"id": "portability-right", "label": "可携带权保障", "type": "textarea", "rows": 2, "description": "可携带权的保障措施"}, {"id": "similar-activities", "label": "同类处理活动", "type": "text", "description": "同类处理活动的链接"}, {"id": "technical-standards", "label": "技术标准", "type": "text", "description": "相关技术标准的链接"}, {"id": "rights-response", "label": "权利响应流程", "type": "text", "description": "权利响应流程的链接"}], "action": {"id": "generate-personal-info-processing", "type": "runScript", "targetFolder": "", "fileName": "", "scriptSource": "inline", "code": "async function entry() {\n  const { form } = this.$context;\n  \n  const template = `### 个人信息处理：${form['business-scenario']}\n**数据分类**：\n- 一般个人信息：${form['general-info']}\n- 敏感个人信息：${form['sensitive-info']}\n- 特殊类别：${form['special-category']}\n**处理目的与法律基础**：\n- 同意：${form['consent-basis']}\n- 合同履行：${form['contract-basis']}\n- 法定义务：${form['legal-obligation']}\n- 重大公共利益：${form['public-interest']}\n**技术保护措施**：\n- 匿名化/去标识化：${form.anonymization}\n- 加密传输存储：${form.encryption}\n- 访问控制：${form['access-control']}\n- 数据最小化：${form['data-minimization']}\n**个人权利保障**：\n- 知情权：${form['right-to-know']}\n- 访问权：${form['access-right']}\n- 更正删除权：${form['correction-deletion']}\n- 可携带权：${form['portability-right']}\n→ 关联：[[${form['similar-activities']}]] [[${form['technical-standards']}]] [[${form['rights-response']}]]`;\n  \n  return template;\n}\n\nexports.default = {\n  entry: entry,\n};"}, "title": "个人信息处理合规表单"}