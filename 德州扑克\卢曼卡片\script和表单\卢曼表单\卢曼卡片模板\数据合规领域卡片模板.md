# 数据合规领域卡片模板

## 1. 合规冲突分析模板

```markdown
### 合规战场：[具体场景] 
**对阵双方**：  
- 甲方：[法规要求/监管观点] + 法条出处  
- 乙方：[业务需求/技术限制] + 实际案例  
**我的合规判断**：  
- 法律风险评估：______  
- 业务影响分析：______  
- 平衡方案：______（必须给出可执行建议）  
→ 关联：[[相关法规]] [[类似案例]]  
```

## 2. 合规认知更新模板

```markdown
### [日期] 合规认知迭代  
**被颠覆的旧理解**：______（原有合规认知）  
**新证据来源**：______（新法规/判例/监管指导）  
**认知更新路径**：______（思考过程）  
**实务调整**：我需要修正的合规实践______  
→ 关联案例：[[其他认知更新]]  
```

## 3. 每日合规思考模板

```markdown
### [日期]-合规思考：______  
**触发事件**：  
- 合规冲突：______（法规vs业务需求）  
- 相关先例：[[______]] [[______]]  
**分析过程**：  
1. 核心合规风险：______  
2. 现有解决方案不足：______  
3. 我的创新方案：______  
**落地验证**：  
- 可实施步骤：______  
- 风险控制点：______  
→ 关联：[[后续跟踪]]  
```

## 4. 数据合规问答模板

```markdown
### Q：在[具体业务场景]中，如何平衡[数据利用需求]与[隐私保护要求]？  

A：  
- **法规依据**：GDPR/PIPL第X条规定______（1句话）  
- **我的解读**：用技术/管理手段重构  
- **操作指南**：具体到可执行的合规checklist  

→ 关联：[[相关法条解读]] [[类似场景处理]]  
! 风险：当前方案可能忽略______（监管趋势变化）  
```

## 5. 数据合规体系论证模板

```markdown
### [新技术/新模式]对数据合规的潜在冲击  
- **传统合规局限**：______（现有框架不足）  
- **创新机遇**：通过[[技术方案]]←→[[法律解释]]←→[[业务实践]]的协同  
- **实证案例**：______（成功/失败案例分析）  
- **风险预警**：______（潜在合规陷阱）  
→ 实验：[[合规方案验证]]  
```

## 6. 合规实效检验模板

```markdown
### 数据合规知识资产审计  
**检验维度**：
1. 这个合规认知帮我避免了哪些具体风险？
2. 这个方案被多少个实际项目采用？
3. 如果删除这个知识点，我的合规体系会有漏洞吗？

**价值评估**：
- 高价值：直接指导业务决策，避免合规风险
- 中价值：提供参考思路，需要进一步验证  
- 低价值：纯理论堆砌，无实际指导意义
```

## 7. 合规问题精准切割模板

```markdown
### 合规问题手术刀：[原模糊合规问题]
**手术前**：______（如"如何做好数据合规？"）
**切除操作**：
1. 删除泛化词汇（"做好"、"合规"）
2. 明确业务场景和数据类型
3. 添加可量化标准
**手术后**：______（如"电商平台收集用户浏览行为数据用于个性化推荐，需要满足哪些PIPL第13条的告知要求？"）
**验证标准**：法务同事能否直接给出操作建议
```

## 8. 数据合规强制约束模板

```markdown
## 合规核心问题:: [必须包含具体法条+业务场景]  
## 风险等级:: [高/中/低] + 具体后果  
## 解决方案:: ≤3个可执行步骤+1个实际案例  
## 关联义务:: 至少链接到1个相关法规条文  
## 实践反思:: 我在类似项目中的应用/踩坑经历
```

## 9. 监管专家审查模板

```markdown
### 监管专家review记录
**审查对象**：______（我最自信的合规方案）
**专家身份**：______（监管机构/资深法务）
**专业反馈**：______
**合规盲点**：______（被指出的风险点）
**方案迭代**：______
→ 关联：[[其他专家意见]] [[监管趋势]]
```

## 10. 合规方案生存测试模板

```markdown
### 合规方案实战测试：[场景]
**禁用资源**：不能查阅任何法规条文
**仅用资源**：自己的合规笔记系统
**测试要求**：30分钟内给出完整合规建议
**测试结果**：
- 能给出可执行方案 → 知识体系基本可用
- 只能说原则性建议 → 知识体系是"合规花瓶"
**改进计划**：______
```

## 11. 数据合规实战问答模板

```markdown
### Q：为什么我的"数据合规检查清单"总是流于形式？  
A：  
- **法规说**：应建立数据处理活动记录（PIPL第52条）  
- **我做**：实际只是走过场，缺少动态更新机制  
- **创新方案**：  
  1. 绑定到产品发布流程（不过合规review不能上线）  
  2. 设置季度合规风险扫描（自动化工具+人工review）  
  3. 建立合规KPI（违规事件数量、整改及时率）  
→ 关联：[[合规流程优化]] [[自动化工具]]  
! 已验证：上季度合规检查覆盖率从40%→90%  
```

## 12. 数据合规知识MVP模板

```markdown
### 合规知识产品：[微创新点]
**原料法条**：[[GDPR第X条]] + [[PIPL第Y条]] + [[实际案例Z]]
**组合假设**：______（跨法域的合规创新思路）
**最小验证**：______（内部分享/行业交流）
**同行反馈**：______
**迭代方向**：______
**ROI计算**：
- 实际项目应用：______
- 避免合规风险：______
- 可标准化推广：______
```

## 13. 合规知识炼金术模板

```markdown
### 合规炼金操作：[原始法规条文/案例]
**原料等级**：
- #待消化（纯法条摘录）
- #半熟品（含业务场景分析）  
- #精品（可直接指导实践）

**炼金过程**：
- **冲突挖掘**：这与其他法规/业务需求矛盾在哪？
- **实践案例**：我处理过的类似合规问题______
- **创新输出**：______（独特的合规解决思路）

**成品检验**：能否直接用于客户咨询/内部决策？
```

## 14. 数据合规5W1H拆解模板

```markdown
### 合规问题拆解：[原问题]
**Who**：涉及哪些数据主体和处理者？
**What**：具体涉及哪类个人信息？
**When**：数据处理的时间节点和期限？
**Where**：数据处理和存储的地理位置？
**Why**：数据处理的合法性基础？
**How**：具体的技术和管理措施？

**重构后问题**：______
**法规适用性**：能否直接对应具体法条？
```

## 15. 数据合规著作权检验模板

```markdown
### 合规方案原创性检验
**核心问题**："这个合规方案除了我，还有哪个法务能提出？"

**评估标准**：
- 通用法务都能想到 → 还在条文搬运层面
- 只有结合我的XX行业经验才能设计 → 真正的专业价值

**价值提升方向**：
- 记录实际合规项目中的真实困境
- 质疑现有合规框架的局限性
- 定期做"合规认知拆迁"（推翻过时理解）
```

## 16. 跨境数据传输专项模板

```markdown
### 跨境传输合规分析：[具体场景]
**传输路径**：[数据源] → [中转节点] → [目标地]
**适用法规**：
- 中国：PIPL第X条、数据安全法第Y条
- 目标国：GDPR/其他当地法规
- 国际协议：SCC/BCR/认证机制
**合规路径选择**：
1. 标准合同条款：______
2. 认证机制：______  
3. 监管备案：______
**技术保障措施**：______
**风险控制矩阵**：______
→ 关联：[[类似传输场景]] [[监管动态]]
```

## 17. 个人信息处理合规模板

```markdown
### 个人信息处理：[业务场景]
**数据分类**：
- 一般个人信息：______
- 敏感个人信息：______
- 特殊类别：______（生物识别、宗教信仰等）
**处理目的与法律基础**：
- 同意：______（具体同意场景和撤回机制）
- 合同履行：______
- 法定义务：______
- 重大公共利益：______
**技术保护措施**：
- 匿名化/去标识化：______
- 加密传输存储：______
- 访问控制：______
- 数据最小化：______
**个人权利保障**：
- 知情权：______
- 访问权：______
- 更正删除权：______
- 可携带权：______
→ 关联：[[同类处理活动]] [[技术标准]] [[权利响应流程]]
```