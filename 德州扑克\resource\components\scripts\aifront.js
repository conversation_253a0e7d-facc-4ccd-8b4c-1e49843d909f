﻿async function aifront(token, propertyName, modelType) {
  const model = modelType || "GLM-4-Flash"; // 智谱清言模型，GLM-4-Flash 是一个免费模型，其他模型需要付�?
  if (!propertyName || !token) {
    new Notice("请设置密钥或属性名");
    return;
  }
  const { currentFile } = this;
  const file = currentFile;
  const fileContent = await app.vault.cachedRead(file);
  const title = file.basename;
  
  // 获取文件的frontmatter属�?  const frontmatter = app.metadataCache.getFileCache(file)?.frontmatter || {};
  const tags = frontmatter.tags || [];
  const characters = frontmatter.characters || frontmatter.人物 || [];
  const location = frontmatter.location || frontmatter.地点 || "";
  const theme = frontmatter.theme || frontmatter.主题 || "";
  const mood = frontmatter.mood || frontmatter.情绪 || "";
  const genre = frontmatter.genre || frontmatter.类型 || "悬疑";
  
  // 构建属性信息字符串
  let attributeInfo = "";
  if (tags.length > 0) attributeInfo += `标签: ${tags.join(", ")}\n`;
  if (characters.length > 0) attributeInfo += `人物: ${characters.join(", ")}\n`;
  if (location) attributeInfo += `地点: ${location}\n`;
  if (theme) attributeInfo += `主题: ${theme}\n`;
  if (mood) attributeInfo += `情绪: ${mood}\n`;
  if (genre) attributeInfo += `类型: ${genre}\n`;

  // 提示�?  const prompt = `
作为一个专业的小说家，请根据以下笔记信息创作一个精彩的小说段落�?
笔记标题�?{title}

笔记属性：
${attributeInfo}

笔记内容�?${fileContent || ""}

创作要求�?1. 根据笔记的标签、人物、地点等属性信息，创作一个引人入胜的小说段落
2. 段落应该包含关键人物和重要情�?3. 情节描述要具体生动，符合指定的类型和情绪
4. 段落长度控制�?00-500字之�?5. 语言要富有文学性和感染�?6. 如果有具体的人物和地点，请在段落中自然融�?
请直接输出小说段落内容，不需要其他说明�?`;

  var options = {
    method: "POST",
    url: "https://open.bigmodel.cn/api/paas/v4/chat/completions",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      model: model,
      messages: [
        {
          role: "user",
          content: prompt,
        },
      ],
    }),
  };
  
  const response = await obsidian.requestUrl(options);
  const result = response.json;
  
  if (result.choices.length === 0) {
    new Notice("没有内容可输�?);
    return;
  }

  const content = result.choices[0].message?.content;
  if (!content) {
    new Notice("没有内容可输�?);
    return;
  }
  
  // 生成段落标题（关键情节概括）
  const titlePrompt = `
请为以下小说段落生成一个简洁的标题，格式要求："角色+动作+关键�?，类�?CEO模糊条款获数�?艾伦调查废弃工厂交易"这样的格式�?
段落内容�?${content}

要求�?1. 标题要概括段落的核心情节
2. 包含主要角色和关键动�?3. 长度控制�?5字以�?4. 只输出标题，不要其他内容
`;

  const titleOptions = {
    method: "POST",
    url: "https://open.bigmodel.cn/api/paas/v4/chat/completions",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      model: model,
      messages: [
        {
          role: "user",
          content: titlePrompt,
        },
      ],
    }),
  };
  
  const titleResponse = await obsidian.requestUrl(titleOptions);
  const titleResult = titleResponse.json;
  const paragraphTitle = titleResult.choices?.[0]?.message?.content || "未命名情�?;
  
  // 将情节概括标题写�?段落"属性，段落内容写入正文
  app.fileManager.processFrontMatter(file, (frontmatter) => {
    frontmatter["段落"] = paragraphTitle.trim();
  });
  
  // 将段落内容追加到文件正文
  const currentContent = await app.vault.read(file);
  const newContent = currentContent + "\n\n" + content;
  await app.vault.modify(file, newContent);
  
  new Notice(`小说段落已生成：${paragraphTitle.trim()}`);
}

// �?Components 插件中使用示�?// aifront("a6ff22a8cdac4123a9d9a8ea3779009c.aOZZo3lqUQxXgZkN", "段落");

exports.default = {
  entry: aifront,
  name: "aifront",
  description: `通过智谱清言�?API 根据笔记属性生成小说段�?默认使用的是免费模型 GLM-4-Flash)

  ==请先�?https://open.bigmodel.cn/ 注册并获�?API 密钥�?=

  使用方法

\`aifront('你的密钥', '属性名')\`

  也可以指定其他付费模型，模型类型可以�?https://open.bigmodel.cn/console/modelcenter/square 查看

\`aifront('你的密钥', '属性名', 'glm-4-plus')\`

  功能说明�?  - 根据笔记的标签、人物、地点等属性信息生成小说段�?  - 自动生成段落标题（关键情节概括）
  - 支持多种小说类型和情绪设�?  - 生成的段落会包含关键人物和重要情�?  
  支持的笔记属性：
  - tags/标签：影响故事风�?  - characters/人物：段落中的主要角�?  - location/地点：故事发生地
  - theme/主题：故事主�?  - mood/情绪：段落情绪基�?  - genre/类型：小说类型（默认：悬疑）
  `,
};
