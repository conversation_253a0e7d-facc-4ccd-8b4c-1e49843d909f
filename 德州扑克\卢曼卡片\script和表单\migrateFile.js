﻿async function migrateFile(){
    const file = this.currentFile;
    const fileParentFolder = file.parent.path;

    if (file instanceof obsidian.TFile) {
        try {
            const parent = await getProperty(file, 'parent');
            const branch = await getProperty(file, 'branch');
            const isArchived = await getProperty(file, 'Archived');
            const {
                oldParent,
                oldBranch,
                oldIsArchived,
                subfolder
            } = await parseFileFolder(fileParentFolder);
            const targetFolder = await getTargetFolder({ parent, branch, isArchived, subfolder})
            await moveFileTo(file, targetFolder);
            
            new obsidian.Notice('移动成功�?, 2000);
        } catch(e){            
            new obsidian.Notice(e);
            throw Error(e);
        }
    } else {
        new obsidian.Notice('Obsidian中找不到该文�?);
        return;
    }
}

async function moveFileTo(file, targetFolder){
    if (!app.vault.getFolderByPath(targetFolder)){
        await app.vault.createFolder(targetFolder);
    }

    const targetFilePath = targetFolder + '/' + file.name;

    await app.fileManager.renameFile(file, targetFilePath);
}

async function parseFileFolder(fileFolder) {
    // 分割路径
    const parts = fileFolder.split('/');
    const first = parts[0] || "";
    const second = parts[1] || "";
    const third = parts[2] || "";
    
    // 1. 判断是否是归�?
    const isArchived = first === "04-Archives";
    
    // 2. 确定parent
    let parent = "Project"; // 默认�?
    const parentKeywords = {
        "01-Projects": "Project",
        "02-Areas": "Area",
        "03-Resources": "Resource"
    };
    
    // 检查第一个或第二个路径是否包含parent关键�?
    if (parentKeywords[first]) {
        parent = parentKeywords[first];
    } else if (parentKeywords[second]) {
        parent = parentKeywords[second];
    }
    
    // 3. 确定branch
    let branch = "";
    const branchKeywords = {
        "001-学习": "学习",
        "002-生活": "生活",
        "003-项目": "项目"
    };
    
    // 检查第二个或第三个路径是否包含branch关键�?
    if (branchKeywords[second]) {
        branch = branchKeywords[second];
    } else if (branchKeywords[third]) {
        branch = branchKeywords[third];
    }
    
    // 4. 确定subfolder
    let subfolder = "";
    
    // 如果有branch，则从branch所在位置之后开始计算subfolder
    if (branch) {
        const branchIndex = parts.findIndex(part => 
            part === "001-学习" || part === "002-生活" || part === "003-项目"
        );
        if (branchIndex !== -1 && branchIndex + 1 < parts.length) {
            subfolder = parts.slice(branchIndex + 1).join('/');
        }
    } 
    // 如果没有branch，则从第二个路径之后开始计算subfolder（如果第一个路径不是parent�?
    else {
        const parentIndex = parts.findIndex(part => 
            part === "01-Projects" || part === "02-Areas" || part === "03-Resources"
        );
        if (parentIndex !== -1 && parentIndex + 1 < parts.length) {
            subfolder = parts.slice(parentIndex + 1).join('/');
        } else if (parts.length > 1) {
            subfolder = parts.slice(1).join('/');
        }
    }
    
    return {
        parent,
        branch,
        isArchived,
        subfolder
    };
}

async function getTargetFolder({ parent, branch, isArchived, subfolder }) {
    // 1. 确定第一个路径部�?
    let firstPart = "";
    if (isArchived) {
        firstPart = "04-Archives/";
        const parentToPath = {
            "Project": "01-Archived Projects",
            "Area": "02-Archived Areas",
            "Resource": "03-Archived Resources"
        };
        firstPart += parentToPath[parent] || "01-Archived Projects";
    } else {
        const parentToPath = {
            "Project": "01-Projects",
            "Area": "02-Areas",
            "Resource": "03-Resources"
        };
        firstPart = parentToPath[parent] || "01-Projects";
    }
    

    // 2. 确定第二个路径部分（如果有branch�?
    let secondPart = "";
    const branchToPath = {
        "学习": "001-学习",
        "生活": "002-生活",
        "项目": "003-项目"
    };
    if (branch && branchToPath[branch]) {
        secondPart = branchToPath[branch];
    }

    // 3. 构建路径数组
    const pathParts = [firstPart];
    if (secondPart) {
        pathParts.push(secondPart);
    }
    if (subfolder) {
        pathParts.push(subfolder);
    }

    // 4. 合并为完整路�?
    return pathParts.join('/');
}

async function getProperty(file, propertyName){
    return app.metadataCache.getFileCache(file)?.frontmatter?.[propertyName];
}

exports.default = {
    name: 'migrateFile',
    description: '根据文件�?parent 属�? branch 属性和 Archived 属�? 移动文件到对应PARA文件�?保留原文件所在subfolder)',
    entry: migrateFile,
}
