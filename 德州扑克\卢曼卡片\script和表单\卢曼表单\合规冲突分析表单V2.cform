{"id": "compliance-conflict-analysis-form-v2", "fields": [{"id": "scenario", "label": "具体场景", "type": "text", "description": "描述具体的合规冲突场景"}, {"id": "partya", "label": "甲方（法规要求/监管观点）", "type": "textarea", "rows": 3, "description": "法规要求或监管观点，包含法条出处"}, {"id": "partyb", "label": "乙方（业务需求/技术限制）", "type": "textarea", "rows": 3, "description": "业务需求或技术限制，包含实际案例"}, {"id": "risk", "label": "法律风险评估", "type": "select", "options": [{"id": "high-risk", "label": "高风险", "value": "高风险"}, {"id": "medium-risk", "label": "中风险", "value": "中风险"}, {"id": "low-risk", "label": "低风险", "value": "低风险"}], "description": "评估法律风险等级"}, {"id": "impact", "label": "业务影响分析", "type": "textarea", "rows": 3, "description": "分析对业务的具体影响"}, {"id": "solution", "label": "平衡方案", "type": "textarea", "rows": 5, "description": "提供可执行的平衡建议"}, {"id": "regulations", "label": "相关法规", "type": "text", "description": "相关法规链接或标识"}, {"id": "cases", "label": "类似案例", "type": "text", "description": "类似案例链接或标识"}], "action": {"id": "generate-compliance-conflict-analysis-v2", "type": "runScript", "targetFolder": "", "fileName": "", "scriptSource": "inline", "code": "async function entry() {\n  const { form, app, requestUrl, Notice } = this.$context;\n  \n  try {\n    // 先生成基础模板内容\n    const baseTemplate = `### 合规战场：${form.scenario}\n**对阵双方**：\n- 甲方：${form.partya}\n- 乙方：${form.partyb}\n**我的合规判断**：\n- 法律风险评估：${form.risk}\n- 业务影响分析：${form.impact}\n- 平衡方案：${form.solution}\n→ 关联：[[${form.regulations}]] [[${form.cases}]]`;\n\n    // AI认知碰撞和版权化改造\n    let aiEnhancedContent = '';\n    const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec';\n    \n    const enhancedPrompt = `你是一位资深的数据合规专家，现在需要对以下合规冲突分析进行深度的认知碰撞和版权化改造。\n\n原始分析：\n${baseTemplate}\n\n请你作为专业的合规顾问，与这个分析进行认知碰撞，提供你独特的专业视角：\n\n1. **认知挑战**：对原有分析提出质疑或补充，是否存在遗漏的风险点？\n2. **深度洞察**：基于你的专业经验，这个冲突的本质是什么？\n3. **创新观点**：结合监管趋势，提出更具前瞻性的解决思路\n4. **实战经验**：分享类似冲突的实际处理经验和踩坑教训\n5. **版权化方案**：形成具有独特价值的专业解决方案\n\n要求：\n- 不要简单重复原内容，要有认知碰撞和挑战\n- 体现专业深度和独特视角\n- 提供具体可操作的改进建议\n- 形成有版权价值的专业内容\n\n请以资深合规专家的身份，对这个冲突分析进行深度改造和升华。`;\n    \n    try {\n      const aiResponse = await requestUrl({\n        url: 'https://api.deepseek.com/v1/chat/completions',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: \"deepseek-chat\",\n          messages: [{ role: \"user\", content: enhancedPrompt }],\n          temperature: 0.8,\n          max_tokens: 2000\n        })\n      });\n      \n      if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n        aiEnhancedContent = aiResponse.json.choices[0].message.content;\n      }\n    } catch (e) {\n      aiEnhancedContent = '(AI认知碰撞暂时不可用，请手动补充专业洞察)';\n    }\n\n    // 生成最终内容\n    const template = `${baseTemplate}\n\n---\n\n## 🧠 专业认知碰撞与版权化改造\n\n${aiEnhancedContent}\n\n---\n\n## 📝 实战应用记录\n<!-- 记录这个方案在实际项目中的应用效果 -->\n\n\n## 🔄 方案迭代\n<!-- 记录方案的进一步优化和改进 -->\n\n\n## 🏷️ 标签\n#合规冲突分析 #${form.risk} #认知碰撞 #版权化方案 #数据合规 #${new Date().toISOString().split('T')[0]}\n\n---\n*生成时间：${new Date().toLocaleString()} | AI认知碰撞：DeepSeek | 版权化改造完成*`;\n    \n    // 创建文件\n    const today = new Date();\n    const dateStr = today.toISOString().split('T')[0];\n    const timeStr = today.toTimeString().split(' ')[0].substring(0, 5).replace(':', '');\n    const fileName = `合规冲突分析-${form.scenario}-${dateStr}-${timeStr}.md`;\n    const filePath = `工作室/肌肉/生成笔记/合规冲突分析/${fileName}`;\n    \n    // 检查并创建目录\n    const folderPath = '工作室/肌肉/生成笔记/合规冲突分析';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    // 创建文件\n    const file = await app.vault.create(filePath, template);\n    new Notice(`合规冲突分析已创建: ${fileName}`);\n    \n    // 打开新创建的文件\n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 合规冲突分析已生成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('生成合规冲突分析失败:', error);\n    new Notice('生成失败: ' + error.message);\n    return '❌ 生成失败，请检查配置';\n  }\n}\n\nexports.default = {\n  entry: entry,\n};"}, "title": "合规冲突分析表单V2"}