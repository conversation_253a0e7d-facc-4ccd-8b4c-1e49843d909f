{"id": "cross-border-data-transfer-form", "fields": [{"id": "scenario", "label": "具体场景", "type": "text", "description": "跨境数据传输的具体场景"}, {"id": "data-source", "label": "数据源", "type": "text", "description": "数据的来源地"}, {"id": "transit-node", "label": "中转节点", "type": "text", "description": "数据传输的中转节点"}, {"id": "destination", "label": "目标地", "type": "text", "description": "数据传输的目标地"}, {"id": "china-regulations", "label": "中国适用法规", "type": "textarea", "rows": 2, "description": "PIPL、数据安全法等相关条文"}, {"id": "destination-regulations", "label": "目标国法规", "type": "textarea", "rows": 2, "description": "GDPR或其他当地法规"}, {"id": "international-agreements", "label": "国际协议", "type": "textarea", "rows": 2, "description": "SCC/BCR/认证机制等"}, {"id": "scc-path", "label": "标准合同条款", "type": "textarea", "rows": 2, "description": "标准合同条款的合规路径"}, {"id": "certification-mechanism", "label": "认证机制", "type": "textarea", "rows": 2, "description": "相关认证机制"}, {"id": "regulatory-filing", "label": "监管备案", "type": "textarea", "rows": 2, "description": "监管备案要求"}, {"id": "technical-measures", "label": "技术保障措施", "type": "textarea", "rows": 3, "description": "技术层面的保障措施"}, {"id": "risk-control-matrix", "label": "风险控制矩阵", "type": "textarea", "rows": 3, "description": "风险控制的具体措施"}, {"id": "similar-transfer", "label": "类似传输场景", "type": "text", "description": "类似传输场景的链接"}, {"id": "regulatory-dynamics", "label": "监管动态", "type": "text", "description": "相关监管动态的链接"}], "action": {"id": "generate-cross-border-transfer", "type": "runScript", "targetFolder": "", "fileName": "", "scriptSource": "inline", "code": "async function entry() {\n  const { form } = this.$context;\n  \n  const template = `### 跨境传输合规分析：${form.scenario}\n**传输路径**：${form['data-source']} → ${form['transit-node']} → ${form.destination}\n**适用法规**：\n- 中国：${form['china-regulations']}\n- 目标国：${form['destination-regulations']}\n- 国际协议：${form['international-agreements']}\n**合规路径选择**：\n1. 标准合同条款：${form['scc-path']}\n2. 认证机制：${form['certification-mechanism']}\n3. 监管备案：${form['regulatory-filing']}\n**技术保障措施**：${form['technical-measures']}\n**风险控制矩阵**：${form['risk-control-matrix']}\n→ 关联：[[${form['similar-transfer']}]] [[${form['regulatory-dynamics']}]]`;\n  \n  return template;\n}\n\nexports.default = {\n  entry: entry,\n};"}, "title": "跨境数据传输表单"}