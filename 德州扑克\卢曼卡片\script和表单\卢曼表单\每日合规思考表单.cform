{"id": "daily-compliance-thinking-form", "fields": [{"id": "date", "label": "日期", "type": "text", "defaultValue": "{{date:YYYY-MM-DD}}", "description": "思考日期"}, {"id": "thinking-topic", "label": "合规思考主题", "type": "text", "description": "今日合规思考的主题"}, {"id": "trigger-event", "label": "触发事件", "type": "textarea", "rows": 2, "description": "引发思考的具体事件"}, {"id": "compliance-conflict", "label": "合规冲突", "type": "textarea", "rows": 2, "description": "法规vs业务需求的冲突点"}, {"id": "related-precedent-1", "label": "相关先例1", "type": "text", "description": "第一个相关先例的链接"}, {"id": "related-precedent-2", "label": "相关先例2", "type": "text", "description": "第二个相关先例的链接"}, {"id": "core-risk", "label": "核心合规风险", "type": "textarea", "rows": 2, "description": "识别的核心合规风险"}, {"id": "solution-gap", "label": "现有解决方案不足", "type": "textarea", "rows": 2, "description": "现有方案的不足之处"}, {"id": "innovative-solution", "label": "我的创新方案", "type": "textarea", "rows": 3, "description": "提出的创新解决方案"}, {"id": "implementation-steps", "label": "可实施步骤", "type": "textarea", "rows": 3, "description": "具体的实施步骤"}, {"id": "risk-control", "label": "风险控制点", "type": "textarea", "rows": 2, "description": "需要控制的风险点"}, {"id": "follow-up", "label": "后续跟踪", "type": "text", "description": "后续跟踪事项的链接"}], "action": {"id": "generate-daily-compliance-thinking", "type": "runScript", "targetFolder": "", "fileName": "", "scriptSource": "inline", "code": "async function entry() {\n  const { form } = this.$context;\n  \n  const template = `### ${form.date}-合规思考：${form['thinking-topic']}\n**触发事件**：\n- 合规冲突：${form['compliance-conflict']}\n- 相关先例：[[${form['related-precedent-1']}]] [[${form['related-precedent-2']}]]\n**分析过程**：\n1. 核心合规风险：${form['core-risk']}\n2. 现有解决方案不足：${form['solution-gap']}\n3. 我的创新方案：${form['innovative-solution']}\n**落地验证**：\n- 可实施步骤：${form['implementation-steps']}\n- 风险控制点：${form['risk-control']}\n→ 关联：[[${form['follow-up']}]]`;\n  \n  return template;\n}\n\nexports.default = {\n  entry: entry,\n};"}, "title": "每日合规思考表单"}