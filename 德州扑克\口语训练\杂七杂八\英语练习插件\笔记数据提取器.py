#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
英语练习插件 - 笔记数据提取器
基于您的笔记库自动提取练习数据
"""

import os
import re
import json
import yaml
from pathlib import Path
from typing import Dict, List, Any

class NotesDataExtractor:
    def __init__(self, notes_root: str = "."):
        self.notes_root = Path(notes_root)
        self.extracted_data = {
            "scenarios": {},
            "templates": [],
            "keywords": {},
            "variables": {},
            "error_patterns": [],
            "difficulty_levels": {}
        }
    
    def extract_all_data(self):
        """提取所有笔记数据"""
        print("🚀 开始提取笔记数据...")
        
        # 提取场景短语
        self.extract_scenario_phrases()
        
        # 提取模板结构
        self.extract_templates()
        
        # 提取变量数据
        self.extract_variables()
        
        # 提取错题本数据
        self.extract_error_patterns()
        
        # 提取开会场景
        self.extract_meeting_scenarios()
        
        # 提取合规场景
        self.extract_compliance_scenarios()
        
        print("✅ 数据提取完成！")
        return self.extracted_data
    
    def extract_scenario_phrases(self):
        """提取场景短语映射表"""
        mapping_file = self.notes_root / "闪卡" / "短语" / "场景短语映射表.md"
        if mapping_file.exists():
            with open(mapping_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取表格数据
            table_pattern = r'\| (.+?) \| (.+?) \| (.+?) \|'
            matches = re.findall(table_pattern, content)
            
            for match in matches:
                if match[0] != "情景类型" and not match[0].startswith("---"):
                    scenario_type = match[0].strip().replace("**", "")
                    trigger_phrases = match[1].strip()
                    output_template = match[2].strip()
                    
                    self.extracted_data["scenarios"][scenario_type] = {
                        "trigger_phrases": trigger_phrases.split(" + "),
                        "template": output_template,
                        "category": "场景短语"
                    }
    
    def extract_templates(self):
        """提取模板结构"""
        # 从错题本提取模板
        error_dir = self.notes_root / "错题本"
        if error_dir.exists():
            for file_path in error_dir.glob("*.md"):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 提取YAML front matter
                    yaml_match = re.search(r'^---\s*\n(.*?)\n---', content, re.DOTALL)
                    if yaml_match:
                        try:
                            metadata = yaml.safe_load(yaml_match.group(1))
                            if 'correct_expression' in metadata:
                                template_data = {
                                    "template": metadata['correct_expression'],
                                    "scenario": metadata.get('usage_scenario', ''),
                                    "difficulty": metadata.get('difficulty', '中级'),
                                    "frequency": metadata.get('frequency', '中频'),
                                    "tags": metadata.get('tags', []),
                                    "source_file": file_path.name
                                }
                                
                                # 提取模板结构
                                if '模板结构' in metadata:
                                    template_data["structure"] = metadata['模板结构']
                                
                                self.extracted_data["templates"].append(template_data)
                        except yaml.YAMLError:
                            continue
                except Exception as e:
                    print(f"处理文件 {file_path} 时出错: {e}")
    
    def extract_variables(self):
        """提取变量数据"""
        variables_dir = self.notes_root / "闪卡" / "变量"
        if variables_dir.exists():
            for file_path in variables_dir.glob("*.md"):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 提取变量类型
                    var_type = file_path.stem.split('_')[0]
                    
                    # 提取表格数据
                    table_pattern = r'\| (.+?) \| (.+?) \| (.+?) \| (.+?) \|'
                    matches = re.findall(table_pattern, content)
                    
                    variables = []
                    for match in matches:
                        if not match[0].startswith("---") and match[0] != "中文名称":
                            variables.append({
                                "chinese": match[0].strip(),
                                "english": match[1].strip(),
                                "feature": match[2].strip(),
                                "example": match[3].strip()
                            })
                    
                    if variables:
                        self.extracted_data["variables"][var_type] = variables
                        
                except Exception as e:
                    print(f"处理变量文件 {file_path} 时出错: {e}")
    
    def extract_error_patterns(self):
        """提取错误模式"""
        error_dir = self.notes_root / "错题本"
        if error_dir.exists():
            for file_path in error_dir.glob("*.md"):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 提取YAML front matter
                    yaml_match = re.search(r'^---\s*\n(.*?)\n---', content, re.DOTALL)
                    if yaml_match:
                        try:
                            metadata = yaml.safe_load(yaml_match.group(1))
                            if 'common_errors' in metadata:
                                error_pattern = {
                                    "correct": metadata.get('correct_expression', ''),
                                    "incorrect": metadata['common_errors'],
                                    "error_type": metadata.get('error_type', ''),
                                    "scenario": metadata.get('usage_scenario', ''),
                                    "difficulty": metadata.get('difficulty', '中级')
                                }
                                self.extracted_data["error_patterns"].append(error_pattern)
                        except yaml.YAMLError:
                            continue
                except Exception as e:
                    print(f"处理错误模式文件 {file_path} 时出错: {e}")
    
    def extract_meeting_scenarios(self):
        """提取开会场景"""
        meeting_dir = self.notes_root / "闪卡" / "开会"
        if meeting_dir.exists():
            meeting_phrases = []
            for file_path in meeting_dir.glob("*.md"):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 提取标题和内容
                    title = file_path.stem
                    
                    # 查找英语表达
                    english_patterns = [
                        r'"([^"]+)"',  # 双引号内容
                        r"'([^']+)'",  # 单引号内容
                        r'- "([^"]+)"',  # 列表项
                        r'\*\*([^*]+)\*\*'  # 粗体内容
                    ]
                    
                    for pattern in english_patterns:
                        matches = re.findall(pattern, content)
                        for match in matches:
                            if len(match) > 5 and any(c.isalpha() for c in match):
                                meeting_phrases.append({
                                    "phrase": match,
                                    "source": title,
                                    "category": "开会"
                                })
                
                except Exception as e:
                    print(f"处理开会文件 {file_path} 时出错: {e}")
            
            self.extracted_data["scenarios"]["meeting"] = {
                "phrases": meeting_phrases,
                "category": "开会场景"
            }
    
    def extract_compliance_scenarios(self):
        """提取合规场景"""
        compliance_dir = self.notes_root / "闪卡" / "合规"
        if compliance_dir.exists():
            compliance_phrases = []
            for file_path in compliance_dir.glob("*.md"):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    title = file_path.stem
                    
                    # 提取英语表达
                    english_matches = re.findall(r'[A-Z][a-zA-Z\s,.-]+[.!?]', content)
                    for match in english_matches:
                        if len(match) > 10 and ' ' in match:
                            compliance_phrases.append({
                                "phrase": match.strip(),
                                "source": title,
                                "category": "合规"
                            })
                
                except Exception as e:
                    print(f"处理合规文件 {file_path} 时出错: {e}")
            
            self.extracted_data["scenarios"]["compliance"] = {
                "phrases": compliance_phrases,
                "category": "合规场景"
            }
    
    def save_to_json(self, output_file: str = "练习数据.json"):
        """保存数据到JSON文件"""
        output_path = self.notes_root / output_file
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.extracted_data, f, ensure_ascii=False, indent=2)
        print(f"📁 数据已保存到: {output_path}")
    
    def generate_practice_config(self):
        """生成练习配置"""
        config = {
            "practice_modes": {
                "template_fill": {
                    "name": "模板填空",
                    "description": "根据模板结构填写完整句子",
                    "data_source": "templates"
                },
                "keyword_sentence": {
                    "name": "关键词造句",
                    "description": "使用给定关键词构造句子",
                    "data_source": "variables"
                },
                "scenario_dialogue": {
                    "name": "情景对话",
                    "description": "模拟真实场景进行对话练习",
                    "data_source": "scenarios"
                },
                "error_correction": {
                    "name": "错误纠正",
                    "description": "识别并纠正常见错误",
                    "data_source": "error_patterns"
                }
            },
            "difficulty_levels": {
                "基础": {
                    "score_threshold": 60,
                    "template_complexity": "simple",
                    "variable_count": 3,
                    "keyword_count": 3,
                    "description": "使用基础词汇和简单句式"
                },
                "中级": {
                    "score_threshold": 75,
                    "template_complexity": "medium",
                    "variable_count": 5,
                    "keyword_count": 5,
                    "description": "使用中等复杂度的专业表达"
                },
                "高级": {
                    "score_threshold": 90,
                    "template_complexity": "complex",
                    "variable_count": 7,
                    "keyword_count": 7,
                    "description": "使用高级专业术语和复杂句式"
                }
            },
            "scoring_criteria": {
                "professional_terms": 20,
                "grammar_accuracy": 25,
                "template_compliance": 25,
                "context_appropriateness": 20,
                "creativity": 10
            },
            "optional_keywords": {
                "连接词": ["pursuant to", "in accordance with", "therefore", "furthermore", "moreover", "consequently", "nevertheless"],
                "时间表达": ["within", "by", "no later than", "immediately", "without undue delay", "annually", "quarterly"],
                "义务动词": ["shall", "must", "implement", "ensure", "establish", "maintain", "monitor"],
                "技术术语": ["encryption", "authentication", "authorization", "pseudonymization", "anonymization", "tokenization"],
                "法律术语": ["adequacy decisions", "SCCs", "BCRs", "legitimate interests", "explicit consent", "supervisory authority"],
                "合规术语": ["compliance", "regulatory", "mandatory", "assessment", "audit", "documentation", "remediation"]
            }
        }

        config_path = self.notes_root / "练习配置.json"
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        print(f"⚙️ 配置已保存到: {config_path}")

        return config

    def generate_javascript_data(self):
        """生成JavaScript数据文件"""
        js_data = f"""
// 自动生成的笔记数据 - 请勿手动编辑
// 生成时间: {json.dumps(str(Path(__file__).stat().st_mtime), ensure_ascii=False)}

const extractedNotesData = {json.dumps(self.extracted_data, ensure_ascii=False, indent=2)};

// 更新HTML中的数据
if (typeof window !== 'undefined') {{
    window.notesData = extractedNotesData;
    console.log('📚 笔记数据已加载:', Object.keys(extractedNotesData));
}}
"""

        js_path = self.notes_root / "笔记数据.js"
        with open(js_path, 'w', encoding='utf-8') as f:
            f.write(js_data)
        print(f"📄 JavaScript数据已保存到: {js_path}")

        return js_path

def main():
    """主函数"""
    print("🎯 英语练习插件 - 笔记数据提取器")
    print("=" * 50)
    
    # 创建提取器
    extractor = NotesDataExtractor()
    
    # 提取数据
    data = extractor.extract_all_data()
    
    # 保存数据
    extractor.save_to_json()
    
    # 生成配置
    config = extractor.generate_practice_config()
    
    # 显示统计信息
    print("\n📊 提取统计:")
    print(f"- 场景类型: {len(data['scenarios'])}")
    print(f"- 模板数量: {len(data['templates'])}")
    print(f"- 变量类型: {len(data['variables'])}")
    print(f"- 错误模式: {len(data['error_patterns'])}")
    
    print("\n🎉 数据提取完成！现在可以使用英语练习插件了。")

if __name__ == "__main__":
    main()
