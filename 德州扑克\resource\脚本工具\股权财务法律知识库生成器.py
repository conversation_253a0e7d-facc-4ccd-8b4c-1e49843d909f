#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股权财务法律知识库生成器
专门处理股权、财务、法律相关笔记，建立智能链接关系
"""

import os
import re
from pathlib import Path
from datetime import datetime
import hashlib

class EquityFinanceLegalGenerator:
    def __init__(self, source_dir, output_dir):
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.cards_dir = self.output_dir / "知识卡片"
        self.index_dir = self.output_dir / "索引"
        self.cards_dir.mkdir(exist_ok=True)
        self.index_dir.mkdir(exist_ok=True)
        
        self.all_cards = []
        self.keyword_cards = {}  # 关键词到卡片的映射
        
    def get_core_keywords(self):
        """股权财务法律核心关键词"""
        return {
            '股权类': ['股权', '股份', '股东', '控制权', '投票权', '分红权', '优先股', '普通股', 'AB股', '期权池'],
            '融资类': ['融资', '投资', '估值', '对赌协议', '清算优先权', '领售权', '反稀释', '一票否决权'],
            '法律类': ['合同', '协议', '法律风险', '合规', '知识产权', '竞业禁止', '保密协议', '劳动合同'],
            '财务类': ['财务报表', '现金流', '利润', '成本', '税务', '审计', '会计', '预算', '资产负债'],
            '风险类': ['风险控制', '担保', '连带责任', '个人担保', '抵押', '质押', '保险', '风险评估'],
            '治理类': ['董事会', '股东会', '公司治理', '决策机制', '监督机制', '内控制度', '合伙人'],
            '退出类': ['IPO', '并购', '股权转让', '回购', '清算', '破产', '重组', '上市']
        }
    
    def clean_title(self, title):
        """清理标题"""
        title = re.sub(r'^[✅🏆📌⚡️💡🔍🌰❌📊🎯🚀💰⭐️\d\.\s\-\*\#]+', '', title)
        title = re.sub(r'[：:]+$', '', title)
        title = title.strip()
        # 去掉markdown格式
        title = re.sub(r'\*\*(.*?)\*\*', r'\1', title)
        return title
    
    def extract_keywords_from_content(self, content):
        """从内容中提取关键词"""
        found_keywords = []
        all_keywords = self.get_core_keywords()
        
        for category, keywords in all_keywords.items():
            for keyword in keywords:
                if keyword in content:
                    found_keywords.append((keyword, category))
        
        return found_keywords
    
    def generate_smart_tags(self, title, content):
        """生成智能标签"""
        tags = []
        keywords = self.extract_keywords_from_content(f"{title} {content}")
        
        # 基于关键词生成分类标签
        categories = set()
        for keyword, category in keywords:
            categories.add(category)
        
        tags.extend(list(categories))
        
        # 基于内容特征生成功能标签
        if any(word in content for word in ['案例', '实例', '例子', '故事']):
            tags.append('实战案例')
        if any(word in content for word in ['风险', '陷阱', '坑', '注意']):
            tags.append('风险警示')
        if any(word in content for word in ['方法', '技巧', '策略', '操作']):
            tags.append('实用方法')
        if any(word in content for word in ['法则', '原则', '规律', '定律']):
            tags.append('核心法则')
        if any(word in content for word in ['模板', '范本', '格式', '标准']):
            tags.append('模板工具')
        
        return list(set(tags))
    
    def find_related_cards(self, current_keywords, current_title):
        """找到相关卡片"""
        related = []
        current_keyword_set = set([kw[0] for kw in current_keywords])
        
        for card in self.all_cards:
            if card['title'] == current_title:
                continue
                
            card_keywords = set([kw[0] for kw in card.get('keywords', [])])
            
            # 计算关键词重叠度
            overlap = len(current_keyword_set & card_keywords)
            if overlap >= 2:  # 至少2个关键词重叠
                related.append((card, overlap))
        
        # 按重叠度排序，返回前5个
        related.sort(key=lambda x: x[1], reverse=True)
        return [card[0] for card in related[:5]]
    
    def safe_filename(self, title):
        """生成安全文件名"""
        safe = re.sub(r'[<>:"/\\|?*\n\r\t]', '_', title)
        safe = re.sub(r'[_\s]+', '_', safe)
        safe = safe.strip('_')
        if len(safe) > 45:
            safe = safe[:45]
        return safe if safe else "知识点"
    
    def extract_sections(self, content, file_path):
        """提取章节"""
        sections = []
        lines = content.split('\n')
        current_section = None
        current_content = []
        
        for line in lines:
            # 检测标题行
            if (line.startswith('#') or 
                re.match(r'^[✅🏆📌⚡️💡🔍🌰❌📊🎯🚀💰⭐️\d\.\s]*[\*\-]*\s*\*\*.*\*\*', line) or
                re.match(r'^[✅🏆📌⚡️💡🔍🌰❌📊🎯🚀💰⭐️\d\.\s]*.*[:：]$', line)):
                
                # 保存上一个章节
                if current_section and current_content:
                    content_text = '\n'.join(current_content).strip()
                    if len(content_text) > 100:  # 只保留有足够内容的章节
                        sections.append({
                            'title': current_section,
                            'content': content_text,
                            'source_file': file_path.name
                        })
                
                # 开始新章节
                current_section = self.clean_title(line.replace('#', '').replace('*', '').strip())
                current_content = [line]
                
            else:
                if current_section:
                    current_content.append(line)
        
        # 保存最后一个章节
        if current_section and current_content:
            content_text = '\n'.join(current_content).strip()
            if len(content_text) > 100:
                sections.append({
                    'title': current_section,
                    'content': content_text,
                    'source_file': file_path.name
                })
        
        return sections
    
    def create_knowledge_card(self, section, card_id, related_cards):
        """创建知识卡片"""
        title = section['title']
        content = section['content']
        source_file = section['source_file']
        
        tags = self.generate_smart_tags(title, content)
        keywords = self.extract_keywords_from_content(f"{title} {content}")
        
        # 构建卡片内容
        card_content = f"""---
id: {card_id}
title: {title}
source: [[{source_file}]]
tags: {', '.join(f'#{tag}' for tag in tags)}
keywords: {', '.join([kw[0] for kw in keywords])}
created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
---

# {title}

{content}

---

## 核心关键词
{', '.join([f'**{kw[0]}**({kw[1]})' for kw in keywords[:8]])}

## 相关链接
- 来源笔记: [[{source_file}]]
"""
        
        # 添加相关知识点链接
        if related_cards:
            card_content += "\n## 相关知识点\n"
            for related in related_cards:
                safe_title = self.safe_filename(related['title'])
                card_content += f"- [[{safe_title}]] - {related['title']}\n"
        
        return {
            'content': card_content,
            'keywords': keywords,
            'tags': tags
        }
    
    def process_file(self, file_path):
        """处理单个文件"""
        print(f"处理文件: {file_path.name}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"读取失败: {e}")
            return
        
        sections = self.extract_sections(content, file_path)
        
        if not sections:
            print(f"  未找到有效章节")
            return
        
        print(f"  提取到 {len(sections)} 个知识点")
        
        for section in sections:
            # 生成卡片ID
            card_id = hashlib.md5(f"{file_path.name}_{section['title']}".encode()).hexdigest()[:8]
            
            # 预先添加到all_cards以便查找相关性
            keywords = self.extract_keywords_from_content(f"{section['title']} {section['content']}")
            card_info = {
                'title': section['title'],
                'keywords': keywords,
                'source': file_path.name
            }
            self.all_cards.append(card_info)
        
        # 第二遍处理：创建卡片并建立链接
        for i, section in enumerate(sections):
            card_id = hashlib.md5(f"{file_path.name}_{section['title']}".encode()).hexdigest()[:8]
            current_card = self.all_cards[len(self.all_cards) - len(sections) + i]
            
            # 找到相关卡片
            related_cards = self.find_related_cards(current_card['keywords'], current_card['title'])
            
            # 创建卡片
            card_data = self.create_knowledge_card(section, card_id, related_cards)
            
            # 保存卡片
            safe_title = self.safe_filename(section['title'])
            card_filename = f"{safe_title}.md"
            card_path = self.cards_dir / card_filename
            
            counter = 1
            while card_path.exists():
                card_filename = f"{safe_title}_{counter}.md"
                card_path = self.cards_dir / card_filename
                counter += 1
            
            with open(card_path, 'w', encoding='utf-8') as f:
                f.write(card_data['content'])
            
            # 更新卡片信息
            current_card.update({
                'filename': card_filename,
                'tags': card_data['tags']
            })
            
            print(f"    ✓ {safe_title}")
    
    def create_index_files(self):
        """创建索引文件"""
        # 按关键词分类索引
        keyword_index = {}
        all_keywords = self.get_core_keywords()
        
        for category, keywords in all_keywords.items():
            keyword_index[category] = []
            for card in self.all_cards:
                card_keywords = [kw[0] for kw in card.get('keywords', [])]
                if any(kw in card_keywords for kw in keywords):
                    keyword_index[category].append(card)
        
        # 生成关键词索引
        keyword_content = "# 股权财务法律知识库 - 关键词索引\n\n"
        for category, cards in keyword_index.items():
            if cards:
                keyword_content += f"## {category}\n\n"
                for card in cards:
                    safe_title = self.safe_filename(card['title'])
                    keyword_content += f"- [[{safe_title}]] - {card['title']}\n"
                keyword_content += "\n"
        
        with open(self.index_dir / "关键词索引.md", 'w', encoding='utf-8') as f:
            f.write(keyword_content)
        
        # 生成总索引
        total_content = f"""# 股权财务法律知识库 - 总索引

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
总卡片数: {len(self.all_cards)}

## 所有知识卡片

"""
        for card in sorted(self.all_cards, key=lambda x: x['title']):
            safe_title = self.safe_filename(card['title'])
            tags_str = ', '.join(f'#{tag}' for tag in card.get('tags', []))
            total_content += f"- [[{safe_title}]] - {card['title']} ({tags_str})\n"
        
        with open(self.index_dir / "总索引.md", 'w', encoding='utf-8') as f:
            f.write(total_content)
    
    def run(self):
        """运行生成器"""
        print(f"开始处理股权财务法律笔记")
        print(f"源目录: {self.source_dir}")
        print(f"输出目录: {self.output_dir}")
        
        # 找到所有markdown文件
        md_files = list(self.source_dir.rglob("*.md"))
        print(f"找到 {len(md_files)} 个文件\n")
        
        # 处理每个文件
        for file_path in md_files:
            self.process_file(file_path)
        
        # 创建索引
        print(f"\n创建索引文件...")
        self.create_index_files()
        
        print(f"\n✅ 完成！生成了 {len(self.all_cards)} 个知识卡片")
        print(f"📁 保存在: {self.output_dir}")

if __name__ == "__main__":
    # 配置路径
    source_directory = Path("智库辅助手册/法则")  # 源笔记目录
    output_directory = Path("智库辅助手册/股权财务法律知识库")  # 输出目录
    
    print(f"源目录: {source_directory}")
    print(f"输出目录: {output_directory}")
    
    # 运行生成器
    generator = EquityFinanceLegalGenerator(source_directory, output_directory)
    generator.run()
