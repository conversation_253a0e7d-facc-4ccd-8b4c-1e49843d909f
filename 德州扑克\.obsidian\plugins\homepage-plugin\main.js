const { Plugin, Setting, PluginSettingTab, TFile, Notice } = require('obsidian');

const DEFAULT_SETTINGS = {
	homepagePath: '',
	openOnStartup: true,
	replaceCurrentTab: false,
	pinHomepage: true,
	pinnedPosition: 0,
	smartMode: true
};

module.exports = class HomepagePlugin extends Plugin {
	async onload() {
		console.log('🏠 首页插件开始加载...');
		
		await this.loadSettings();
		console.log('✅ 插件设置已加载:', this.settings);

		// 添加设置选项卡
		this.addSettingTab(new HomepageSettingTab(this.app, this));

		// 添加命令
		this.addCommand({
			id: 'open-homepage',
			name: '打开首页',
			callback: () => {
				console.log('🔄 手动打开首页命令被触发');
				this.openHomepage();
			}
		});

		// 添加设置当前页为首页的命令
		this.addCommand({
			id: 'set-current-as-homepage',
			name: '设置当前页为首页',
			callback: () => {
				console.log('⚙️ 设置当前页为首页命令被触发');
				this.setCurrentAsHomepage();
			}
		});

		// 添加调试命令
		this.addCommand({
			id: 'debug-homepage',
			name: '调试首页插件',
			callback: () => {
				this.debugHomepage();
			}
		});

		// 添加测试命令
		this.addCommand({
			id: 'test-homepage',
			name: '测试首页功能',
			callback: () => {
				this.testHomepage();
			}
		});

		// 启动时打开首页
		console.log('🔍 检查启动设置:', {
			openOnStartup: this.settings.openOnStartup,
			homepagePath: this.settings.homepagePath,
			pinHomepage: this.settings.pinHomepage,
			replaceCurrentTab: this.settings.replaceCurrentTab
		});
		
		if (this.settings.openOnStartup && this.settings.homepagePath) {
			console.log('🚀 准备在启动时打开首页');
			this.app.workspace.onLayoutReady(() => {
				console.log('📐 工作区布局就绪，即将打开首页');
				setTimeout(() => {
					console.log('⏰ 延迟100ms后打开首页');
					this.openHomepage();
				}, 100);
			});
		} else {
			console.log('❌ 不会在启动时打开首页，原因:', {
				openOnStartup: this.settings.openOnStartup,
				homepagePath: this.settings.homepagePath
			});
		}
		
		// 启用首页位置维护
		if (this.settings.pinHomepage) {
			this.registerEvent(
				this.app.workspace.on('layout-change', () => {
					setTimeout(() => {
						this.maintainHomepagePosition();
					}, 100);
				})
			);

			// 根据智能模式决定监听策略
			if (this.settings.smartMode) {
				// 智能模式：只在必要时维护首页位置
				this.registerEvent(
					this.app.workspace.on('file-open', (file) => {
						// 只有当打开的不是首页文件时，才检查首页位置
						if (file && file.path !== this.settings.homepagePath) {
							setTimeout(() => {
								this.maintainHomepagePosition();
							}, 500); // 增加延迟，减少干扰
						}
					})
				);
			} else {
				// 严格模式：更频繁地维护首页位置
				this.registerEvent(
					this.app.workspace.on('file-open', (file) => {
						setTimeout(() => {
							this.maintainHomepagePosition();
						}, 100);
					})
				);

				this.registerEvent(
					this.app.workspace.on('active-leaf-change', (leaf) => {
						setTimeout(() => {
							this.maintainHomepagePosition();
						}, 100);
					})
				);
			}
		}
		
		console.log('✅ 首页插件加载完成!');
	}

	async setCurrentAsHomepage() {
		const activeFile = this.app.workspace.getActiveFile();
		if (!activeFile) {
			new Notice('没有打开的文件');
			return;
		}

		this.settings.homepagePath = activeFile.path;
		await this.saveSettings();
		new Notice(`已将 "${activeFile.name}" 设置为首页`);
	}

	debugHomepage() {
		console.log('🐛 === 首页插件调试信息 ===');
		console.log('📋 插件设置:', this.settings);
		console.log('📁 Vault路径:', this.app.vault.adapter.basePath);
		console.log('🔍 查找首页文件:', this.settings.homepagePath);
		
		const file = this.app.vault.getAbstractFileByPath(this.settings.homepagePath);
		console.log('📄 文件查找结果:', file);
		
		if (file) {
			console.log('✅ 文件存在:', {
				name: file.name,
				path: file.path,
				type: file.constructor.name
			});
		} else {
			console.log('❌ 文件不存在');
			// 列出所有文件以供参考
			const allFiles = this.app.vault.getFiles();
			console.log('📚 Vault中的所有文件:', allFiles.map(f => f.path));
		}
		
		console.log('🖥️ 当前工作区状态:', {
			activeLeaf: this.app.workspace.activeLeaf,
			leftSplit: this.app.workspace.leftSplit,
			rightSplit: this.app.workspace.rightSplit,
			rootSplit: this.app.workspace.rootSplit
		});
		
		new Notice('调试信息已输出到控制台，请按F12查看');
	}

	testHomepage() {
		console.log('🧪 === 测试首页功能 ===');
		new Notice('开始测试首页功能...');
		
		// 测试基本设置
		if (!this.settings.homepagePath) {
			console.log('❌ 测试失败: 首页路径未设置');
			new Notice('测试失败: 请先设置首页路径');
			return;
		}
		
		// 测试文件是否存在
		const file = this.app.vault.getAbstractFileByPath(this.settings.homepagePath);
		if (!file) {
			console.log('❌ 测试失败: 首页文件不存在');
			new Notice('测试失败: 首页文件不存在');
			return;
		}
		
		// 测试获取标签页
		const leaf = this.app.workspace.getLeaf('tab');
		if (!leaf || typeof leaf.openFile !== 'function') {
			console.log('❌ 测试失败: 无法获取有效的标签页');
			new Notice('测试失败: 无法获取有效的标签页');
			return;
		}
		
		console.log('✅ 所有测试通过!');
		new Notice('✅ 首页功能测试通过! 现在尝试打开首页...');
		
		// 执行打开首页
		this.openHomepage();
	}

	async openHomepage() {
		console.log('🏠 开始执行 openHomepage 方法');
		if (!this.settings.homepagePath) {
			console.log('❌ 首页路径未设置');
			new Notice('请先在设置中配置首页文件路径');
			return;
		}

		console.log('🔍 查找文件:', this.settings.homepagePath);
		const file = this.app.vault.getAbstractFileByPath(this.settings.homepagePath);
		if (!file) {
			console.log('❌ 文件不存在:', this.settings.homepagePath);
			console.log('📚 可用文件列表:', this.app.vault.getFiles().map(f => f.path));
			new Notice('首页文件不存在: ' + this.settings.homepagePath + '\n请检查文件路径是否正确');
			return;
		}
		console.log('✅ 文件找到:', file);

		let leaf;
		
		console.log('🔧 Homepage Plugin: 插件设置', {
			pinHomepage: this.settings.pinHomepage,
			pinnedPosition: this.settings.pinnedPosition,
			replaceCurrentTab: this.settings.replaceCurrentTab
		});
		
		try {
			// 如果启用了固定位置功能，优先处理位置逻辑
			if (this.settings.pinHomepage) {
				console.log('📌 固定位置模式：检查首页是否已在指定位置');
				leaf = this.getOrCreateLeafAtPosition(this.settings.pinnedPosition);
				
				// 如果该位置的标签页已经是首页，直接激活
				if (leaf.view && leaf.view.file && leaf.view.file.path === this.settings.homepagePath) {
					console.log('✅ 首页已在指定位置，直接激活');
					this.app.workspace.setActiveLeaf(leaf);
					return;
				}
			} else {
				// 非固定位置模式的原有逻辑
				if (this.settings.replaceCurrentTab && this.app.workspace.activeLeaf) {
					console.log('🔄 替换当前活动标签页');
					leaf = this.app.workspace.activeLeaf;
					// 确保活动标签页在主编辑区域
					if (leaf.parent !== this.app.workspace.rootSplit) {
						console.log('⚠️ 活动标签页不在主编辑区域，获取主编辑区域标签页');
						leaf = this.app.workspace.getMostRecentLeaf(this.app.workspace.rootSplit) || this.app.workspace.getLeaf('tab');
					}
				} else {
					console.log('➕ 在主编辑区域创建新标签页');
					// 使用更可靠的方法获取主编辑区域的标签页
					leaf = this.app.workspace.getMostRecentLeaf(this.app.workspace.rootSplit);
					if (!leaf) {
						console.log('🔧 没有可用的主编辑区域标签页，创建新的');
						leaf = this.app.workspace.getLeaf('tab');
					}
				}
			}

			console.log('📄 准备打开文件:', { 
				file: file.path, 
				leaf: leaf,
				leafType: leaf.constructor.name,
				hasOpenFile: typeof leaf.openFile === 'function',
				leafParent: leaf.parent?.constructor.name,
				isInMainArea: leaf.parent === this.app.workspace.rootSplit,
				rootSplit: this.app.workspace.rootSplit?.constructor.name
			});
			
			// 验证 leaf 对象是否有效
			if (!leaf || typeof leaf.openFile !== 'function') {
				console.error('❌ 无效的 leaf 对象，尝试使用备用方法');
				// 使用备用方法：直接在主编辑区域打开文件
				try {
					await this.app.workspace.openLinkText(file.path, '', false, { active: true });
					console.log('✅ 使用备用方法打开文件成功');
					new Notice('首页已打开: ' + file.name);
					return;
				} catch (backupError) {
					console.error('❌ 备用方法也失败:', backupError);
					new Notice('无法打开首页文件');
					return;
				}
			}
			
			// 如果标签页不在主编辑区域，使用 openLinkText 方法
			if (leaf.parent !== this.app.workspace.rootSplit) {
				console.log('🔄 标签页不在主编辑区域，使用 openLinkText 方法');
				await this.app.workspace.openLinkText(file.path, '', false, { active: true });
			} else {
				await leaf.openFile(file);
			}
			
			console.log('✅ 文件打开成功');
			new Notice('首页已打开: ' + file.name);
			
			// 如果启用了固定位置，确保首页在正确位置
			if (this.settings.pinHomepage) {
				setTimeout(() => {
					this.maintainHomepagePosition();
				}, 100);
			}
			
		} catch (error) {
			console.error('❌ 打开首页失败:', error);
			console.error('错误详情:', {
				message: error.message,
				stack: error.stack,
				leaf: leaf,
				file: file
			});
			new Notice('打开首页失败: ' + error.message);
			return;
		}
	}

	async loadSettings() {
		this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
	}

	async saveSettings() {
		await this.saveData(this.settings);
	}

	// 获取或创建指定位置的标签页
	getOrCreateLeafAtPosition(position) {
		console.log('🔍 获取或创建指定位置的标签页:', position);
		
		// 获取主编辑区域的所有子元素（标签页）
		const rootSplit = this.app.workspace.rootSplit;
		const children = rootSplit.children || [];
		console.log('📋 主编辑区域当前标签页数量:', children.length);
		
		// 如果指定位置已有标签页，返回该标签页
		if (children[position]) {
			console.log('✅ 使用现有位置的标签页:', position);
			return children[position];
		}
		
		// 如果位置0没有标签页，创建新标签页
		if (position === 0 && children.length === 0) {
			console.log('➕ 创建第一个标签页');
			const newLeaf = this.app.workspace.getLeaf('tab');
			console.log('📄 新标签页创建成功');
			return newLeaf;
		}
		
		// 如果指定位置超出范围，使用最后一个标签页或创建新的
		if (children.length > 0) {
			console.log('📍 使用最近的标签页');
			return children[Math.min(position, children.length - 1)];
		}
		
		// 备用方案：创建新标签页
		console.log('🔧 备用方案：创建新标签页');
		const newLeaf = this.app.workspace.getLeaf('tab');
		return newLeaf;
	}

	// 将标签页移动到指定位置
	moveLeafToPosition(leaf, targetPosition) {
		try {
			const rootSplit = this.app.workspace.rootSplit;
			const children = rootSplit.children;
			const currentIndex = children.indexOf(leaf);
			
			if (currentIndex !== -1 && currentIndex !== targetPosition) {
				// 移除当前位置的标签页
				children.splice(currentIndex, 1);
				// 在目标位置插入标签页
				children.splice(targetPosition, 0, leaf);
				// 重新渲染工作区
				rootSplit.recomputeChildrenDimensions();
			}
		} catch (error) {
			console.log('移动标签页位置时出错:', error);
		}
	}

	// 确保首页保持在固定位置
	ensureHomepagePosition(homepageLeaf) {
		if (!this.settings.pinHomepage) return;
		
		// 监听标签页变化事件
		this.registerEvent(
			this.app.workspace.on('layout-change', () => {
				setTimeout(() => {
					const rootSplit = this.app.workspace.rootSplit;
					const children = rootSplit.children;
					const currentIndex = children.indexOf(homepageLeaf);
					
					// 如果首页标签页不在指定位置，移动它
					if (currentIndex !== -1 && currentIndex !== this.settings.pinnedPosition) {
						this.moveLeafToPosition(homepageLeaf, this.settings.pinnedPosition);
					}
				}, 100);
			})
		);
	}

	// 维护首页位置
	maintainHomepagePosition() {
		if (!this.settings.pinHomepage || !this.settings.homepagePath) return;
		
		// 防止过度频繁的调用
		if (this._maintainInProgress) return;
		this._maintainInProgress = true;
		
		try {
			const file = this.app.vault.getAbstractFileByPath(this.settings.homepagePath);
			if (!file) return;
			
			const rootSplit = this.app.workspace.rootSplit;
			const children = rootSplit.children || [];
			
			// 查找首页标签页
			const homepageLeaf = children.find(leaf => {
				const view = leaf.view;
				return view && view.file && view.file.path === this.settings.homepagePath;
			});
			
			if (homepageLeaf) {
				const currentIndex = children.indexOf(homepageLeaf);
				
				// 只有当首页确实不在指定位置时才移动
				if (currentIndex !== this.settings.pinnedPosition && currentIndex !== -1) {
					console.log('🔄 移动首页到指定位置:', currentIndex, '->', this.settings.pinnedPosition);
					this.moveLeafToPosition(homepageLeaf, this.settings.pinnedPosition);
				}
				
				// 添加首页保护标记，防止被意外关闭
				if (!homepageLeaf._isHomepage) {
					homepageLeaf._isHomepage = true;
				}
			} else if (children.length === 0) {
				// 只有当没有任何标签页时才自动重新打开首页
				console.log('⚠️ 没有标签页，重新打开首页');
				setTimeout(() => {
					this.openHomepage();
				}, 100);
			}
		} catch (error) {
			console.log('维护首页位置时出错:', error);
		} finally {
			// 释放锁定状态
			setTimeout(() => {
				this._maintainInProgress = false;
			}, 100);
		}
	}
};

class HomepageSettingTab extends PluginSettingTab {
	constructor(app, plugin) {
		super(app, plugin);
		this.plugin = plugin;
	}

	display() {
		const { containerEl } = this;
		containerEl.empty();

		containerEl.createEl('h2', { text: '首页插件设置' });

		new Setting(containerEl)
			.setName('首页文件路径')
			.setDesc('输入要作为首页的文件路径（如：README.md 或 folder/homepage.md）')
			.addText(text => text
				.setPlaceholder('例如: README.md')
				.setValue(this.plugin.settings.homepagePath)
				.onChange(async (value) => {
					this.plugin.settings.homepagePath = value;
					await this.plugin.saveSettings();
				}));

		new Setting(containerEl)
			.setName('启动时打开首页')
			.setDesc('Obsidian 启动时自动打开首页（如果首页不自动打开，请确保此选项已开启）')
			.addToggle(toggle => toggle
				.setValue(this.plugin.settings.openOnStartup)
				.onChange(async (value) => {
					this.plugin.settings.openOnStartup = value;
					await this.plugin.saveSettings();
				}));

		new Setting(containerEl)
			.setName('替换当前标签页')
			.setDesc('在当前标签页中打开首页，而不是新建标签页（仅在未启用固定位置时生效）')
			.addToggle(toggle => toggle
				.setValue(this.plugin.settings.replaceCurrentTab)
				.onChange(async (value) => {
					this.plugin.settings.replaceCurrentTab = value;
					await this.plugin.saveSettings();
				}));

		new Setting(containerEl)
			.setName('固定首页位置')
			.setDesc('启用后，首页将始终保持在第一个标签页位置，不会被其他操作移动或删除')
			.addToggle(toggle => toggle
				.setValue(this.plugin.settings.pinHomepage)
				.onChange(async (value) => {
					this.plugin.settings.pinHomepage = value;
					await this.plugin.saveSettings();
					// 如果启用固定位置，立即应用
					if (value) {
						setTimeout(() => {
							this.plugin.maintainHomepagePosition();
						}, 100);
					}
					this.display();
				}));

		// 只有在启用固定位置时才显示相关选项
		if (this.plugin.settings.pinHomepage) {
			new Setting(containerEl)
				.setName('智能模式')
				.setDesc('启用后，减少对正常文件操作的干扰，仅在必要时维护首页位置')
				.addToggle(toggle => toggle
					.setValue(this.plugin.settings.smartMode)
					.onChange(async (value) => {
						this.plugin.settings.smartMode = value;
						await this.plugin.saveSettings();
						// 提示用户重新加载插件以应用新设置
						new Notice('请重新加载插件以应用智能模式设置');
					}));
		}

		// 只有在启用固定位置时才显示位置设置
		if (this.plugin.settings.pinHomepage) {
			new Setting(containerEl)
				.setName('固定位置')
				.setDesc('首页在标签页中的固定位置（0表示第一个位置）')
				.addSlider(slider => slider
					.setLimits(0, 5, 1)
					.setValue(this.plugin.settings.pinnedPosition)
					.setDynamicTooltip()
					.onChange(async (value) => {
						this.plugin.settings.pinnedPosition = value;
						await this.plugin.saveSettings();
						// 立即应用新位置
						setTimeout(() => {
							this.plugin.maintainHomepagePosition();
						}, 100);
					}));
		}

		// 添加测试按钮
		new Setting(containerEl)
			.setName('测试功能')
			.setDesc('测试首页插件是否正常工作')
			.addButton(button => button
				.setButtonText('测试首页功能')
				.onClick(async () => {
					this.plugin.testHomepage();
				}));

		// 添加设置当前页为首页的按钮
		new Setting(containerEl)
			.setName('快速设置')
			.setDesc('将当前打开的文件设置为首页')
			.addButton(button => button
				.setButtonText('设置当前页为首页')
				.setCta()
				.onClick(async () => {
					await this.plugin.setCurrentAsHomepage();
					// 刷新设置页面以显示新的首页路径
					this.display();
				}));
	}
}