﻿/**
 * AI模板生成脚本
 * 功能：基于现有内容，自动生成特定格式的文档模�? * 作者：Builder
 * 版本�?.0
 */

/**
 * AI模板生成函数
 * @param {string} token - API密钥
 * @param {string} templateType - 模板类型（如'report', 'email', 'meeting', 'article'等）
 * @param {string} propertyName - 保存模板的属性名
 * @param {string} modelType - AI模型类型，默认为'GLM-4-Flash'
 * @returns {Promise<Object>} 生成结果
 */
async function aiTemplate(token, templateType, propertyName, modelType = 'GLM-4-Flash') {
    try {
        // 验证参数
        if (!token || !templateType || !propertyName) {
            throw new Error('缺少必要参数：token, templateType, propertyName');
        }

        console.log(`开始生成模板：类型=${templateType}, 属�?${propertyName}`);
        
        // 获取当前文件内容
        const activeFile = app.workspace.getActiveFile();
        if (!activeFile) {
            throw new Error('请先打开一个文�?);
        }
        
        const content = await app.vault.read(activeFile);
        
        // 生成模板
        const template = await generateTemplate(content, templateType, token, modelType);
        
        // 保存到文件属�?        await saveToProperty(activeFile, propertyName, template);
        
        new Notice(`模板生成完成！已保存到属性：${propertyName}`);
        
        return {
            success: true,
            templateType,
            propertyName,
            template
        };
        
    } catch (error) {
        console.error('模板生成失败:', error);
        new Notice(`模板生成失败: ${error.message}`);
        return { success: false, error: error.message };
    }
}

/**
 * 生成特定类型的模�? * @param {string} content - 原始内容
 * @param {string} templateType - 模板类型
 * @param {string} token - API密钥
 * @param {string} modelType - 模型类型
 * @returns {Promise<string>} 生成的模�? */
async function generateTemplate(content, templateType, token, modelType) {
    const templatePrompts = {
        'report': `基于以下内容，生成一个标准化的报告模板，包含以下结构�?1. 执行摘要
2. 背景介绍
3. 主要发现
4. 结论建议
5. 附录

请提供详细的模板框架，包含每个部分的写作要点和格式要求。`,
        
        'email': `基于以下内容，生成一个专业的邮件模板，包含：
1. 主题行建议（3个选项�?2. 开场白模板
3. 正文结构框架
4. 结尾语选项
5. 签名格式建议

请确保语言专业、简洁、有效。`,
        
        'meeting': `基于以下内容，生成一个会议文档模板，包含�?1. 会议议程模板
2. 讨论要点框架
3. 决议事项记录格式
4. 行动计划模板
5. 后续跟进清单

请提供可直接使用的结构化模板。`,
        
        'article': `基于以下内容，生成一个文章写作模板，包含�?1. 标题建议�?个选项�?2. 引言结构模板
3. 主体段落框架
4. 结论模板
5. SEO关键词建�?
请提供完整的写作指导和结构框架。`,
        
        'proposal': `基于以下内容，生成一个项目提案模板，包含�?1. 项目概述
2. 问题陈述
3. 解决方案
4. 实施计划
5. 预算估算
6. 风险评估

请提供专业的提案结构和写作要点。`,
        
        'presentation': `基于以下内容，生成一个演示文稿模板，包含�?1. 开场白模板
2. 主要观点结构
3. 数据展示框架
4. 互动环节设计
5. 总结和行动号�?
请提供吸引人的演示结构。`,
        
        'manual': `基于以下内容，生成一个操作手册模板，包含�?1. 概述和目�?2. 步骤详解
3. 注意事项
4. 常见问题
5. 故障排除

请提供清晰易懂的手册结构。`,
        
        'contract': `基于以下内容，生成一个合同模板框架，包含�?1. 合同主体信息
2. 权利义务条款
3. 履行条件
4. 违约责任
5. 争议解决

请提供标准的合同结构（仅供参考，具体合同需专业律师审核）。`
    };
    
    const prompt = templatePrompts[templateType] || `请基于以下内容生成一�?{templateType}类型的模板：`;
    
    const requestBody = {
        model: modelType,
        messages: [
            {
                role: "system",
                content: "你是一个专业的文档模板生成专家，擅长创建各种类型的结构化模板。请根据用户提供的内容和要求，生成高质量、实用的模板�?
            },
            {
                role: "user",
                content: `${prompt}\n\n原始内容：\n${content}\n\n请生成详细的模板，包含具体的结构、格式要求和写作指导。`
            }
        ],
        temperature: 0.7,
        max_tokens: 3000
    };
    
    const response = await fetch('https://open.bigmodel.cn/api/paas/v4/chat/completions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
    }
    
    const data = await response.json();
    return data.choices[0].message.content;
}

/**
 * 保存模板到文件属�? * @param {Object} file - 文件对象
 * @param {string} propertyName - 属性名
 * @param {string} template - 模板内容
 */
async function saveToProperty(file, propertyName, template) {
    await app.fileManager.processFrontMatter(file, (frontmatter) => {
        frontmatter[propertyName] = template;
    });
}

/**
 * 快速生成常用模�? * @param {string} token - API密钥
 * @param {string} type - 快速模板类�? * @returns {Promise<Object>} 生成结果
 */
async function quickTemplate(token, type) {
    const quickTypes = {
        'daily': {
            templateType: 'article',
            propertyName: '日报模板',
            content: '今日工作总结和明日计�?
        },
        'weekly': {
            templateType: 'report',
            propertyName: '周报模板',
            content: '本周工作回顾和下周规�?
        },
        'project': {
            templateType: 'proposal',
            propertyName: '项目模板',
            content: '项目启动和管理框�?
        },
        'study': {
            templateType: 'article',
            propertyName: '学习笔记模板',
            content: '学习内容整理和知识总结'
        }
    };
    
    const config = quickTypes[type];
    if (!config) {
        throw new Error(`不支持的快速模板类�? ${type}`);
    }
    
    return await aiTemplate(token, config.templateType, config.propertyName);
}

/**
 * 批量生成多种模板
 * @param {string} token - API密钥
 * @param {Array} templateTypes - 模板类型数组
 * @returns {Promise<Object>} 生成结果
 */
async function batchTemplate(token, templateTypes) {
    const results = [];
    
    for (const type of templateTypes) {
        try {
            const result = await aiTemplate(token, type, `${type}模板`);
            results.push(result);
            
            // 添加延迟避免API限制
            await new Promise(resolve => setTimeout(resolve, 2000));
            
        } catch (error) {
            console.error(`生成${type}模板失败:`, error);
            results.push({ success: false, templateType: type, error: error.message });
        }
    }
    
    return {
        success: true,
        totalTemplates: templateTypes.length,
        results
    };
}

// 导出函数
module.exports = {
    aiTemplate,
    quickTemplate,
    batchTemplate
};

// 使用说明
console.log(`
AI模板生成脚本已加载！

主要函数�?1. aiTemplate(token, templateType, propertyName, modelType)
2. quickTemplate(token, type)
3. batchTemplate(token, templateTypes)

支持的模板类型：
- report: 报告模板
- email: 邮件模板
- meeting: 会议模板
- article: 文章模板
- proposal: 提案模板
- presentation: 演示模板
- manual: 手册模板
- contract: 合同模板

快速模板类型：
- daily: 日报模板
- weekly: 周报模板
- project: 项目模板
- study: 学习笔记模板

使用示例�?// 生成报告模板
aiTemplate('your-token', 'report', '报告模板')

// 快速生成日报模�?quickTemplate('your-token', 'daily')

// 批量生成多种模板
batchTemplate('your-token', ['report', 'email', 'meeting'])
`);
