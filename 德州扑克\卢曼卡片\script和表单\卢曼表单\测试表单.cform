{"id": "test-form", "fields": [{"id": "date", "label": "日期", "type": "text", "defaultValue": "{{date:YYYY-MM-DD}}", "description": "测试日期"}, {"id": "topic", "label": "主题", "type": "text", "description": "测试主题"}, {"id": "content", "label": "内容", "type": "textarea", "rows": 3, "description": "测试内容"}], "action": {"id": "generate-test", "type": "runScript", "targetFolder": "", "fileName": "", "scriptSource": "inline", "code": "async function entry() {\n  const { form, app, Notice } = this.$context;\n  \n  try {\n    // 测试字段访问\n    const testContent = `# 测试表单结果\n\n**日期**: ${form.date || '未填写'}\n**主题**: ${form.topic || '未填写'}\n**内容**: ${form.content || '未填写'}\n\n**调试信息**:\n- form对象: ${JSON.stringify(form, null, 2)}\n\n---\n*生成时间：${new Date().toLocaleString()}*`;\n    \n    // 创建文件\n    const fileName = `测试结果-${new Date().toISOString().split('T')[0]}.md`;\n    const filePath = `工作室/肌肉/生成笔记/测试/${fileName}`;\n    \n    // 检查并创建目录\n    const folderPath = '工作室/肌肉/生成笔记/测试';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    // 创建文件\n    const file = await app.vault.create(filePath, testContent);\n    new Notice(`测试文件已创建: ${fileName}`);\n    \n    // 打开新创建的文件\n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 测试完成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('测试失败:', error);\n    new Notice('测试失败: ' + error.message);\n    return '❌ 测试失败，请检查配置';\n  }\n}\n\nexports.default = {\n  entry: entry,\n};"}, "title": "测试表单"}