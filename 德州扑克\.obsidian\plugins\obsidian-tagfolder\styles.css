.workspace-leaf-content[data-type="tagfolder-view"]>.view-content,
.workspace-leaf-content[data-type="tagfolder-view-list"]>.view-content {
	padding: 0;
	height: 100%;
	overflow: unset;
}

.override-input {
	flex-direction: column-reverse;
}

.tagfolder-tag::before {
	all: inherit;
	color: inherit;
	font: inherit;
}

.tagfolder-tag.tag-tag::before {
	margin-left: 0px;
	margin-right: 0px;
}

.tagfolder-tag.tag-tag span.tf-tag-each:empty {
	display: none;
}

.tagfolder-tag.tag-tag.tf-tag span.tf-tag-each:first-child:not(:empty):before {
	content: "#";
	margin-left: 0.3em;
}

.tagfolder-tag.tag-tag.tf-link span.tf-tag-each:first-child:not(:empty):before {
	content: "";
	margin-left: 0.3em;
}

.tagfolder-tag.tag-tag.tf-link.link-cross span.tf-tag-each:first-child:not(:empty):before {
	content: "⇔ ";
	margin-left: 0.3em;
}

.tagfolder-tag.tag-tag.tf-link.link-forward span.tf-tag-each:first-child:not(:empty):before {
	content: "⇒ ";
	margin-left: 0.3em;
}

.tagfolder-tag.tag-tag.tf-link.link-reverse span.tf-tag-each:first-child:not(:empty):before {
	content: "⇐ ";
	margin-left: 0.3em;
}

.tagfolder-tag.tag-tag span.tf-tag-each::before {
	content: "→ ";
	margin-left: 0;
	margin-right: 0;
}


.tf-taglist {
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
	display: inline-block;
	flex-shrink: 100;
	margin-left: auto;
	text-align: right;
}

.tf-taglist:not(:empty) {
	min-width: 3em;
}

.tf-taglist .tf-tag {
	background-color: var(--background-secondary-alt);
	border-radius: 4px;
	padding: 2px 4px;
	margin-left: 4px;
	color: var(--nav-item-color);
}

.nav-folder-title:hover .tagfolder-quantity,
.nav-file-title:hover .tf-taglist {
	color: var(--text-on-accent);
}

.nav-folder-title:hover .tagfolder-quantity span,
.nav-file-title:hover .tf-taglist span.tf-tag {
	color: var(--text-on-accent);
	background-color: var(--interactive-accent-hover);
}


.lsl-f {
	flex-direction: row;
	display: flex;
	flex-grow: 1;
	overflow: hidden;
	flex-shrink: 1;
}

.lsl-f:not(:last-child) {
	min-width: 3em;
}

.lsl-f:empty::before {
	content: "...";
}

.tagfolder-titletagname {
	flex-grow: 1;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
}

.tagfolder-quantity span {
	background-color: var(--background-secondary-alt);
	color: var(--nav-item-color);
	border-radius: 4px;
	padding: 2px 4px;
}

.tagfolder-quantity {
	width: 3em;
	text-align: right;
	cursor: pointer;
	margin-left: auto;
}

.tag-folder-title {
	max-width: 100%;
}

.tree-item.nav-folder.updating {
	background: linear-gradient(135deg, var(--interactive-accent-hover) 0%, var(--interactive-accent-hover) 50%, rgba(0, 0, 0, 0) 50%, rgba(0, 0, 0, 0) 100%);
	background-repeat: no-repeat;
	background-position: 0 0;
	background-size: 10px 10px;
}