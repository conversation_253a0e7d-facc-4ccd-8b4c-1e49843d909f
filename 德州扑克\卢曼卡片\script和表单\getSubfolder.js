﻿async function getSubfolder(fileParentFolder){
    // 分割路径
    const parts = fileParentFolder.split('/');
    const first = parts[0] || "";
    const second = parts[1] || "";
    const third = parts[2] || "";
    
    // 1. 判断是否是归�?
    const isArchived = first === "04-Archives";
    
    // 2. 确定parent
    let parent = "Project"; // 默认�?
    const parentKeywords = {
        "01-Projects": "Project",
        "02-Areas": "Area",
        "03-Resources": "Resource"
    };
    
    // 检查第一个或第二个路径是否包含parent关键�?
    if (parentKeywords[first]) {
        parent = parentKeywords[first];
    } else if (parentKeywords[second]) {
        parent = parentKeywords[second];
    }
    
    // 3. 确定branch
    let branch = "";
    const branchKeywords = {
        "001-学习": "学习",
        "002-生活": "生活",
        "003-项目": "项目"
    };
    
    // 检查第二个或第三个路径是否包含branch关键�?
    if (branchKeywords[second]) {
        branch = branchKeywords[second];
    } else if (branchKeywords[third]) {
        branch = branchKeywords[third];
    }
    
    // 4. 确定subfolder
    let subfolder = "";
    
    // 如果有branch，则从branch所在位置之后开始计算subfolder
    if (branch) {
        const branchIndex = parts.findIndex(part => 
            part === "001-学习" || part === "002-生活" || part === "003-项目"
        );
        if (branchIndex !== -1 && branchIndex + 1 < parts.length) {
            subfolder = parts.slice(branchIndex + 1).join('/');
        }
    } 
    // 如果没有branch，则从第二个路径之后开始计算subfolder（如果第一个路径不是parent�?
    else {
        const parentIndex = parts.findIndex(part => 
            part === "01-Projects" || part === "02-Areas" || part === "03-Resources"
        );
        if (parentIndex !== -1 && parentIndex + 1 < parts.length) {
            subfolder = parts.slice(parentIndex + 1).join('/');
        } else if (parts.length > 1) {
            subfolder = parts.slice(1).join('/');
        }
    }
    
    return subfolder;
}

exports.default = {
    name: 'getSubfolder',
    description: `输入"prop('\${file.parent}')", 自动分割文件所在文件夹的subfolder并返�? 比如: \n�?4-Archives/01-Projects/003-项目/subfolder1�? 返回: "subfolder1"\n"03-Resources/001-学习/subfolder2": 返回: "subfolder2"\n"01-Projects": 返回空�? ""`,
    entry: getSubfolder,
}
