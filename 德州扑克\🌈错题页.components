{"components": [{"id": "a65ef6ee-fd1a-4c91-b615-3c57f4a0948d", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2025-03-25T13:04:09.233Z", "updateAt": "2025-03-25T13:04:09.233Z", "components": [{"componentId": "1d80d70b-dce3-4ba5-a661-b743e067e3ec"}, {"componentId": "7091e6ee-07ab-43a5-9088-2720c5aeb0e4"}, {"componentId": "e55a5db2-cafa-4ff4-8da8-4a67c46426be"}, {"componentId": "c4e8de2a-f395-4ab4-91cc-a8291ab49a3a"}, {"componentId": "f8b887df-6437-4680-b7fd-04044b2e541e"}, {"componentId": "8b866999-e7de-469e-8ac4-8e15c0e34b36"}, {"componentId": "3e3dfffd-d6b4-418a-a0e8-345ec59176d7"}, {"componentId": "9deb8de1-7989-45f2-ad9d-8bba83234ac1"}, {"componentId": "f387320e-ab77-4abd-9b83-0c4d087834a7"}, {"componentId": "c499208a-0d28-4562-a6f3-2651947798cc"}, {"componentId": "fb5bcf7a-a224-412f-b76a-488bf672cdc8"}], "layoutType": "tab", "locked": false, "layoutOptions": {}}, {"id": "1d80d70b-dce3-4ba5-a661-b743e067e3ec", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "查询", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T13:04:36.133Z", "updateAt": "2025-03-25T13:04:36.133Z", "viewType": "table", "newPageNameFormat": "{{date:MMDD HH:mm}}", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": true, "options": {"width": "1107.7142944335938", "pinned": "left"}, "alias": "File Name"}, {"id": "20a2162a-297b-4a44-b852-dd135779c5a0", "name": "parent", "isShow": false, "type": "select", "options": {"items": [{"id": "85b72cda-e45a-4ee0-bd66-8ead94985e19", "value": "Project", "color": "components--color-red"}, {"id": "2b85000d-f319-438b-b58f-70ced55f8eda", "value": "Area", "color": "components--color-green"}, {"id": "c500ec07-a7d3-4750-bde1-3d54874358a9", "value": "Resource", "color": "components--color-purple"}, {"id": "1a5fdcdb-7748-41a6-abf3-6aa321ceb5d1", "value": "Diary", "color": "components--color-yellow"}], "pinned": null, "width": "73"}, "alias": "Parent"}, {"id": "cf02fde7-9938-44f7-80a3-c2e4b86b6be5", "name": "branch", "isShow": false, "type": "select", "options": {"items": [{"id": "601c21b3-bfe8-4df9-afc8-77993e69ead9", "value": "学习", "color": "components--color-blue"}, {"id": "1a59031f-cda4-4fd4-b166-d2b30e45fb5f", "value": "生活", "color": "components--color-pink"}, {"id": "fe431b21-d1f4-462e-a328-8276f65a90b7", "value": "项目", "color": "components--color-orange"}], "width": "75", "pinned": null}, "alias": "branch", "wrap": false}, {"id": "b12cee3c-2cdd-40bc-b061-69070f69c56c", "name": "tags", "isShow": false, "type": "multiSelect", "options": {}, "wrap": true}, {"id": "a9b1a3e0-80cd-474c-83f9-6edf390c5bde", "name": "Tasks", "isShow": false, "type": "taskList", "options": {"showTaskList": false, "showAllTasks": false, "hideTaskFields": false, "timeRecordStyle": "tasks", "insertPosition": {"position": "TopOfNote", "headingLine": ""}, "prefix": "", "suffix": "", "totalValueType": "constant", "color": "components--color-none", "uiType": "progressRing", "total": 100}, "alias": "tasks"}, {"id": "1629486d-e1ea-4fa4-a326-b0452b162714", "name": "Archived", "isShow": false, "type": "checkbox", "options": {}, "alias": "archived"}, {"id": "d69f01f1-444b-471a-bc64-81b35b153981", "name": "${file.mtime}", "isShow": false, "type": "datetime", "options": {}, "alias": "modified time"}, {"id": "d3d655c4-f29e-4a30-aaa0-36097e7fbc91", "name": "${file.ctime}", "isShow": false, "type": "datetime", "options": {}, "alias": "created time"}, {"id": "7d996d58-5159-4952-befb-60544507cee8", "name": "🗂️", "isShow": false, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "migrateFile()"}, "width": "87"}, "alias": "auto-file"}], "templates": [{"id": "c027b5fa-af9b-452e-aa56-cecf838bc352", "path": "TestFolder/Templates/New Project.md", "name": "New Project.md", "type": "templater"}], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": true, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "newPageLocation": {"location": "01-Projects"}, "loadLimitPerPage": 25, "filter": {"id": "3ce56217-e3bd-445c-9749-ee5988f8338f", "type": "group", "operator": "and", "conditions": [{"id": "94712956-c3e8-4d9d-a5d9-99c9181ee719", "type": "filter", "operator": "contains", "property": "${file.parent}", "value": "错题本", "conditions": []}]}, "sort": {"orders": [{"id": "fcfa6709-4d24-4c30-bf9c-bfea5ff6ab10", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}, "defaultTemplate": "c027b5fa-af9b-452e-aa56-cecf838bc352"}, {"id": "c4e8de2a-f395-4ab4-91cc-a8291ab49a3a", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "错题", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T13:04:36.133Z", "updateAt": "2025-03-25T13:04:36.133Z", "viewType": "table", "newPageNameFormat": "{{date:MMDD HH:mm}}", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": true, "options": {"width": "153", "pinned": "left"}, "alias": "File Name"}, {"id": "20a2162a-297b-4a44-b852-dd135779c5a0", "name": "parent", "isShow": false, "type": "select", "options": {"items": [{"id": "85b72cda-e45a-4ee0-bd66-8ead94985e19", "value": "Project", "color": "components--color-red"}, {"id": "2b85000d-f319-438b-b58f-70ced55f8eda", "value": "Area", "color": "components--color-green"}, {"id": "c500ec07-a7d3-4750-bde1-3d54874358a9", "value": "Resource", "color": "components--color-purple"}, {"id": "1a5fdcdb-7748-41a6-abf3-6aa321ceb5d1", "value": "Diary", "color": "components--color-yellow"}], "pinned": null, "width": "73"}, "alias": "Parent"}, {"id": "5a18b4bb-2aef-4d2e-870e-d1d5385a271a", "name": "correct_expression", "isShow": true, "type": "text", "options": {}}, {"id": "325d525e-f78f-449c-ab76-bbb3a7e77c8f", "name": "usage_scenario", "isShow": true, "type": "text", "options": {}}, {"id": "c8e50ccc-bf2a-4dd0-9cf3-d6d5003f8861", "name": "common_errors", "isShow": true, "type": "text", "options": {}}, {"id": "cf02fde7-9938-44f7-80a3-c2e4b86b6be5", "name": "branch", "isShow": false, "type": "select", "options": {"items": [{"id": "601c21b3-bfe8-4df9-afc8-77993e69ead9", "value": "学习", "color": "components--color-blue"}, {"id": "1a59031f-cda4-4fd4-b166-d2b30e45fb5f", "value": "生活", "color": "components--color-pink"}, {"id": "fe431b21-d1f4-462e-a328-8276f65a90b7", "value": "项目", "color": "components--color-orange"}], "width": "75", "pinned": null}, "alias": "branch", "wrap": false}, {"id": "b12cee3c-2cdd-40bc-b061-69070f69c56c", "name": "tags", "isShow": true, "type": "multiSelect", "options": {}, "wrap": true}, {"id": "51a34193-5a44-442f-accb-b2353644d26e", "name": "Archived", "isShow": false, "type": "checkbox", "options": {}, "alias": "archived"}, {"id": "d69f01f1-444b-471a-bc64-81b35b153981", "name": "${file.mtime}", "isShow": false, "type": "datetime", "options": {}, "alias": "modified time"}, {"id": "d3d655c4-f29e-4a30-aaa0-36097e7fbc91", "name": "${file.ctime}", "isShow": false, "type": "datetime", "options": {}, "alias": "created time"}, {"id": "d627fc1b-9017-4334-bcf6-27f00e0de211", "name": "🗂️", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "migrateFile()"}, "width": "49"}, "alias": "auto-file"}, {"id": "74716a8b-07ea-4613-bf37-81caab8988bd", "name": "frequency", "isShow": true, "type": "text", "options": {"width": "67"}}, {"id": "b6047932-1c50-4453-bc17-ac34596e4865", "name": "concept_type", "isShow": false, "type": "text", "options": {}}, {"id": "0f0ca5ad-48b9-400d-83aa-7d15a75c2579", "name": "difficulty", "isShow": true, "type": "text", "options": {"width": "81"}}, {"id": "75bf3a2c-7517-4fb6-bac8-f73fb6bdec85", "name": "error_type", "isShow": false, "type": "text", "options": {}}], "templates": [{"id": "ec7e462a-d5dd-4462-8ede-fcc87b74d5fe", "path": "TestFolder/Templates/New Area.md", "name": "New Area.md", "type": "templater"}], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": true, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "format": "none", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "newPageLocation": {"location": "02-Areas"}, "loadLimitPerPage": 25, "filter": {"id": "3ce56217-e3bd-445c-9749-ee5988f8338f", "type": "group", "operator": "and", "conditions": [{"id": "eb5614fc-7545-41cd-8ea2-c67b99e27e14", "type": "filter", "operator": "contains", "property": "${file.parent}", "value": "错题本", "conditions": []}]}, "sort": {"orders": [{"id": "fcfa6709-4d24-4c30-bf9c-bfea5ff6ab10", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}, "defaultTemplate": "ec7e462a-d5dd-4462-8ede-fcc87b74d5fe", "groupBy": "error_type"}, {"id": "c499208a-0d28-4562-a6f3-2651947798cc", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "Calendar", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T15:13:16.772Z", "updateAt": "2025-03-25T15:13:16.772Z", "viewType": "calendar", "newPageNameFormat": "{{date: YYYY-MM-DD}}", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": true, "options": {}}, {"id": "8049fc77-dfbe-4be6-934c-04a40d713a2e", "name": "${file.mtime}", "isShow": false, "type": "datetime", "options": {}}], "templates": [{"id": "8556662d-2059-4768-9e79-7436c3caaad4", "path": "TestFolder/Templates/Daily Notes.md", "name": "Daily Notes.md", "type": "templater"}], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "itemSize": "components--page-card-medium", "showPropertyName": false, "showAllProperties": false, "items": [], "cover": {"type": "none", "source": "default", "fit": "contains", "layout": "components--page-card-cover-landscape", "position": "top", "sourceValue": "## "}, "pinFiltersToMenuBar": false, "showGrid": false, "heightType": "auto", "heightValue": 600, "calendarViewType": "month", "cardColorFrom": "property", "cardColorProperty": "total words", "dateProperty": "${file.basename}", "endDateProperty": ""}, "groupStates": {"sort": "nameAsc", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "newPageLocation": {"location": "日志/Daily Notes"}, "defaultTemplate": "8556662d-2059-4768-9e79-7436c3caaad4"}, {"id": "fb5bcf7a-a224-412f-b76a-488bf672cdc8", "type": "attachments", "titleAlign": "center", "tabTitle": "Attachments", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2025-03-25T15:30:55.775Z", "updateAt": "2025-03-25T15:30:55.775Z", "attachmentSource": "All", "attachmentType": "All", "fileExensions": [], "specifiedFilePath": "", "sortField": "createTime", "sortDirection": "desc", "title": ""}, {"id": "f8b887df-6437-4680-b7fd-04044b2e541e", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "必背句式", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T13:04:36.133Z", "updateAt": "2025-03-25T13:04:36.133Z", "viewType": "table", "newPageNameFormat": "{{date:MMDD HH:mm}}", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": true, "options": {"width": "153", "pinned": "left"}, "alias": "File Name"}, {"id": "20a2162a-297b-4a44-b852-dd135779c5a0", "name": "parent", "isShow": false, "type": "select", "options": {"items": [{"id": "85b72cda-e45a-4ee0-bd66-8ead94985e19", "value": "Project", "color": "components--color-red"}, {"id": "2b85000d-f319-438b-b58f-70ced55f8eda", "value": "Area", "color": "components--color-green"}, {"id": "c500ec07-a7d3-4750-bde1-3d54874358a9", "value": "Resource", "color": "components--color-purple"}, {"id": "1a5fdcdb-7748-41a6-abf3-6aa321ceb5d1", "value": "Diary", "color": "components--color-yellow"}], "pinned": null, "width": "73"}, "alias": "Parent"}, {"id": "5a18b4bb-2aef-4d2e-870e-d1d5385a271a", "name": "correct_expression", "isShow": true, "type": "text", "options": {}}, {"id": "325d525e-f78f-449c-ab76-bbb3a7e77c8f", "name": "usage_scenario", "isShow": true, "type": "text", "options": {}}, {"id": "c8e50ccc-bf2a-4dd0-9cf3-d6d5003f8861", "name": "common_errors", "isShow": true, "type": "text", "options": {}}, {"id": "cf02fde7-9938-44f7-80a3-c2e4b86b6be5", "name": "branch", "isShow": false, "type": "select", "options": {"items": [{"id": "601c21b3-bfe8-4df9-afc8-77993e69ead9", "value": "学习", "color": "components--color-blue"}, {"id": "1a59031f-cda4-4fd4-b166-d2b30e45fb5f", "value": "生活", "color": "components--color-pink"}, {"id": "fe431b21-d1f4-462e-a328-8276f65a90b7", "value": "项目", "color": "components--color-orange"}], "width": "75", "pinned": null}, "alias": "branch", "wrap": false}, {"id": "b12cee3c-2cdd-40bc-b061-69070f69c56c", "name": "tags", "isShow": true, "type": "multiSelect", "options": {}, "wrap": true}, {"id": "51a34193-5a44-442f-accb-b2353644d26e", "name": "Archived", "isShow": false, "type": "checkbox", "options": {}, "alias": "archived"}, {"id": "d69f01f1-444b-471a-bc64-81b35b153981", "name": "${file.mtime}", "isShow": false, "type": "datetime", "options": {}, "alias": "modified time"}, {"id": "d3d655c4-f29e-4a30-aaa0-36097e7fbc91", "name": "${file.ctime}", "isShow": false, "type": "datetime", "options": {}, "alias": "created time"}, {"id": "d627fc1b-9017-4334-bcf6-27f00e0de211", "name": "🗂️", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "migrateFile()"}, "width": "49"}, "alias": "auto-file"}, {"id": "74716a8b-07ea-4613-bf37-81caab8988bd", "name": "frequency", "isShow": true, "type": "text", "options": {"width": "67"}}, {"id": "b6047932-1c50-4453-bc17-ac34596e4865", "name": "concept_type", "isShow": false, "type": "text", "options": {}}, {"id": "0f0ca5ad-48b9-400d-83aa-7d15a75c2579", "name": "difficulty", "isShow": true, "type": "text", "options": {"width": "81"}}, {"id": "75bf3a2c-7517-4fb6-bac8-f73fb6bdec85", "name": "error_type", "isShow": false, "type": "text", "options": {}}], "templates": [{"id": "ec7e462a-d5dd-4462-8ede-fcc87b74d5fe", "path": "TestFolder/Templates/New Area.md", "name": "New Area.md", "type": "templater"}], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": true, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "format": "none", "statics": [], "orders": [], "hiddens": ["表达法律影响的错误 - 高频场景专业模板", "对比关系错误 - 逻辑衔接类错误", "法律名称错误 - 术语/概念类错误", "冠词与单复数错误 - 语法结构类错误", "技术概念错误 - 术语/概念类错误", "介词搭配错误 - 语法结构类错误", "描述合规挑战的表达错误 - 高频场景专业模板", "讨论解决方案的表达错误 - 高频场景专业模板", "因果表述错误 - 逻辑衔接类错误", "主谓宾结构错误 - 语法结构类错误", "专业术语概念 - 国际数据传输", "专业术语概念 - 数据保护核心理念", "专业术语概念 - 数据治理体系", "专业术语概念 - 系统架构设计", "专业术语概念 - GDPR合规要求", "专业术语概念 - GDPR核心机制", "专业术语概念 - IT系统分类", "__$uncategorizedGroup", "代词指代不明错误", "冠词与单复数错误", "介词搭配错误", "逻辑衔接类错误", "术语混淆错误 - 风险评估表述", "术语混淆错误 - 技术方案表述", "术语混淆错误 - 情态动词误用", "语用问题 - 不当表述", "语用问题 - 过时/不恰当用语", "语用问题 - 回应不充分", "语用问题 - 强弱语气误用", "语用问题 - 正式度不匹配", "语用问题 - 主观表达", "语用问题 - 主观表述不专业", "专业术语概念 - 高级词汇替换", "专业术语概念 - 高级词汇替换矩阵", "专业术语概念 - 监管文件挖矿法", "专业术语概念 - 精准量化型表达", "专业术语概念 - 方案策略类", "专业术语概念 - 高频核心词汇", "专业术语概念 - 价值成果类", "专业术语概念 - 问题诊断类"], "collapseds": []}, "newPageLocation": {"location": "02-Areas"}, "loadLimitPerPage": 25, "filter": {"id": "3ce56217-e3bd-445c-9749-ee5988f8338f", "type": "group", "operator": "and", "conditions": [{"id": "eb5614fc-7545-41cd-8ea2-c67b99e27e14", "type": "filter", "operator": "contains", "property": "${file.parent}", "value": "错题本", "conditions": []}]}, "sort": {"orders": [{"id": "fcfa6709-4d24-4c30-bf9c-bfea5ff6ab10", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}, "defaultTemplate": "ec7e462a-d5dd-4462-8ede-fcc87b74d5fe", "groupBy": "error_type"}, {"id": "8b866999-e7de-469e-8ac4-8e15c0e34b36", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "专业术语", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T13:04:36.133Z", "updateAt": "2025-03-25T13:04:36.133Z", "viewType": "table", "newPageNameFormat": "{{date:MMDD HH:mm}}", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": true, "options": {"width": "153", "pinned": "left"}, "alias": "File Name"}, {"id": "20a2162a-297b-4a44-b852-dd135779c5a0", "name": "parent", "isShow": false, "type": "select", "options": {"items": [{"id": "85b72cda-e45a-4ee0-bd66-8ead94985e19", "value": "Project", "color": "components--color-red"}, {"id": "2b85000d-f319-438b-b58f-70ced55f8eda", "value": "Area", "color": "components--color-green"}, {"id": "c500ec07-a7d3-4750-bde1-3d54874358a9", "value": "Resource", "color": "components--color-purple"}, {"id": "1a5fdcdb-7748-41a6-abf3-6aa321ceb5d1", "value": "Diary", "color": "components--color-yellow"}], "pinned": null, "width": "73"}, "alias": "Parent"}, {"id": "5a18b4bb-2aef-4d2e-870e-d1d5385a271a", "name": "correct_expression", "isShow": true, "type": "text", "options": {}}, {"id": "325d525e-f78f-449c-ab76-bbb3a7e77c8f", "name": "usage_scenario", "isShow": true, "type": "text", "options": {}}, {"id": "c8e50ccc-bf2a-4dd0-9cf3-d6d5003f8861", "name": "common_errors", "isShow": true, "type": "text", "options": {}}, {"id": "cf02fde7-9938-44f7-80a3-c2e4b86b6be5", "name": "branch", "isShow": false, "type": "select", "options": {"items": [{"id": "601c21b3-bfe8-4df9-afc8-77993e69ead9", "value": "学习", "color": "components--color-blue"}, {"id": "1a59031f-cda4-4fd4-b166-d2b30e45fb5f", "value": "生活", "color": "components--color-pink"}, {"id": "fe431b21-d1f4-462e-a328-8276f65a90b7", "value": "项目", "color": "components--color-orange"}], "width": "75", "pinned": null}, "alias": "branch", "wrap": false}, {"id": "b12cee3c-2cdd-40bc-b061-69070f69c56c", "name": "tags", "isShow": true, "type": "multiSelect", "options": {}, "wrap": true}, {"id": "51a34193-5a44-442f-accb-b2353644d26e", "name": "Archived", "isShow": false, "type": "checkbox", "options": {}, "alias": "archived"}, {"id": "d69f01f1-444b-471a-bc64-81b35b153981", "name": "${file.mtime}", "isShow": false, "type": "datetime", "options": {}, "alias": "modified time"}, {"id": "d3d655c4-f29e-4a30-aaa0-36097e7fbc91", "name": "${file.ctime}", "isShow": false, "type": "datetime", "options": {}, "alias": "created time"}, {"id": "d627fc1b-9017-4334-bcf6-27f00e0de211", "name": "🗂️", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "migrateFile()"}, "width": "49"}, "alias": "auto-file"}, {"id": "74716a8b-07ea-4613-bf37-81caab8988bd", "name": "frequency", "isShow": true, "type": "text", "options": {"width": "67"}}, {"id": "b6047932-1c50-4453-bc17-ac34596e4865", "name": "concept_type", "isShow": false, "type": "text", "options": {}}, {"id": "0f0ca5ad-48b9-400d-83aa-7d15a75c2579", "name": "difficulty", "isShow": true, "type": "text", "options": {"width": "81"}}, {"id": "75bf3a2c-7517-4fb6-bac8-f73fb6bdec85", "name": "error_type", "isShow": false, "type": "text", "options": {}}], "templates": [{"id": "ec7e462a-d5dd-4462-8ede-fcc87b74d5fe", "path": "TestFolder/Templates/New Area.md", "name": "New Area.md", "type": "templater"}], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": true, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "format": "none", "statics": ["技术概念错误 - 术语/概念类错误", "法律名称错误 - 术语/概念类错误", "表达法律影响的错误 - 高频场景专业模板"], "orders": [], "hiddens": ["__$uncategorizedGroup", "必背专业句式 - 描述法律要求", "必背专业句式 - 分析合规难点", "必背专业句式 - 提出建议", "对比关系错误 - 逻辑衔接类错误", "冠词与单复数错误 - 语法结构类错误", "介词搭配错误 - 语法结构类错误", "描述合规挑战的表达错误 - 高频场景专业模板", "讨论解决方案的表达错误 - 高频场景专业模板", "因果表述错误 - 逻辑衔接类错误", "主谓宾结构错误 - 语法结构类错误", "表达法律影响的错误 - 高频场景专业模板", "必背专业句式 - 法律依据引用", "必背专业句式 - 法律引用", "必背专业句式 - 风险对冲长难句", "必背专业句式 - 技术降维打击句", "必背专业句式 - 跨境传输", "必背专业句式 - 立法引用三明治句", "必背专业句式 - 前瞻建议句", "必背专业句式 - 权利响应", "必背专业句式 - 三并列句", "必背专业句式 - 数据量化句", "必背专业句式 - 重要性表述", "必背专业句式 - 综合训练方案", "代词指代不明错误", "法律名称错误 - 术语/概念类错误", "冠词与单复数错误", "技术概念错误 - 术语/概念类错误", "介词搭配错误", "逻辑衔接类错误", "语用问题 - 不当表述", "语用问题 - 过时/不恰当用语", "语用问题 - 回应不充分", "语用问题 - 强弱语气误用", "语用问题 - 正式度不匹配", "语用问题 - 主观表达", "语用问题 - 主观表述不专业", "必背专业句式 - 辩论开场结构", "必背专业句式 - 反驳技巧", "必背专业句式 - 风险量化+合规对冲", "必背专业句式 - 高压演讲模拟", "必背专业句式 - 技术参数+法律效力", "必背专业句式 - 结构化表述框架", "必背专业句式 - 结尾影响力", "必背专业句式 - 精准定义+立法锚定", "必背专业句式 - 逻辑转换", "必背专业句式 - 权威引用句", "必背专业句式 - 让步转折句", "必背专业句式 - 术语三明治结构", "必背专业句式 - 条件假设句", "必背专业句式 - 因果链式句", "必背专业句式 - 证据呈现公式", "必背专业句式 - 场景化表述模板", "必背专业句式 - 主动赋能表述"], "collapseds": []}, "newPageLocation": {"location": "02-Areas"}, "loadLimitPerPage": 25, "filter": {"id": "3ce56217-e3bd-445c-9749-ee5988f8338f", "type": "group", "operator": "and", "conditions": [{"id": "eb5614fc-7545-41cd-8ea2-c67b99e27e14", "type": "filter", "operator": "contains", "property": "${file.parent}", "value": "错题本", "conditions": []}]}, "sort": {"orders": [{"id": "fcfa6709-4d24-4c30-bf9c-bfea5ff6ab10", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}, "defaultTemplate": "ec7e462a-d5dd-4462-8ede-fcc87b74d5fe", "groupBy": "error_type"}, {"id": "3e3dfffd-d6b4-418a-a0e8-345ec59176d7", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "逻辑", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T13:04:36.133Z", "updateAt": "2025-03-25T13:04:36.133Z", "viewType": "table", "newPageNameFormat": "{{date:MMDD HH:mm}}", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": true, "options": {"width": "153", "pinned": "left"}, "alias": "File Name"}, {"id": "20a2162a-297b-4a44-b852-dd135779c5a0", "name": "parent", "isShow": false, "type": "select", "options": {"items": [{"id": "85b72cda-e45a-4ee0-bd66-8ead94985e19", "value": "Project", "color": "components--color-red"}, {"id": "2b85000d-f319-438b-b58f-70ced55f8eda", "value": "Area", "color": "components--color-green"}, {"id": "c500ec07-a7d3-4750-bde1-3d54874358a9", "value": "Resource", "color": "components--color-purple"}, {"id": "1a5fdcdb-7748-41a6-abf3-6aa321ceb5d1", "value": "Diary", "color": "components--color-yellow"}], "pinned": null, "width": "73"}, "alias": "Parent"}, {"id": "5a18b4bb-2aef-4d2e-870e-d1d5385a271a", "name": "correct_expression", "isShow": true, "type": "text", "options": {}}, {"id": "325d525e-f78f-449c-ab76-bbb3a7e77c8f", "name": "usage_scenario", "isShow": true, "type": "text", "options": {}}, {"id": "c8e50ccc-bf2a-4dd0-9cf3-d6d5003f8861", "name": "common_errors", "isShow": true, "type": "text", "options": {}}, {"id": "cf02fde7-9938-44f7-80a3-c2e4b86b6be5", "name": "branch", "isShow": false, "type": "select", "options": {"items": [{"id": "601c21b3-bfe8-4df9-afc8-77993e69ead9", "value": "学习", "color": "components--color-blue"}, {"id": "1a59031f-cda4-4fd4-b166-d2b30e45fb5f", "value": "生活", "color": "components--color-pink"}, {"id": "fe431b21-d1f4-462e-a328-8276f65a90b7", "value": "项目", "color": "components--color-orange"}], "width": "75", "pinned": null}, "alias": "branch", "wrap": false}, {"id": "b12cee3c-2cdd-40bc-b061-69070f69c56c", "name": "tags", "isShow": true, "type": "multiSelect", "options": {}, "wrap": true}, {"id": "51a34193-5a44-442f-accb-b2353644d26e", "name": "Archived", "isShow": false, "type": "checkbox", "options": {}, "alias": "archived"}, {"id": "d69f01f1-444b-471a-bc64-81b35b153981", "name": "${file.mtime}", "isShow": false, "type": "datetime", "options": {}, "alias": "modified time"}, {"id": "d3d655c4-f29e-4a30-aaa0-36097e7fbc91", "name": "${file.ctime}", "isShow": false, "type": "datetime", "options": {}, "alias": "created time"}, {"id": "d627fc1b-9017-4334-bcf6-27f00e0de211", "name": "🗂️", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "migrateFile()"}, "width": "49"}, "alias": "auto-file"}, {"id": "74716a8b-07ea-4613-bf37-81caab8988bd", "name": "frequency", "isShow": true, "type": "text", "options": {"width": "67"}}, {"id": "b6047932-1c50-4453-bc17-ac34596e4865", "name": "concept_type", "isShow": false, "type": "text", "options": {}}, {"id": "0f0ca5ad-48b9-400d-83aa-7d15a75c2579", "name": "difficulty", "isShow": true, "type": "text", "options": {"width": "81"}}, {"id": "75bf3a2c-7517-4fb6-bac8-f73fb6bdec85", "name": "error_type", "isShow": false, "type": "text", "options": {}}], "templates": [{"id": "ec7e462a-d5dd-4462-8ede-fcc87b74d5fe", "path": "TestFolder/Templates/New Area.md", "name": "New Area.md", "type": "templater"}], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": true, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "format": "none", "statics": [], "orders": [], "hiddens": ["__$uncategorizedGroup", "必背专业句式 - 分析合规难点", "必背专业句式 - 描述法律要求", "必背专业句式 - 提出建议", "专业术语概念 - 国际数据传输", "专业术语概念 - 数据保护核心理念", "专业术语概念 - 数据治理体系", "专业术语概念 - 系统架构设计", "专业术语概念 - GDPR合规要求", "专业术语概念 - GDPR核心机制", "专业术语概念 - IT系统分类", "表达法律影响的错误 - 高频场景专业模板", "法律名称错误 - 术语/概念类错误", "描述合规挑战的表达错误 - 高频场景专业模板", "讨论解决方案的表达错误 - 高频场景专业模板", "技术概念错误 - 术语/概念类错误", "冠词与单复数错误 - 语法结构类错误", "介词搭配错误 - 语法结构类错误", "主谓宾结构错误 - 语法结构类错误", "必背专业句式 - 法律依据引用", "必背专业句式 - 法律引用", "必背专业句式 - 风险对冲长难句", "必背专业句式 - 技术降维打击句", "必背专业句式 - 跨境传输", "必背专业句式 - 立法引用三明治句", "必背专业句式 - 前瞻建议句", "必背专业句式 - 权利响应", "必背专业句式 - 三并列句", "必背专业句式 - 数据量化句", "必背专业句式 - 重要性表述", "必背专业句式 - 综合训练方案", "代词指代不明错误", "冠词与单复数错误", "介词搭配错误", "术语混淆错误 - 风险评估表述", "术语混淆错误 - 技术方案表述", "术语混淆错误 - 情态动词误用", "因果表述错误 - 逻辑衔接类错误", "专业术语概念 - 高级词汇替换", "必背专业句式 - 辩论开场结构", "必背专业句式 - 反驳技巧", "必背专业句式 - 风险量化+合规对冲", "必背专业句式 - 高压演讲模拟", "必背专业句式 - 技术参数+法律效力", "必背专业句式 - 结构化表述框架", "必背专业句式 - 结尾影响力", "必背专业句式 - 精准定义+立法锚定", "必背专业句式 - 逻辑转换", "必背专业句式 - 权威引用句", "必背专业句式 - 让步转折句", "必背专业句式 - 术语三明治结构", "必背专业句式 - 条件假设句", "必背专业句式 - 因果链式句", "必背专业句式 - 证据呈现公式", "专业术语概念 - 方案策略类", "专业术语概念 - 高级词汇替换矩阵", "专业术语概念 - 监管文件挖矿法", "专业术语概念 - 精准量化型表达", "专业术语概念 - 问题诊断类", "必背专业句式 - 场景化表述模板", "必背专业句式 - 主动赋能表述", "语用问题 - 不当表述", "语用问题 - 过时/不恰当用语", "语用问题 - 回应不充分", "语用问题 - 强弱语气误用", "语用问题 - 正式度不匹配", "语用问题 - 主观表达", "语用问题 - 主观表述不专业", "专业术语概念 - 高频核心词汇", "专业术语概念 - 价值成果类"], "collapseds": []}, "newPageLocation": {"location": "02-Areas"}, "loadLimitPerPage": 25, "filter": {"id": "3ce56217-e3bd-445c-9749-ee5988f8338f", "type": "group", "operator": "and", "conditions": [{"id": "30463bda-1ff8-4cac-bc18-668e60ac5000", "type": "filter", "operator": "contains", "property": "${file.parent}", "value": "错题本", "conditions": []}]}, "sort": {"orders": [{"id": "fcfa6709-4d24-4c30-bf9c-bfea5ff6ab10", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}, "defaultTemplate": "ec7e462a-d5dd-4462-8ede-fcc87b74d5fe", "groupBy": "error_type"}, {"id": "9deb8de1-7989-45f2-ad9d-8bba83234ac1", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "语法", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T13:04:36.133Z", "updateAt": "2025-03-25T13:04:36.133Z", "viewType": "table", "newPageNameFormat": "{{date:MMDD HH:mm}}", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": true, "options": {"width": "153", "pinned": "left"}, "alias": "File Name"}, {"id": "20a2162a-297b-4a44-b852-dd135779c5a0", "name": "parent", "isShow": false, "type": "select", "options": {"items": [{"id": "85b72cda-e45a-4ee0-bd66-8ead94985e19", "value": "Project", "color": "components--color-red"}, {"id": "2b85000d-f319-438b-b58f-70ced55f8eda", "value": "Area", "color": "components--color-green"}, {"id": "c500ec07-a7d3-4750-bde1-3d54874358a9", "value": "Resource", "color": "components--color-purple"}, {"id": "1a5fdcdb-7748-41a6-abf3-6aa321ceb5d1", "value": "Diary", "color": "components--color-yellow"}], "pinned": null, "width": "73"}, "alias": "Parent"}, {"id": "5a18b4bb-2aef-4d2e-870e-d1d5385a271a", "name": "correct_expression", "isShow": true, "type": "text", "options": {}}, {"id": "325d525e-f78f-449c-ab76-bbb3a7e77c8f", "name": "usage_scenario", "isShow": true, "type": "text", "options": {}}, {"id": "c8e50ccc-bf2a-4dd0-9cf3-d6d5003f8861", "name": "common_errors", "isShow": true, "type": "text", "options": {}}, {"id": "cf02fde7-9938-44f7-80a3-c2e4b86b6be5", "name": "branch", "isShow": false, "type": "select", "options": {"items": [{"id": "601c21b3-bfe8-4df9-afc8-77993e69ead9", "value": "学习", "color": "components--color-blue"}, {"id": "1a59031f-cda4-4fd4-b166-d2b30e45fb5f", "value": "生活", "color": "components--color-pink"}, {"id": "fe431b21-d1f4-462e-a328-8276f65a90b7", "value": "项目", "color": "components--color-orange"}], "width": "75", "pinned": null}, "alias": "branch", "wrap": false}, {"id": "b12cee3c-2cdd-40bc-b061-69070f69c56c", "name": "tags", "isShow": true, "type": "multiSelect", "options": {}, "wrap": true}, {"id": "51a34193-5a44-442f-accb-b2353644d26e", "name": "Archived", "isShow": false, "type": "checkbox", "options": {}, "alias": "archived"}, {"id": "d69f01f1-444b-471a-bc64-81b35b153981", "name": "${file.mtime}", "isShow": false, "type": "datetime", "options": {}, "alias": "modified time"}, {"id": "d3d655c4-f29e-4a30-aaa0-36097e7fbc91", "name": "${file.ctime}", "isShow": false, "type": "datetime", "options": {}, "alias": "created time"}, {"id": "d627fc1b-9017-4334-bcf6-27f00e0de211", "name": "🗂️", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "migrateFile()"}, "width": "49"}, "alias": "auto-file"}, {"id": "74716a8b-07ea-4613-bf37-81caab8988bd", "name": "frequency", "isShow": true, "type": "text", "options": {"width": "67"}}, {"id": "b6047932-1c50-4453-bc17-ac34596e4865", "name": "concept_type", "isShow": false, "type": "text", "options": {}}, {"id": "0f0ca5ad-48b9-400d-83aa-7d15a75c2579", "name": "difficulty", "isShow": true, "type": "text", "options": {"width": "81"}}, {"id": "75bf3a2c-7517-4fb6-bac8-f73fb6bdec85", "name": "error_type", "isShow": false, "type": "text", "options": {}}], "templates": [{"id": "ec7e462a-d5dd-4462-8ede-fcc87b74d5fe", "path": "TestFolder/Templates/New Area.md", "name": "New Area.md", "type": "templater"}], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": true, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "format": "none", "statics": [], "orders": [], "hiddens": ["__$uncategorizedGroup", "必背专业句式 - 分析合规难点", "必背专业句式 - 描述法律要求", "必背专业句式 - 提出建议", "表达法律影响的错误 - 高频场景专业模板", "对比关系错误 - 逻辑衔接类错误", "法律名称错误 - 术语/概念类错误", "技术概念错误 - 术语/概念类错误", "描述合规挑战的表达错误 - 高频场景专业模板", "讨论解决方案的表达错误 - 高频场景专业模板", "因果表述错误 - 逻辑衔接类错误", "专业术语概念 - 国际数据传输", "专业术语概念 - 数据保护核心理念", "专业术语概念 - 数据治理体系", "专业术语概念 - 系统架构设计", "专业术语概念 - GDPR合规要求", "专业术语概念 - GDPR核心机制", "专业术语概念 - IT系统分类", "必背专业句式 - 法律依据引用", "必背专业句式 - 法律引用", "必背专业句式 - 风险对冲长难句", "必背专业句式 - 技术降维打击句", "必背专业句式 - 跨境传输", "必背专业句式 - 立法引用三明治句", "必背专业句式 - 前瞻建议句", "必背专业句式 - 权利响应", "必背专业句式 - 三并列句", "必背专业句式 - 数据量化句", "必背专业句式 - 重要性表述", "必背专业句式 - 综合训练方案", "逻辑衔接类错误", "术语混淆错误 - 风险评估表述", "术语混淆错误 - 技术方案表述", "术语混淆错误 - 情态动词误用", "语用问题 - 不当表述", "语用问题 - 过时/不恰当用语", "语用问题 - 回应不充分", "语用问题 - 强弱语气误用", "语用问题 - 正式度不匹配", "语用问题 - 主观表达", "语用问题 - 主观表述不专业", "专业术语概念 - 高级词汇替换", "必背专业句式 - 辩论开场结构", "必背专业句式 - 反驳技巧", "必背专业句式 - 风险量化+合规对冲", "必背专业句式 - 高压演讲模拟", "必背专业句式 - 技术参数+法律效力", "必背专业句式 - 结构化表述框架", "必背专业句式 - 结尾影响力", "必背专业句式 - 精准定义+立法锚定", "必背专业句式 - 逻辑转换", "必背专业句式 - 权威引用句", "必背专业句式 - 让步转折句", "必背专业句式 - 术语三明治结构", "必背专业句式 - 条件假设句", "必背专业句式 - 因果链式句", "必背专业句式 - 证据呈现公式", "专业术语概念 - 方案策略类", "专业术语概念 - 高级词汇替换矩阵", "专业术语概念 - 价值成果类", "专业术语概念 - 监管文件挖矿法", "专业术语概念 - 精准量化型表达", "专业术语概念 - 问题诊断类", "必背专业句式 - 场景化表述模板", "必背专业句式 - 主动赋能表述", "专业术语概念 - 高频核心词汇"], "collapseds": []}, "newPageLocation": {"location": "02-Areas"}, "loadLimitPerPage": 25, "filter": {"id": "3ce56217-e3bd-445c-9749-ee5988f8338f", "type": "group", "operator": "and", "conditions": [{"id": "eb5614fc-7545-41cd-8ea2-c67b99e27e14", "type": "filter", "operator": "contains", "property": "${file.parent}", "value": "错题本", "conditions": []}]}, "sort": {"orders": [{"id": "fcfa6709-4d24-4c30-bf9c-bfea5ff6ab10", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}, "defaultTemplate": "ec7e462a-d5dd-4462-8ede-fcc87b74d5fe", "groupBy": "error_type"}, {"id": "f387320e-ab77-4abd-9b83-0c4d087834a7", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "场景", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T13:04:36.133Z", "updateAt": "2025-03-25T13:04:36.133Z", "viewType": "table", "newPageNameFormat": "{{date:MMDD HH:mm}}", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": true, "options": {"width": "153", "pinned": "left"}, "alias": "File Name"}, {"id": "20a2162a-297b-4a44-b852-dd135779c5a0", "name": "parent", "isShow": false, "type": "select", "options": {"items": [{"id": "85b72cda-e45a-4ee0-bd66-8ead94985e19", "value": "Project", "color": "components--color-red"}, {"id": "2b85000d-f319-438b-b58f-70ced55f8eda", "value": "Area", "color": "components--color-green"}, {"id": "c500ec07-a7d3-4750-bde1-3d54874358a9", "value": "Resource", "color": "components--color-purple"}, {"id": "1a5fdcdb-7748-41a6-abf3-6aa321ceb5d1", "value": "Diary", "color": "components--color-yellow"}], "pinned": null, "width": "73"}, "alias": "Parent"}, {"id": "5a18b4bb-2aef-4d2e-870e-d1d5385a271a", "name": "correct_expression", "isShow": true, "type": "text", "options": {}}, {"id": "325d525e-f78f-449c-ab76-bbb3a7e77c8f", "name": "usage_scenario", "isShow": true, "type": "text", "options": {}}, {"id": "c8e50ccc-bf2a-4dd0-9cf3-d6d5003f8861", "name": "common_errors", "isShow": true, "type": "text", "options": {}}, {"id": "cf02fde7-9938-44f7-80a3-c2e4b86b6be5", "name": "branch", "isShow": false, "type": "select", "options": {"items": [{"id": "601c21b3-bfe8-4df9-afc8-77993e69ead9", "value": "学习", "color": "components--color-blue"}, {"id": "1a59031f-cda4-4fd4-b166-d2b30e45fb5f", "value": "生活", "color": "components--color-pink"}, {"id": "fe431b21-d1f4-462e-a328-8276f65a90b7", "value": "项目", "color": "components--color-orange"}], "width": "75", "pinned": null}, "alias": "branch", "wrap": false}, {"id": "b12cee3c-2cdd-40bc-b061-69070f69c56c", "name": "tags", "isShow": true, "type": "multiSelect", "options": {}, "wrap": true}, {"id": "51a34193-5a44-442f-accb-b2353644d26e", "name": "Archived", "isShow": false, "type": "checkbox", "options": {}, "alias": "archived"}, {"id": "d69f01f1-444b-471a-bc64-81b35b153981", "name": "${file.mtime}", "isShow": false, "type": "datetime", "options": {}, "alias": "modified time"}, {"id": "d3d655c4-f29e-4a30-aaa0-36097e7fbc91", "name": "${file.ctime}", "isShow": false, "type": "datetime", "options": {}, "alias": "created time"}, {"id": "d627fc1b-9017-4334-bcf6-27f00e0de211", "name": "🗂️", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "migrateFile()"}, "width": "49"}, "alias": "auto-file"}, {"id": "74716a8b-07ea-4613-bf37-81caab8988bd", "name": "frequency", "isShow": true, "type": "text", "options": {"width": "67"}}, {"id": "b6047932-1c50-4453-bc17-ac34596e4865", "name": "concept_type", "isShow": false, "type": "text", "options": {}}, {"id": "0f0ca5ad-48b9-400d-83aa-7d15a75c2579", "name": "difficulty", "isShow": true, "type": "text", "options": {"width": "81"}}, {"id": "75bf3a2c-7517-4fb6-bac8-f73fb6bdec85", "name": "error_type", "isShow": false, "type": "text", "options": {}}], "templates": [{"id": "ec7e462a-d5dd-4462-8ede-fcc87b74d5fe", "path": "TestFolder/Templates/New Area.md", "name": "New Area.md", "type": "templater"}], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": true, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "format": "none", "statics": ["描述合规挑战的表达错误 - 高频场景专业模板", "讨论解决方案的表达错误 - 高频场景专业模板", "表达法律影响的错误 - 高频场景专业模板", "语用问题 - 不当表述", "语用问题 - 过时/不恰当用语", "语用问题 - 回应不充分", "语用问题 - 强弱语气误用", "语用问题 - 正式度不匹配", "语用问题 - 主观表达", "语用问题 - 主观表述不专业"], "orders": [], "hiddens": ["__$uncategorizedGroup", "必背专业句式 - 分析合规难点", "必背专业句式 - 描述法律要求", "必背专业句式 - 提出建议", "对比关系错误 - 逻辑衔接类错误", "法律名称错误 - 术语/概念类错误", "技术概念错误 - 术语/概念类错误", "因果表述错误 - 逻辑衔接类错误", "专业术语概念 - 国际数据传输", "专业术语概念 - 数据保护核心理念", "专业术语概念 - 数据治理体系", "专业术语概念 - 系统架构设计", "专业术语概念 - GDPR合规要求", "专业术语概念 - GDPR核心机制", "专业术语概念 - IT系统分类", "冠词与单复数错误 - 语法结构类错误", "介词搭配错误 - 语法结构类错误", "主谓宾结构错误 - 语法结构类错误", "必背专业句式 - 法律依据引用", "必背专业句式 - 法律引用", "必背专业句式 - 风险对冲长难句", "必背专业句式 - 技术降维打击句", "必背专业句式 - 跨境传输", "必背专业句式 - 立法引用三明治句", "必背专业句式 - 前瞻建议句", "必背专业句式 - 权利响应", "必背专业句式 - 三并列句", "必背专业句式 - 数据量化句", "必背专业句式 - 重要性表述", "必背专业句式 - 综合训练方案", "代词指代不明错误", "冠词与单复数错误", "介词搭配错误", "逻辑衔接类错误", "术语混淆错误 - 风险评估表述", "术语混淆错误 - 技术方案表述", "术语混淆错误 - 情态动词误用", "专业术语概念 - 高级词汇替换", "必背专业句式 - 辩论开场结构", "必背专业句式 - 场景化表述模板", "必背专业句式 - 反驳技巧", "必背专业句式 - 风险量化+合规对冲", "必背专业句式 - 高压演讲模拟", "必背专业句式 - 技术参数+法律效力", "必背专业句式 - 结构化表述框架", "必背专业句式 - 结尾影响力", "必背专业句式 - 精准定义+立法锚定", "必背专业句式 - 逻辑转换", "必背专业句式 - 权威引用句", "必背专业句式 - 让步转折句", "必背专业句式 - 术语三明治结构", "必背专业句式 - 条件假设句", "必背专业句式 - 因果链式句", "必背专业句式 - 证据呈现公式", "必背专业句式 - 主动赋能表述", "专业术语概念 - 方案策略类", "专业术语概念 - 高级词汇替换矩阵", "专业术语概念 - 高频核心词汇", "专业术语概念 - 价值成果类", "专业术语概念 - 监管文件挖矿法", "专业术语概念 - 精准量化型表达", "专业术语概念 - 问题诊断类"], "collapseds": []}, "newPageLocation": {"location": "02-Areas"}, "loadLimitPerPage": 25, "filter": {"id": "3ce56217-e3bd-445c-9749-ee5988f8338f", "type": "group", "operator": "and", "conditions": [{"id": "eb5614fc-7545-41cd-8ea2-c67b99e27e14", "type": "filter", "operator": "contains", "property": "${file.parent}", "value": "错题本", "conditions": []}]}, "sort": {"orders": [{"id": "fcfa6709-4d24-4c30-bf9c-bfea5ff6ab10", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}, "defaultTemplate": "ec7e462a-d5dd-4462-8ede-fcc87b74d5fe", "groupBy": "error_type"}, {"id": "7091e6ee-07ab-43a5-9088-2720c5aeb0e4", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "笔记", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T13:04:36.133Z", "updateAt": "2025-03-25T13:04:36.133Z", "viewType": "table", "newPageNameFormat": "{{date:MMDD HH:mm}}", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": true, "options": {"width": "1107.7142944335938", "pinned": "left"}, "alias": "File Name"}, {"id": "20a2162a-297b-4a44-b852-dd135779c5a0", "name": "parent", "isShow": false, "type": "select", "options": {"items": [{"id": "85b72cda-e45a-4ee0-bd66-8ead94985e19", "value": "Project", "color": "components--color-red"}, {"id": "2b85000d-f319-438b-b58f-70ced55f8eda", "value": "Area", "color": "components--color-green"}, {"id": "c500ec07-a7d3-4750-bde1-3d54874358a9", "value": "Resource", "color": "components--color-purple"}, {"id": "1a5fdcdb-7748-41a6-abf3-6aa321ceb5d1", "value": "Diary", "color": "components--color-yellow"}], "pinned": null, "width": "73"}, "alias": "Parent"}, {"id": "cf02fde7-9938-44f7-80a3-c2e4b86b6be5", "name": "branch", "isShow": false, "type": "select", "options": {"items": [{"id": "601c21b3-bfe8-4df9-afc8-77993e69ead9", "value": "学习", "color": "components--color-blue"}, {"id": "1a59031f-cda4-4fd4-b166-d2b30e45fb5f", "value": "生活", "color": "components--color-pink"}, {"id": "fe431b21-d1f4-462e-a328-8276f65a90b7", "value": "项目", "color": "components--color-orange"}], "width": "75", "pinned": null}, "alias": "branch", "wrap": false}, {"id": "b12cee3c-2cdd-40bc-b061-69070f69c56c", "name": "tags", "isShow": false, "type": "multiSelect", "options": {}, "wrap": true}, {"id": "a9b1a3e0-80cd-474c-83f9-6edf390c5bde", "name": "Tasks", "isShow": false, "type": "taskList", "options": {"showTaskList": false, "showAllTasks": false, "hideTaskFields": false, "timeRecordStyle": "tasks", "insertPosition": {"position": "TopOfNote", "headingLine": ""}, "prefix": "", "suffix": "", "totalValueType": "constant", "color": "components--color-none", "uiType": "progressRing", "total": 100}, "alias": "tasks"}, {"id": "1629486d-e1ea-4fa4-a326-b0452b162714", "name": "Archived", "isShow": false, "type": "checkbox", "options": {}, "alias": "archived"}, {"id": "d69f01f1-444b-471a-bc64-81b35b153981", "name": "${file.mtime}", "isShow": false, "type": "datetime", "options": {}, "alias": "modified time"}, {"id": "d3d655c4-f29e-4a30-aaa0-36097e7fbc91", "name": "${file.ctime}", "isShow": false, "type": "datetime", "options": {}, "alias": "created time"}, {"id": "7d996d58-5159-4952-befb-60544507cee8", "name": "🗂️", "isShow": false, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "migrateFile()"}, "width": "87"}, "alias": "auto-file"}], "templates": [{"id": "c027b5fa-af9b-452e-aa56-cecf838bc352", "path": "TestFolder/Templates/New Project.md", "name": "New Project.md", "type": "templater"}], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": true, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "format": "none", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "newPageLocation": {"location": "01-Projects"}, "loadLimitPerPage": 25, "filter": {"id": "3ce56217-e3bd-445c-9749-ee5988f8338f", "type": "group", "operator": "or", "conditions": [{"id": "1733a49a-f952-4c07-a9c1-1a98be81a0d9", "type": "filter", "operator": "contains", "property": "${file.parent}", "value": "英语笔记", "conditions": []}, {"id": "e7179556-db8b-4c2a-a516-512200b685e8", "type": "filter", "operator": "contains", "property": "${file.basename}", "value": "组块英语", "conditions": []}]}, "sort": {"orders": [{"id": "fcfa6709-4d24-4c30-bf9c-bfea5ff6ab10", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}, "defaultTemplate": "c027b5fa-af9b-452e-aa56-cecf838bc352", "groupBy": "English type"}, {"id": "e55a5db2-cafa-4ff4-8da8-4a67c46426be", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "地域差异", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T13:04:36.133Z", "updateAt": "2025-03-25T13:04:36.133Z", "viewType": "table", "newPageNameFormat": "{{date:MMDD HH:mm}}", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": true, "options": {"width": "153", "pinned": "left"}, "alias": "File Name"}, {"id": "20a2162a-297b-4a44-b852-dd135779c5a0", "name": "parent", "isShow": false, "type": "select", "options": {"items": [{"id": "85b72cda-e45a-4ee0-bd66-8ead94985e19", "value": "Project", "color": "components--color-red"}, {"id": "2b85000d-f319-438b-b58f-70ced55f8eda", "value": "Area", "color": "components--color-green"}, {"id": "c500ec07-a7d3-4750-bde1-3d54874358a9", "value": "Resource", "color": "components--color-purple"}, {"id": "1a5fdcdb-7748-41a6-abf3-6aa321ceb5d1", "value": "Diary", "color": "components--color-yellow"}], "pinned": null, "width": "73"}, "alias": "Parent"}, {"id": "5a18b4bb-2aef-4d2e-870e-d1d5385a271a", "name": "correct_expression", "isShow": false, "type": "text", "options": {}}, {"id": "325d525e-f78f-449c-ab76-bbb3a7e77c8f", "name": "usage_scenario", "isShow": false, "type": "text", "options": {}}, {"id": "c8e50ccc-bf2a-4dd0-9cf3-d6d5003f8861", "name": "common_errors", "isShow": true, "type": "text", "options": {}}, {"id": "cf02fde7-9938-44f7-80a3-c2e4b86b6be5", "name": "branch", "isShow": false, "type": "select", "options": {"items": [{"id": "601c21b3-bfe8-4df9-afc8-77993e69ead9", "value": "学习", "color": "components--color-blue"}, {"id": "1a59031f-cda4-4fd4-b166-d2b30e45fb5f", "value": "生活", "color": "components--color-pink"}, {"id": "fe431b21-d1f4-462e-a328-8276f65a90b7", "value": "项目", "color": "components--color-orange"}], "width": "75", "pinned": null}, "alias": "branch", "wrap": false}, {"id": "b12cee3c-2cdd-40bc-b061-69070f69c56c", "name": "tags", "isShow": false, "type": "multiSelect", "options": {}, "wrap": true}, {"id": "51a34193-5a44-442f-accb-b2353644d26e", "name": "Archived", "isShow": false, "type": "checkbox", "options": {}, "alias": "archived"}, {"id": "d69f01f1-444b-471a-bc64-81b35b153981", "name": "${file.mtime}", "isShow": false, "type": "datetime", "options": {}, "alias": "modified time"}, {"id": "d3d655c4-f29e-4a30-aaa0-36097e7fbc91", "name": "${file.ctime}", "isShow": false, "type": "datetime", "options": {}, "alias": "created time"}, {"id": "d627fc1b-9017-4334-bcf6-27f00e0de211", "name": "🗂️", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "migrateFile()"}, "width": "49"}, "alias": "auto-file"}, {"id": "74716a8b-07ea-4613-bf37-81caab8988bd", "name": "frequency", "isShow": true, "type": "text", "options": {"width": "67"}}, {"id": "b6047932-1c50-4453-bc17-ac34596e4865", "name": "concept_type", "isShow": false, "type": "text", "options": {}}, {"id": "0f0ca5ad-48b9-400d-83aa-7d15a75c2579", "name": "difficulty", "isShow": false, "type": "text", "options": {"width": "81"}}, {"id": "75bf3a2c-7517-4fb6-bac8-f73fb6bdec85", "name": "error_type", "isShow": false, "type": "text", "options": {}}], "templates": [{"id": "ec7e462a-d5dd-4462-8ede-fcc87b74d5fe", "path": "TestFolder/Templates/New Area.md", "name": "New Area.md", "type": "templater"}], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": true, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "format": "none", "statics": [], "orders": [], "hiddens": ["__$uncategorizedGroup"], "collapseds": []}, "newPageLocation": {"location": "02-Areas"}, "loadLimitPerPage": 25, "filter": {"id": "3ce56217-e3bd-445c-9749-ee5988f8338f", "type": "group", "operator": "and", "conditions": [{"id": "eb5614fc-7545-41cd-8ea2-c67b99e27e14", "type": "filter", "operator": "contains", "property": "${file.parent}", "value": "错题本", "conditions": []}]}, "sort": {"orders": [{"id": "fcfa6709-4d24-4c30-bf9c-bfea5ff6ab10", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}, "defaultTemplate": "ec7e462a-d5dd-4462-8ede-fcc87b74d5fe", "groupBy": "English type"}], "rootComponentId": "a65ef6ee-fd1a-4c91-b615-3c57f4a0948d"}