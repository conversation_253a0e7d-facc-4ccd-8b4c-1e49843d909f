// sleepQualityCalculator.js
// 采用与calculateWorkScore完全相同的导出结构

function computeSleepHours(wakeTime, sleepTime) {
    const [wakeH, wakeM] = wakeTime.split(':').map(Number);
    const [sleepH, sleepM] = sleepTime.split(':').map(Number);
    
    // 判断是否跨天（20:00-24:00 算跨天，00:00-04:00 算同一天）
    const isSameDay = (sleepH >= 0 && sleepH <= 4);
    
    let totalMinutes;
    if (isSameDay) {
        // 同一天（如 1:30 睡觉，8:00 起床）
        totalMinutes = (wakeH - sleepH) * 60 + (wakeM - sleepM);
    } else {
        // 跨天（如 23:00 睡觉，7:00 起床）
        totalMinutes = (24 - sleepH + wakeH) * 60 + (wakeM - sleepM);
    }
    
    return parseFloat((totalMinutes / 60).toFixed(1));
}

async function calculateSleepQuality() {
    try {
        const { currentFile } = this;
        const content = await app.vault.read(currentFile);
        
        const wakeMatch = content.match(/起床[:：]\s*(\d{1,2}[:：]\d{2})/);
        const sleepMatch = content.match(/睡觉[:：]\s*(\d{1,2}[:：]\d{2})/);
        
        if (!wakeMatch || !sleepMatch) return "⏳ 无睡眠数据";
        
        const hours = computeSleepHours(
            wakeMatch[1].replace("：", ":"),
            sleepMatch[1].replace("：", ":")
        );
        
        // 评价逻辑
        let evaluation = "";
        if (hours < 5) evaluation = "⚠️ 睡眠严重不足";
        else if (hours < 6) evaluation = "😴 睡眠不足";
        else if (hours < 7) evaluation = "😌 睡眠基本足够";
        else if (hours < 9) evaluation = "😊 睡眠理想时长";
        else evaluation = "🛌 睡眠时间过长";
        
        return `${evaluation} (${hours}小时)`;
        
    } catch (error) {
        console.error('睡眠计算错误:', error);
        return "⚠️ 计算失败";
    }
}

// 严格遵循calculateWorkScore的导出方式
exports.default = {
    name: "calculateSleepQuality",
    description: `计算睡眠质量并返回评价（兼容Components插件）
    
    输入格式：
    起床: 07:30
    睡觉: 23:45
    
    输出格式：
    [表情] 评价文字 (X小时)`,
    entry: calculateSleepQuality
};