# 卢曼卡片模板提取

## 1. 认知冲突沙盒模板

```markdown
### 认知战场：[领域] 
**对阵双方**：  
- 甲方：[观点A] + 出处  
- 乙方：[观点B] + 出处  
**我的审判**：  
- 证据支持：______  
- 个人体验：______  
- 临时判决：______（必须站队或提出第三条路）  
→ 关联：[[相关经验]] [[矛盾案例]]  
```

## 2. 认知犯罪现场模板

```markdown
### [日期] 认知犯罪现场  
**被谋杀的旧认知**：______（原坚信的观点）  
**凶手证据**：______（新发现的反例）  
**作案手法**：______（思考路径）  
**忏悔录**：我过去愚蠢在______  
→ 关联案件：[[其他凶杀现场]]  
```

## 3. 每日思考熔炼模板

```markdown
### [日期]-思考1：______  
**原料**：  
- 冲突事件：______  
- 相关旧知：[[______]] [[______]]  
**熔炼过程**：  
1. 矛盾核心：______  
2. 现有解释不足：______  
3. 我的假设：______  
**成品检验**：  
- 可行动项：______  
- 可分享点：______（发社交媒体验证）  
→ 关联：[[后续验证计划]]  
```

## 4. 三明治结构问答模板

```markdown
### Q：[具体到能谷歌搜索的问题]  

A：  
- **外部证据**：作者/研究结论（1句话）  
- **我的解读**：用比喻/案例重构  
- **行动指令**：具体到可执行  

→ 关联：[[已有相关笔记]] [[矛盾观点笔记]]  
! 风险：当前理解可能忽略______（留白）  
```

## 5. 超文本论证模板

```markdown
### [主题]对[领域]的潜在颠覆  
- **传统局限**：______  
- **数字机遇**：通过[[A]]←→[[B]]←→[[C]]的网状论证  
- **实证案例**：______  
- **风险预警**：______  
→ 实验：[[验证计划]]  
```

## 6. 血腥量化检验模板

```markdown
### 笔记破产清算  
**检验问题**：
1. 这张卡片改变过我哪个具体行为？
2. 这张卡片被后续思考引用过几次？
3. 这张卡片如果消失，对我的认知体系有何影响？

**处理结果**：
- 若90%卡片答不上来 → 立即停止新增笔记1个月
- 保留的卡片必须用红字标注「实际用途」
```

## 7. 问题手术刀模板

```markdown
### 问题精准切割：[原模糊问题]
**手术前**：______（模糊表述）
**切除操作**：
1. 删除无效修饰词
2. 明确问题边界
3. 添加可验证标准
**手术后**：______（精确问题）
**验证标准**：其他人能否仅看问题就明白在问什么
```

## 8. 工具强制约束模板

```markdown
## 核心问题:: [必须用问句]  
## 答案约束:: ≤3句话+1个案例  
## 链接义务:: 至少链接到1张旧卡片  
## 反思层:: 我的应用/质疑
```

## 9. 认知羞耻制造模板

```markdown
### 专家审查记录
**审查对象**：______（我最得意的笔记）
**专家反馈**：______
**羞耻点**：______（被指出的问题）
**改进方向**：______
→ 关联：[[其他专家意见]]
```

## 10. 生存性写作测试模板

```markdown
### 知识变现测试：[主题]
**禁用资源**：不能直接引用任何外部材料
**仅用资源**：自己的笔记系统
**输出要求**：300字批判性分析
**测试结果**：
- 能写出300字 → 笔记系统基本合格
- 只能写50字 → 系统是"知识棺材"
**改进计划**：______
```

## 11. 卢曼式问答对模板

```markdown
### Q：为什么我的"闪念笔记"总变成垃圾堆？  
A：  
- **书说**：闪念笔记应24小时内处理  
- **我做**：实际平均积压7天 → 因缺少触发机制  
- **创新方案**：  
  1. 设置手机壁纸"今日清空闪念？"  
  2. 绑定到咖啡时间（每天第一杯咖啡时处理）  
  3. 超过3条未处理就罚自己捐10元  
→ 关联：[[习惯养成系统]] [[我的时间锚点]]  
! 已实践：本周闪念处理率从30%→85%  
```

## 12. 知识创业模式模板

```markdown
### 知识MVP：[微观点]
**原料卡片**：[[卡片A]] + [[卡片B]] + [[卡片C]]
**组合假设**：______
**最小验证**：______（朋友圈/微博分享）
**反馈收集**：______
**迭代方向**：______
**ROI计算**：
- 实际应用次数：______
- 引发后续思考：______
- 可归档/删除：______
```

## 13. 笔记炼金术模板

```markdown
### 炼金操作：[原始笔记标题]
**原料等级**：
- #待冶炼（原样抄录）
- #半成品（含少量个人思考）  
- #金块（已有创新点）

**炼金过程**：
- **冲突挖掘**：这与XX观点矛盾在哪？
- **个人案例**：我的相关经历是______
- **改造输出**：______

**成品检验**：能否用于实际决策/写作？
```

## 14. 5W1H暴力拆解模板

```markdown
### 问题拆解：[原问题]
**Who**：涉及哪些主体？
**What**：具体指什么？
**When**：时间范围？
**Where**：适用场景？
**Why**：为什么重要？
**How**：如何操作？

**重构后问题**：______
**可搜索性检验**：能否直接谷歌搜索？
```

## 15. 终极检验标准模板

```markdown
### 著作权检验
**核心问题**："这个内容除了我，还有谁能写出来？"

**评估标准**：
- 作者/老师都能写 → 还在知识搬运层面
- 只有结合我的XX经历才能写 → 真正的卡片盒用法

**改进方向**：
- 记录工作/生活中的真实困惑
- 在笔记中直接质疑作者
- 定期做"认知拆迁"
```