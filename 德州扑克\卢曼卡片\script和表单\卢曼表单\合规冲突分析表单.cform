{"id": "compliance-conflict-analysis-form", "fields": [{"id": "scenario", "label": "具体场景", "type": "text", "description": "描述具体的合规冲突场景"}, {"id": "party-a", "label": "甲方（法规要求/监管观点）", "type": "textarea", "rows": 3, "description": "法规要求或监管观点，包含法条出处"}, {"id": "party-b", "label": "乙方（业务需求/技术限制）", "type": "textarea", "rows": 3, "description": "业务需求或技术限制，包含实际案例"}, {"id": "legal-risk", "label": "法律风险评估", "type": "select", "options": [{"id": "high-risk", "label": "高风险", "value": "高风险"}, {"id": "medium-risk", "label": "中风险", "value": "中风险"}, {"id": "low-risk", "label": "低风险", "value": "低风险"}], "description": "评估法律风险等级"}, {"id": "business-impact", "label": "业务影响分析", "type": "textarea", "rows": 3, "description": "分析对业务的具体影响"}, {"id": "balance-solution", "label": "平衡方案", "type": "textarea", "rows": 5, "description": "提供可执行的平衡建议"}, {"id": "related-regulations", "label": "相关法规", "type": "text", "description": "相关法规链接或标识"}, {"id": "similar-cases", "label": "类似案例", "type": "text", "description": "类似案例链接或标识"}], "action": {"id": "generate-compliance-conflict-analysis", "type": "runScript", "targetFolder": "", "fileName": "", "scriptSource": "inline", "code": "async function entry() {\n  const { form, app, requestUrl, Notice } = this.$context;\n  \n  try {\n    // 生成AI优化的内容\n    let aiEnhancedContent = '';\n    const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec';\n    \n    const prompt = `你是专业的数据合规专家。请基于以下信息，生成一份专业的合规冲突分析报告：\n\n场景：${form.scenario}\n甲方（法规要求）：${form['party-a']}\n乙方（业务需求）：${form['party-b']}\n法律风险等级：${form['legal-risk']}\n业务影响：${form['business-impact']}\n平衡方案：${form['balance-solution']}\n\n请提供：\n1. 深度分析这个合规冲突的本质\n2. 补充可能遗漏的风险点\n3. 优化平衡方案的可操作性\n4. 提供类似案例参考\n\n要求：专业、实用、具体可执行。`;\n    \n    try {\n      const aiResponse = await requestUrl({\n        url: 'https://api.deepseek.com/v1/chat/completions',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: \"deepseek-chat\",\n          messages: [{ role: \"user\", content: prompt }],\n          temperature: 0.7,\n          max_tokens: 1500\n        })\n      });\n      \n      if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n        aiEnhancedContent = aiResponse.json.choices[0].message.content;\n      }\n    } catch (e) {\n      aiEnhancedContent = '(AI分析暂时不可用，请手动补充深度分析)';\n    }\n    \n    // 生成完整内容\n    const template = `### 合规战场：${form.scenario}\n**对阵双方**：\n- 甲方：${form['party-a']}\n- 乙方：${form['party-b']}\n**我的合规判断**：\n- 法律风险评估：${form['legal-risk']}\n- 业务影响分析：${form['business-impact']}\n- 平衡方案：${form['balance-solution']}\n→ 关联：[[${form['related-regulations']}]] [[${form['similar-cases']}]]\n\n---\n\n## 🤖 AI深度分析\n\n${aiEnhancedContent}\n\n---\n\n## 📝 补充记录\n<!-- 在此添加后续思考和实践反馈 -->\n\n\n## 🏷️ 标签\n#合规冲突分析 #${form['legal-risk']} #数据合规 #${new Date().toISOString().split('T')[0]}\n\n---\n*生成时间：${new Date().toLocaleString()} | AI助手：DeepSeek*`;\n    \n    // 创建文件\n    const today = new Date();\n    const dateStr = today.toISOString().split('T')[0];\n    const timeStr = today.toTimeString().split(' ')[0].substring(0, 5).replace(':', '');\n    const fileName = `合规冲突分析-${form.scenario}-${dateStr}-${timeStr}.md`;\n    const filePath = `工作室/肌肉/生成笔记/合规冲突分析/${fileName}`;\n    \n    // 检查并创建目录\n    const folderPath = '工作室/肌肉/生成笔记/合规冲突分析';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    // 创建文件\n    const file = await app.vault.create(filePath, template);\n    new Notice(`合规冲突分析已创建: ${fileName}`);\n    \n    // 打开新创建的文件\n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 合规冲突分析已生成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('生成合规冲突分析失败:', error);\n    new Notice('生成失败: ' + error.message);\n    return '❌ 生成失败，请检查配置';\n  }\n}\n\nexports.default = {\n  entry: entry,\n};"}, "title": "合规冲突分析表单"}