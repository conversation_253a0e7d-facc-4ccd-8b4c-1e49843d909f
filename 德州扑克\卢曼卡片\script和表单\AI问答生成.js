﻿/**
 * AI问答生成脚本
 * 功能：基于文章内容生成问答对
 * 作者：Builder
 * 版本�?.0
 */

/**
 * AI问答生成函数
 * @param {string} token - API密钥
 * @param {string} propertyName - 保存问答的属性名
 * @param {number} questionCount - 问题数量，默认为5
 * @param {string} modelType - AI模型类型，默认为'GLM-4-Flash'
 * @returns {Promise<Object>} 生成结果
 */
async function aiQA(token, propertyName, questionCount = 5, modelType = 'GLM-4-Flash') {
    try {
        // 验证参数
        if (!token || !propertyName) {
            throw new Error('缺少必要参数：token, propertyName');
        }

        console.log(`开始生成问答：数量=${questionCount}, 属�?${propertyName}`);
        
        // 获取当前文件内容
        const activeFile = app.workspace.getActiveFile();
        if (!activeFile) {
            throw new Error('请先打开一个文�?);
        }
        
        const content = await app.vault.read(activeFile);
        
        if (!content.trim()) {
            throw new Error('文件内容为空');
        }
        
        // 构建问答生成提示
        const prompt = `基于以下文章内容，生�?{questionCount}个有价值的问答对，要求�?
1. 问题应该涵盖文章的核心要点和重要概念
2. 问题类型要多样化：事实性问题、理解性问题、应用性问题、分析性问�?3. 答案要准确、完整，基于文章内容
4. 问答对要有教育价值，适合学习和复�?5. 按重要性排�?6. 格式：Q: 问题\nA: 答案\n\n
7. 每个问答对之间用空行分隔

文章内容�?${content}`;
        
        // 调用AI API
        const qaResult = await callQAAPI(prompt, token, modelType);
        
        // 处理问答�?        const processedQA = processQAPairs(qaResult);
        
        // 保存问答到属�?        await saveToProperty(activeFile, propertyName, processedQA);
        
        new Notice(`问答生成完成！生成了 ${processedQA.length} 个问答对`);
        
        return {
            success: true,
            questionCount: processedQA.length,
            qaPairs: processedQA,
            propertyName
        };
        
    } catch (error) {
        console.error('问答生成失败:', error);
        new Notice(`问答生成失败: ${error.message}`);
        return { success: false, error: error.message };
    }
}

/**
 * 调用问答生成API
 * @param {string} prompt - 生成提示
 * @param {string} token - API密钥
 * @param {string} modelType - 模型类型
 * @returns {Promise<string>} 问答结果
 */
async function callQAAPI(prompt, token, modelType) {
    const requestBody = {
        model: modelType,
        messages: [
            {
                role: "system",
                content: "你是一个专业的教育内容创作专家，擅长基于文章内容设计高质量的问答对。你的问题应该能够帮助读者更好地理解和掌握文章的核心内容�?
            },
            {
                role: "user",
                content: prompt
            }
        ],
        temperature: 0.7,
        max_tokens: 3000
    };
    
    const response = await fetch('https://open.bigmodel.cn/api/paas/v4/chat/completions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
    }
    
    const data = await response.json();
    return data.choices[0].message.content;
}

/**
 * 处理问答对字符串
 * @param {string} qaString - 原始问答字符�? * @returns {Array} 处理后的问答对数�? */
function processQAPairs(qaString) {
    const qaPairs = [];
    
    // 分割问答�?    const sections = qaString.split(/\n\s*\n/);
    
    for (const section of sections) {
        const lines = section.trim().split('\n');
        let question = '';
        let answer = '';
        let isAnswer = false;
        
        for (const line of lines) {
            const trimmedLine = line.trim();
            if (trimmedLine.startsWith('Q:') || trimmedLine.startsWith('�?') || trimmedLine.startsWith('问题:')) {
                question = trimmedLine.replace(/^(Q:|�?|问题:)\s*/, '');
                isAnswer = false;
            } else if (trimmedLine.startsWith('A:') || trimmedLine.startsWith('�?') || trimmedLine.startsWith('答案:')) {
                answer = trimmedLine.replace(/^(A:|�?|答案:)\s*/, '');
                isAnswer = true;
            } else if (isAnswer && trimmedLine) {
                answer += ' ' + trimmedLine;
            } else if (!isAnswer && trimmedLine && !question) {
                question = trimmedLine;
            }
        }
        
        if (question && answer) {
            qaPairs.push({
                question: question.trim(),
                answer: answer.trim()
            });
        }
    }
    
    return qaPairs;
}

/**
 * 分类问答生成（按难度级别�? * @param {string} token - API密钥
 * @param {string} propertyName - 属性名
 * @param {string} difficulty - 难度级别
 * @param {number} questionCount - 问题数量
 * @param {string} modelType - 模型类型
 * @returns {Promise<Object>} 生成结果
 */
async function categorizedQA(token, propertyName, difficulty = 'mixed', questionCount = 5, modelType = 'GLM-4-Flash') {
    const difficultyPrompts = {
        'basic': '基础理解问题，重点考查：基本概念、关键定义、主要事�?,
        'intermediate': '中等难度问题，重点考查：概念关系、逻辑推理、实际应�?,
        'advanced': '高级分析问题，重点考查：深度分析、批判思维、创新应�?,
        'mixed': '混合难度问题，包含基础、中等、高级各个层�?
    };
    
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
        throw new Error('请先打开一个文�?);
    }
    
    const content = await app.vault.read(activeFile);
    const difficultyPrompt = difficultyPrompts[difficulty] || difficultyPrompts['mixed'];
    
    const prompt = `${difficultyPrompt}\n\n基于以下文章内容，生�?{questionCount}�?{difficulty}级别的问答对：\n\n${content}`;
    
    const qaResult = await callQAAPI(prompt, token, modelType);
    const processedQA = processQAPairs(qaResult);
    
    await saveToProperty(activeFile, propertyName, processedQA);
    
    return {
        success: true,
        difficulty,
        questionCount: processedQA.length,
        qaPairs: processedQA,
        propertyName
    };
}

/**
 * 专业领域问答生成
 * @param {string} token - API密钥
 * @param {string} propertyName - 属性名
 * @param {string} domain - 专业领域
 * @param {number} questionCount - 问题数量
 * @param {string} modelType - 模型类型
 * @returns {Promise<Object>} 生成结果
 */
async function domainQA(token, propertyName, domain = 'general', questionCount = 5, modelType = 'GLM-4-Flash') {
    const domainPrompts = {
        'academic': '学术研究问答，重点关注：研究方法、理论框架、实验设计、数据分�?,
        'business': '商业管理问答，重点关注：战略规划、运营管理、市场分析、财务指�?,
        'technical': '技术开发问答，重点关注：技术原理、实现方法、最佳实践、故障排�?,
        'legal': '法律实务问答，重点关注：法条适用、案例分析、程序要求、风险防�?,
        'medical': '医学专业问答，重点关注：诊断要点、治疗方案、预防措施、注意事�?,
        'education': '教育教学问答，重点关注：教学目标、学习要点、能力培养、评估方�?,
        'general': '通用知识问答，重点关注：核心概念、重要信息、实际应�?
    };
    
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
        throw new Error('请先打开一个文�?);
    }
    
    const content = await app.vault.read(activeFile);
    const domainPrompt = domainPrompts[domain] || domainPrompts['general'];
    
    const prompt = `${domainPrompt}\n\n基于以下${domain}领域的文档，生成${questionCount}个专业问答对：\n\n${content}`;
    
    const qaResult = await callQAAPI(prompt, token, modelType);
    const processedQA = processQAPairs(qaResult);
    
    await saveToProperty(activeFile, propertyName, processedQA);
    
    return {
        success: true,
        domain,
        questionCount: processedQA.length,
        qaPairs: processedQA,
        propertyName
    };
}

/**
 * 批量问答生成
 * @param {string} token - API密钥
 * @param {string} filePattern - 文件匹配模式
 * @param {number} questionCount - 问题数量
 * @param {string} modelType - 模型类型
 * @returns {Promise<Object>} 批量生成结果
 */
async function batchQA(token, filePattern, questionCount = 5, modelType = 'GLM-4-Flash') {
    try {
        const vault = app.vault;
        const files = vault.getMarkdownFiles();
        
        const matchedFiles = files.filter(file => {
            if (filePattern === '*.md') return true;
            return file.path.includes(filePattern);
        });
        
        if (matchedFiles.length === 0) {
            throw new Error('没有找到匹配的文�?);
        }
        
        new Notice(`开始批量生成问答，�?${matchedFiles.length} 个文�?..`);
        
        const results = [];
        let successCount = 0;
        
        for (let i = 0; i < matchedFiles.length; i++) {
            const file = matchedFiles[i];
            try {
                console.log(`处理文件 ${i + 1}/${matchedFiles.length}: ${file.name}`);
                
                const content = await vault.read(file);
                if (!content.trim()) continue;
                
                const prompt = `基于以下文章内容，生�?{questionCount}个问答对，格式：Q: 问题\nA: 答案\n\n：\n\n${content}`;
                const qaResult = await callQAAPI(prompt, token, modelType);
                const processedQA = processQAPairs(qaResult);
                
                await saveToProperty(file, 'AI问答', processedQA);
                
                results.push({
                    file: file.name,
                    success: true,
                    questionCount: processedQA.length
                });
                
                successCount++;
                
                // 添加延迟
                await new Promise(resolve => setTimeout(resolve, 2000));
                
            } catch (error) {
                console.error(`处理文件 ${file.name} 失败:`, error);
                results.push({
                    file: file.name,
                    success: false,
                    error: error.message
                });
            }
        }
        
        new Notice(`批量问答生成完成！成功：${successCount}/${matchedFiles.length}`);
        
        return {
            success: true,
            totalFiles: matchedFiles.length,
            successCount,
            results
        };
        
    } catch (error) {
        console.error('批量问答生成失败:', error);
        new Notice(`批量问答生成失败: ${error.message}`);
        return { success: false, error: error.message };
    }
}

/**
 * 保存到文件属�? * @param {Object} file - 文件对象
 * @param {string} propertyName - 属性名
 * @param {Array} qaPairs - 问答对数�? */
async function saveToProperty(file, propertyName, qaPairs) {
    // 格式化问答对为字符串
    const formattedQA = qaPairs.map((qa, index) => 
        `**Q${index + 1}:** ${qa.question}\n\n**A${index + 1}:** ${qa.answer}`
    ).join('\n\n---\n\n');
    
    await app.fileManager.processFrontMatter(file, (frontmatter) => {
        frontmatter[propertyName] = formattedQA;
        frontmatter[`${propertyName}_count`] = qaPairs.length;
    });
}

// 导出函数
module.exports = {
    aiQA,
    categorizedQA,
    domainQA,
    batchQA
};

// 使用说明
console.log(`
AI问答生成脚本已加载！

主要函数�?1. aiQA(token, propertyName, questionCount, modelType)
2. categorizedQA(token, propertyName, difficulty, questionCount, modelType)
3. domainQA(token, propertyName, domain, questionCount, modelType)
4. batchQA(token, filePattern, questionCount, modelType)

难度级别�?- basic: 基础理解
- intermediate: 中等难度
- advanced: 高级分析
- mixed: 混合难度

专业领域�?- academic: 学术研究
- business: 商业管理
- technical: 技术开�?- legal: 法律实务
- medical: 医学专业
- education: 教育教学
- general: 通用知识

使用示例�?// 生成5个问答对
aiQA('your-token', '学习问答', 5)

// 生成基础难度问答
categorizedQA('your-token', '基础问答', 'basic', 3)

// 生成技术领域问�?domainQA('your-token', '技术问�?, 'technical', 4)

// 批量生成问答
batchQA('your-token', '*.md', 3)
`);
