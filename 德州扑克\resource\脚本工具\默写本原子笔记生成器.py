#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
默写本原子笔记生成器
从默写本笔记中提取原子笔记，建立与已有笔记的链接关系
"""

import os
import re
from pathlib import Path
from datetime import datetime
import hashlib

class MemoAtomicNoteGenerator:
    def __init__(self, memo_dir, output_dir):
        self.memo_dir = Path(memo_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.atomic_dir = self.output_dir / "原子笔记"
        self.atomic_dir.mkdir(exist_ok=True)
        
        self.all_atomic_notes = []
        self.existing_notes = {}  # 存储已有笔记的标题和路径
        
    def scan_existing_notes(self):
        """扫描已有的笔记文件"""
        print("扫描已有笔记...")
        
        # 扫描整个笔记系统（排除默写本和输出目录）
        base_dir = self.memo_dir.parent.parent  # 回到obsidian笔记系统目录
        
        for md_file in base_dir.rglob("*.md"):
            # 排除默写本和输出目录
            if (self.memo_dir.name in str(md_file) or 
                self.output_dir.name in str(md_file) or
                '原子笔记' in str(md_file)):
                continue
                
            title = md_file.stem
            self.existing_notes[title] = md_file
        
        print(f"找到 {len(self.existing_notes)} 个已有笔记")
    
    def clean_title(self, title):
        """清理标题"""
        # 去掉序号和特殊符号
        title = re.sub(r'^[\d\.\s]*', '', title)
        title = re.sub(r'^[✅🏆📌⚡️💡🔍🌰❌📊🎯🚀💰⭐️\*\-\#\s]+', '', title)
        title = re.sub(r'[：:]+$', '', title)
        title = title.strip()
        
        # 去掉markdown格式
        title = re.sub(r'\*\*(.*?)\*\*', r'\1', title)
        title = re.sub(r'\*(.*?)\*', r'\1', title)
        
        return title
    
    def extract_atomic_sections(self, content, source_file):
        """提取原子化章节"""
        sections = []
        lines = content.split('\n')
        current_section = None
        current_content = []
        
        for line in lines:
            # 检测标题行（各种格式）
            if (line.startswith('#') or 
                re.match(r'^\d+\.\s*\*\*.*\*\*', line) or  # 数字序号+粗体
                re.match(r'^\*\*\d+\.\s*.*\*\*', line) or  # 粗体数字序号
                re.match(r'^[✅🏆📌⚡️💡🔍🌰❌📊🎯🚀💰⭐️\d\.\s]*[\*\-]*\s*\*\*.*\*\*', line) or
                re.match(r'^[✅🏆📌⚡️💡🔍🌰❌📊🎯🚀💰⭐️\d\.\s]*.*[:：]$', line)):
                
                # 保存上一个章节
                if current_section and current_content:
                    content_text = '\n'.join(current_content).strip()
                    if len(content_text) > 80:  # 只保留有足够内容的章节
                        sections.append({
                            'title': current_section,
                            'content': content_text,
                            'source_file': source_file.name,
                            'source_path': source_file
                        })
                
                # 开始新章节
                raw_title = line.replace('#', '').strip()
                current_section = self.clean_title(raw_title)
                current_content = [line]
                
            else:
                if current_section:
                    current_content.append(line)
        
        # 保存最后一个章节
        if current_section and current_content:
            content_text = '\n'.join(current_content).strip()
            if len(content_text) > 80:
                sections.append({
                    'title': current_section,
                    'content': content_text,
                    'source_file': source_file.name,
                    'source_path': source_file
                })
        
        return sections
    
    def find_related_existing_notes(self, atomic_note):
        """找到与原子笔记相关的已有笔记"""
        related_notes = []
        atomic_content = f"{atomic_note['title']} {atomic_note['content']}"
        
        # 关键词匹配
        keywords = self.extract_keywords(atomic_content)
        
        for existing_title, existing_path in self.existing_notes.items():
            # 计算相关度
            score = 0
            
            # 标题相似度
            if any(word in existing_title for word in atomic_note['title'].split() if len(word) > 2):
                score += 10
            
            # 关键词匹配
            for keyword in keywords:
                if keyword in existing_title:
                    score += 5
            
            if score >= 5:  # 相关度阈值
                related_notes.append((existing_title, existing_path, score))
        
        # 按相关度排序，返回前3个
        related_notes.sort(key=lambda x: x[2], reverse=True)
        return related_notes[:3]
    
    def extract_keywords(self, content):
        """提取关键词"""
        keywords = []
        
        # 常见关键词
        common_keywords = [
            '股权', '融资', '投资', '对赌', '控制权', '董事会', '股东',
            '法律', '合规', '风险', '合同', '协议', '知识产权',
            '财务', '现金流', '成本', '利润', '税务', '审计',
            '战略', '管理', '运营', '市场', '客户', '产品',
            '团队', '领导', '文化', '创新', '变革'
        ]
        
        for keyword in common_keywords:
            if keyword in content:
                keywords.append(keyword)
        
        return keywords
    
    def generate_tags(self, content):
        """生成标签"""
        tags = ['原子笔记', '默写本']
        
        keywords = self.extract_keywords(content)
        
        # 基于关键词生成分类标签
        if any(kw in keywords for kw in ['股权', '融资', '投资', '对赌']):
            tags.append('股权融资')
        if any(kw in keywords for kw in ['法律', '合规', '风险', '合同']):
            tags.append('法律合规')
        if any(kw in keywords for kw in ['财务', '现金流', '成本', '利润']):
            tags.append('财务管理')
        if any(kw in keywords for kw in ['战略', '管理', '运营', '市场']):
            tags.append('战略管理')
        if any(kw in keywords for kw in ['团队', '领导', '文化', '创新']):
            tags.append('组织管理')
        
        return tags
    
    def safe_filename(self, title):
        """生成安全文件名"""
        safe = re.sub(r'[<>:"/\\|?*\n\r\t]', '_', title)
        safe = re.sub(r'[_\s]+', '_', safe)
        safe = safe.strip('_')
        if len(safe) > 40:
            safe = safe[:40]
        return safe if safe else "原子笔记"
    
    def create_atomic_note(self, section, related_notes):
        """创建原子笔记"""
        title = section['title']
        content = section['content']
        source_file = section['source_file']
        
        tags = self.generate_tags(f"{title} {content}")
        
        # 生成原子笔记内容
        note_content = f"""---
title: {title}
source: [[{source_file}]]
tags: {', '.join(f'#{tag}' for tag in tags)}
created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
type: 原子笔记
---

# {title}

{content}

---

## 来源
- 原始笔记: [[{source_file}]]

## 相关笔记
"""
        
        # 添加相关已有笔记的链接
        if related_notes:
            for note_title, note_path, score in related_notes:
                note_content += f"- [[{note_title}]] (相关度: {score})\n"
        else:
            note_content += "- 暂无相关笔记\n"
        
        return note_content
    
    def process_memo_file(self, file_path):
        """处理单个默写本文件"""
        print(f"处理: {file_path.name}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"  读取失败: {e}")
            return
        
        # 提取原子章节
        sections = self.extract_atomic_sections(content, file_path)
        
        if not sections:
            print(f"  未找到原子章节")
            return
        
        print(f"  提取到 {len(sections)} 个原子笔记")
        
        for section in sections:
            # 找到相关的已有笔记
            related_notes = self.find_related_existing_notes(section)
            
            # 创建原子笔记
            atomic_content = self.create_atomic_note(section, related_notes)
            
            # 保存原子笔记
            safe_title = self.safe_filename(section['title'])
            atomic_filename = f"{safe_title}.md"
            atomic_path = self.atomic_dir / atomic_filename
            
            # 避免重名
            counter = 1
            while atomic_path.exists():
                atomic_filename = f"{safe_title}_{counter}.md"
                atomic_path = self.atomic_dir / atomic_filename
                counter += 1
            
            with open(atomic_path, 'w', encoding='utf-8') as f:
                f.write(atomic_content)
            
            self.all_atomic_notes.append({
                'title': section['title'],
                'filename': atomic_filename,
                'source': section['source_file'],
                'related_count': len(related_notes)
            })
            
            print(f"    ✓ {safe_title} (关联 {len(related_notes)} 个笔记)")
    
    def create_index(self):
        """创建索引"""
        index_content = f"""# 默写本原子笔记索引

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
原子笔记数量: {len(self.all_atomic_notes)}

## 统计信息
- 总原子笔记: {len(self.all_atomic_notes)} 个
- 有关联笔记: {len([n for n in self.all_atomic_notes if n['related_count'] > 0])} 个
- 无关联笔记: {len([n for n in self.all_atomic_notes if n['related_count'] == 0])} 个

## 所有原子笔记

"""
        
        # 按来源文件分组
        by_source = {}
        for note in self.all_atomic_notes:
            source = note['source']
            if source not in by_source:
                by_source[source] = []
            by_source[source].append(note)
        
        for source, notes in sorted(by_source.items()):
            index_content += f"### {source}\n"
            for note in notes:
                filename_without_ext = note['filename'][:-3]
                index_content += f"- [[{filename_without_ext}]] - {note['title']} (关联{note['related_count']}个)\n"
            index_content += "\n"
        
        with open(self.output_dir / "原子笔记索引.md", 'w', encoding='utf-8') as f:
            f.write(index_content)
    
    def run(self):
        """运行生成器"""
        print(f"默写本原子笔记生成器")
        print(f"源目录: {self.memo_dir}")
        print(f"输出目录: {self.output_dir}")
        print("=" * 50)
        
        # 扫描已有笔记
        self.scan_existing_notes()
        
        # 处理默写本文件
        print("\n处理默写本文件...")
        md_files = list(self.memo_dir.rglob("*.md"))
        print(f"找到 {len(md_files)} 个默写本文件\n")
        
        for file_path in md_files:
            self.process_memo_file(file_path)
        
        # 创建索引
        print(f"\n创建索引...")
        self.create_index()
        
        print(f"\n✅ 完成！")
        print(f"📝 生成了 {len(self.all_atomic_notes)} 个原子笔记")
        print(f"📁 保存在: {self.atomic_dir}")
        print(f"📊 索引文件: {self.output_dir / '原子笔记索引.md'}")

if __name__ == "__main__":
    # 配置路径
    memo_directory = Path(r"c:\Users\<USER>\OneDrive\obsidian笔记系统\战略与战术\智库辅助手册\默写本")
    output_directory = memo_directory / "原子笔记库"
    
    # 检查默写本目录是否存在
    if not memo_directory.exists():
        print(f"错误：默写本目录不存在 {memo_directory}")
        exit(1)
    
    # 运行生成器
    generator = MemoAtomicNoteGenerator(memo_directory, output_directory)
    generator.run()
