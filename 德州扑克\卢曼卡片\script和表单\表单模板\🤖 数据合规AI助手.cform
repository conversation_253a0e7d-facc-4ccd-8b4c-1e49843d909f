{"id": "data-compliance-ai-assistant", "fields": [{"id": "task-type", "label": "任务类型", "type": "select", "options": [{"id": "compliance-report", "label": "生成合规报告", "value": "生成合规报告"}, {"id": "competitor-analysis", "label": "竞品分析", "value": "竞品分析"}, {"id": "legal-interpretation", "label": "法规解读", "value": "法规解读"}, {"id": "checklist-generation", "label": "生成合规清单", "value": "生成合规清单"}], "defaultValue": "生成合规报告", "description": "选择要执行的AI任务类型"}, {"id": "company-name", "label": "企业名称", "type": "text", "defaultValue": "", "description": "输入企业名称（用于报告生成）"}, {"id": "industry", "label": "行业类型", "type": "select", "options": [{"id": "internet", "label": "互联网科技", "value": "互联网科技"}, {"id": "finance", "label": "金融服务", "value": "金融服务"}, {"id": "ecommerce", "label": "电子商务", "value": "电子商务"}, {"id": "healthcare", "label": "医疗健康", "value": "医疗健康"}], "defaultValue": "互联网科技", "description": "选择行业类型"}, {"id": "content", "label": "详细内容", "type": "textarea", "rows": 8, "defaultValue": "", "description": "输入详细内容：企业情况/竞品信息/法规条款等（可选）"}, {"id": "requirements", "label": "具体要求", "type": "textarea", "rows": 3, "defaultValue": "", "description": "补充具体要求或重点关注的方面"}], "action": {"type": "runScript", "scriptSource": "inline", "code": "async function entry() {\n  const { form } = this.$context;\n\n\n\n  let prompt = '';\n  let fileName = '';\n  let folderName = '';\n\n  switch (form['task-type']) {\n    case '生成合规报告':\n      prompt = `你是专业的数据合规顾问。请为以下企业生成详细的合规报告：\n\n企业名称：${form['company-name'] || '待填写'}\n行业类型：${form.industry}\n企业情况：${form.content || '请根据行业特点生成通用报告'}\n具体要求：${form.requirements || '无特殊要求'}\n\n请生成包含执行摘要、风险评估、合规建议、实施计划的专业报告。`;\n      fileName = `${form['company-name'] || '企业'}-合规报告-${new Date().toISOString().split('T')[0]}.md`;\n      folderName = 'AI合规报告';\n      break;\n\n    case '竞品分析':\n      prompt = `你是商业分析专家。请分析以下竞品信息：\n\n行业背景：${form.industry}\n竞品信息：${form.content || '请根据行业特点进行通用竞品分析'}\n分析要求：${form.requirements || '全面分析'}\n\n请从服务范围、价格策略、竞争优势、薄弱环节等角度进行专业分析。`;\n      fileName = `竞品分析报告-${new Date().toISOString().split('T')[0]}.md`;\n      folderName = 'AI竞品分析';\n      break;\n\n    case '法规解读':\n      prompt = `你是法律专家。请解读以下法规条款：\n\n行业背景：${form.industry}\n法规内容：${form.content || '请根据行业特点解读相关法规'}\n解读要求：${form.requirements || '通俗易懂'}\n\n请提供专业的法规解读，包括核心要点、合规要求、实施建议。`;\n      fileName = `法规解读-${new Date().toISOString().split('T')[0]}.md`;\n      folderName = 'AI法规解读';\n      break;\n\n    case '生成合规清单':\n      prompt = `你是合规专家。请根据以下信息生成合规检查清单：\n\n行业类型：${form.industry}\n具体情况：${form.content || '请生成该行业的标准合规清单'}\n特殊要求：${form.requirements || '标准清单'}\n\n请生成详细的合规检查清单，每项都要具体可操作。`;\n      fileName = `合规清单-${form.industry}-${new Date().toISOString().split('T')[0]}.md`;\n      folderName = 'AI合规清单';\n      break;\n  }\n\n  const aiResult = await callAI(prompt, this.$context);\n  \n  const filePath = `数据合规工作室/${folderName}/${fileName}`;\n  \n  const content = `# ${form['task-type']}结果\n\n**生成时间**: ${new Date().toLocaleString()}\n**任务类型**: ${form['task-type']}\n**行业类型**: ${form.industry}\n**企业名称**: ${form['company-name'] || '未填写'}\n\n---\n\n${aiResult}\n\n---\n*此内容由数据合规AI助手生成*`;\n\n  if (!await this.app.vault.adapter.exists(`数据合规工作室/${folderName}`)) {\n    await this.app.vault.createFolder(`数据合规工作室/${folderName}`);\n  }\n  \n  await this.app.vault.create(filePath, content);\n  \n  return `✅ ${form['task-type']}已完成：${fileName}`;\n}\n\nasync function callAI(prompt, $context) {\n  const { SelectionPopup, fetchJsonStream } = $context;\n  \n  const url = 'https://open.bigmodel.cn/api/paas/v4/chat/completions';\n  const model = 'GLM-4-Flash';\n  const token = 'a6ff22a8cdac4123a9d9a8ea3779009c.aOZZo3lqUQxXgZkN';\n  \n  let text = '';\n  const popup = new SelectionPopup('AI正在处理您的请求...');\n  \n  await fetchJsonStream(url, {\n    method: 'POST',\n    headers: {\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify({\n      model: model,\n      stream: true,\n      messages: [{\n        role: 'user',\n        content: prompt,\n      }],\n    }),\n  }, {\n    onParsedData: (data) => {\n      if (data.choices?.[0]?.delta?.content) {\n        const curr = data.choices[0].delta.content;\n        text += curr;\n        popup.update(text);\n      }\n    },\n  });\n  \n  return text;\n}\n\nexports.default = {\n  entry: entry,\n};"}, "title": "数据合规AI助手"}