#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
默写本知识卡片生成器（基于原脚本改进）
将默写本笔记拆分成原子化知识点，过滤低质量内容
"""

import os
import re
import json
from pathlib import Path
from datetime import datetime
import hashlib

class MemoKnowledgeCardGenerator:
    def __init__(self, source_dir, output_dir):
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        self.cards_dir = self.output_dir / "知识卡片"
        self.index_dir = self.output_dir / "索引"
        self.cards_dir.mkdir(exist_ok=True)
        self.index_dir.mkdir(exist_ok=True)
        
        # 存储所有卡片信息
        self.all_cards = []
        self.processed_content = set()  # 用于去重
        
    def clean_title(self, title):
        """清理标题，去掉序号和特殊符号"""
        title = re.sub(r'^[✅🏆📌⚡️💡🔍🌰❌📊🎯🚀💰⭐️\d\.\s\-\*\#]+', '', title)
        title = re.sub(r'[：:]+$', '', title)
        title = title.strip()
        return title

    def extract_core_keywords(self, title, content):
        """提取核心关键词作为文件名（适配默写本内容）"""
        full_text = f"{title} {content}"
        
        # 默写本相关的核心关键词
        core_keywords = [
            '思维模式', '方法论', '策略思维', '管理思维', '决策框架',
            '商业模式', '营销策略', '客户关系', '团队管理', '领导力',
            '风险控制', '合规管理', '财务管理', '运营优化', '创新思维',
            '沟通技巧', '谈判策略', '时间管理', '目标设定', '执行力',
            '学习方法', '知识管理', '效率提升', '问题解决', '创业思维'
        ]
        
        found_keywords = []
        for keyword in core_keywords:
            if keyword in full_text:
                found_keywords.append(keyword)
        
        if found_keywords:
            return '-'.join(found_keywords[:2])
        
        # 查找重要动作短语
        action_patterns = [
            r'如何[^，。！？]*',
            r'怎么[^，。！？]*', 
            r'为什么[^，。！？]*',
            r'什么是[^，。！？]*',
            r'防止[^，。！？]*',
            r'避免[^，。！？]*',
            r'提升[^，。！？]*',
            r'优化[^，。！？]*'
        ]
        
        for pattern in action_patterns:
            matches = re.findall(pattern, full_text)
            if matches:
                return matches[0][:25]
        
        return self.clean_title(title)[:30]
    
    def is_valuable_content(self, content):
        """判断内容是否有价值（过滤低质量内容）"""
        # 内容长度检查
        if len(content.strip()) < 150:
            return False
            
        # 检查实质内容行数
        lines = [line.strip() for line in content.split('\n') if line.strip()]
        substantial_lines = []
        
        for line in lines:
            # 跳过标题行、分隔符、纯链接行
            if not (line.startswith('#') or 
                   line.startswith('*') or 
                   line in ['---', '===', '***'] or
                   line.startswith('[[') and line.endswith(']]') or
                   line == '相关链接'):
                substantial_lines.append(line)
        
        if len(substantial_lines) < 4:  # 至少4行实质内容
            return False
            
        # 检查是否重复内容
        content_hash = hashlib.md5(content.encode()).hexdigest()
        if content_hash in self.processed_content:
            return False
        
        self.processed_content.add(content_hash)
        return True

    def extract_sections(self, content, file_path):
        """从内容中提取各个章节作为知识点"""
        sections = []
        lines = content.split('\n')
        current_section = None
        current_content = []
        
        for line in lines:
            # 检测标题行
            if (line.startswith('#') or 
                re.match(r'^[✅🏆📌⚡️💡🔍🌰❌📊🎯🚀💰⭐️\d\.\s]*[\*\-]*\s*\*\*.*\*\*', line) or
                re.match(r'^[✅🏆📌⚡️💡🔍🌰❌📊🎯🚀💰⭐️\d\.\s]*.*[:：]$', line)):
                
                # 保存上一个章节（只保留有价值的内容）
                if current_section and current_content:
                    content_text = '\n'.join(current_content).strip()
                    if self.is_valuable_content(content_text):
                        sections.append({
                            'title': current_section,
                            'content': content_text,
                            'source_file': file_path.name
                        })
                
                # 开始新章节
                current_section = self.clean_title(line.replace('#', '').replace('*', '').strip())
                current_content = [line]
                
            else:
                if current_section:
                    current_content.append(line)
        
        # 保存最后一个章节
        if current_section and current_content:
            content_text = '\n'.join(current_content).strip()
            if self.is_valuable_content(content_text):
                sections.append({
                    'title': current_section,
                    'content': content_text,
                    'source_file': file_path.name
                })
        
        return sections

    def extract_links(self, content):
        """提取内容中的链接"""
        internal_links = re.findall(r'\[\[([^\]]+)\]\]', content)
        external_links = re.findall(r'\[([^\]]+)\]\(([^)]+)\)', content)
        
        return {
            'internal_links': internal_links,
            'external_links': external_links
        }

    def generate_tags(self, title, content, source_file):
        """为知识卡片生成标签（适配默写本）"""
        tags = ['默写本']
        
        # 基于内容生成标签
        tag_mapping = {
            '思维': '思维模式',
            '方法': '方法论', 
            '策略': '策略思维',
            '管理': '管理思维',
            '营销': '营销策略',
            '客户': '客户关系',
            '团队': '团队管理',
            '领导': '领导力',
            '风险': '风险管理',
            '财务': '财务管理',
            '运营': '运营管理',
            '创新': '创新思维',
            '沟通': '沟通技巧',
            '谈判': '谈判技巧',
            '时间': '时间管理',
            '目标': '目标管理',
            '学习': '学习方法',
            '知识': '知识管理',
            '效率': '效率提升',
            '问题': '问题解决',
            '创业': '创业思维'
        }
        
        for keyword, tag in tag_mapping.items():
            if keyword in content or keyword in title:
                tags.append(tag)
        
        # 基于标题生成标签
        if any(word in title for word in ['方法', '技巧', '工具']):
            tags.append('实用工具')
        if any(word in title for word in ['思维', '思考', '认知']):
            tags.append('思维模式')
        if any(word in title for word in ['总结', '回顾', '复盘']):
            tags.append('总结复盘')
        
        return list(set(tags))

    def create_knowledge_card(self, section, card_id):
        """创建单个知识卡片"""
        title = section['title']
        content = section['content']
        source_file = section['source_file']
        
        tags = self.generate_tags(title, content, source_file)
        links = self.extract_links(content)
        
        card_metadata = {
            'id': card_id,
            'title': title,
            'source_file': source_file,
            'tags': tags,
            'created_time': datetime.now().isoformat(),
            'internal_links': links['internal_links'],
            'external_links': links['external_links']
        }
        
        card_content = f"""---
id: {card_id}
title: {title}
source: [[{source_file}]]
tags: {', '.join(f'#{tag}' for tag in tags)}
created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
---

# {title}

{content}

---

## 相关链接
- 来源笔记: [[{source_file}]]
"""
        
        if links['internal_links']:
            card_content += "\n## 相关知识点\n"
            for link in links['internal_links']:
                card_content += f"- [[{link}]]\n"
        
        return card_metadata, card_content

    def safe_filename(self, title):
        """生成安全的文件名"""
        safe_title = re.sub(r'[<>:"/\\|?*\n\r\t]', '_', title)
        safe_title = re.sub(r'[_\s]+', '_', safe_title)
        safe_title = safe_title.strip('_')
        if len(safe_title) > 50:
            safe_title = safe_title[:50].rstrip('_')
        if not safe_title:
            safe_title = "未命名知识点"
        return safe_title

    def process_file(self, file_path):
        """处理单个笔记文件"""
        print(f"处理文件: {file_path.name}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"读取文件失败 {file_path}: {e}")
            return
        
        sections = self.extract_sections(content, file_path)
        
        if not sections:
            print(f"  未找到有价值的知识点")
            return
        
        print(f"  提取到 {len(sections)} 个有价值的知识点")
        
        for section in sections:
            card_id = hashlib.md5(f"{file_path.name}_{section['title']}".encode()).hexdigest()[:8]
            metadata, card_content = self.create_knowledge_card(section, card_id)
            
            core_filename = self.extract_core_keywords(section['title'], section['content'])
            safe_title = self.safe_filename(core_filename)
            card_filename = f"{safe_title}.md"
            card_path = self.cards_dir / card_filename
            
            counter = 1
            while card_path.exists():
                card_filename = f"{safe_title}_{counter}.md"
                card_path = self.cards_dir / card_filename
                counter += 1
            
            with open(card_path, 'w', encoding='utf-8') as f:
                f.write(card_content)
            
            self.all_cards.append(metadata)
            print(f"    创建卡片: {safe_title}")

    def create_index_files(self):
        """创建索引文件"""
        # 按标签分类索引
        tags_index = {}
        for card in self.all_cards:
            for tag in card['tags']:
                if tag not in tags_index:
                    tags_index[tag] = []
                tags_index[tag].append(card)
        
        tags_content = "# 默写本知识卡片标签索引\n\n"
        for tag, cards in sorted(tags_index.items()):
            tags_content += f"## #{tag}\n\n"
            for card in cards:
                safe_title = self.safe_filename(self.extract_core_keywords(card['title'], ''))
                tags_content += f"- [[{safe_title}]] - {card['title']}\n"
            tags_content += "\n"
        
        with open(self.index_dir / "标签索引.md", 'w', encoding='utf-8') as f:
            f.write(tags_content)
        
        # 生成总索引
        total_content = f"""# 默写本知识卡片总索引

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
总卡片数: {len(self.all_cards)}

## 所有知识卡片

"""
        for card in sorted(self.all_cards, key=lambda x: x['title']):
            safe_title = self.safe_filename(self.extract_core_keywords(card['title'], ''))
            tags_str = ', '.join(f'#{tag}' for tag in card['tags'])
            total_content += f"- [[{safe_title}]] - {card['title']} ({tags_str})\n"
        
        with open(self.index_dir / "总索引.md", 'w', encoding='utf-8') as f:
            f.write(total_content)

    def run(self):
        """运行主程序"""
        print(f"开始处理默写本目录: {self.source_dir}")
        print(f"输出目录: {self.output_dir}")
        
        md_files = list(self.source_dir.rglob("*.md"))
        print(f"找到 {len(md_files)} 个markdown文件")
        
        for file_path in md_files:
            self.process_file(file_path)
        
        print("\n创建索引文件...")
        self.create_index_files()
        
        print(f"\n完成！共生成 {len(self.all_cards)} 个精炼知识卡片")
        print(f"知识卡片保存在: {self.cards_dir}")
        print(f"索引文件保存在: {self.index_dir}")

if __name__ == "__main__":
    import sys
    
    # 尝试找到默写本文件夹
    possible_paths = [
        r"c:\Users\<USER>\OneDrive\obsidian笔记系统\工作室\读书会\默写本",
        r"c:\Users\<USER>\OneDrive\obsidian笔记系统\默写本",
        "默写本"
    ]
    
    source_directory = None
    for path in possible_paths:
        test_path = Path(path)
        if test_path.exists():
            source_directory = test_path
            break
    
    if source_directory is None:
        print("错误：找不到默写本文件夹")
        print("请手动指定路径：")
        print("python 脚本名.py \"你的默写本文件夹路径\"")
        exit(1)
    
    # 在默写本下创建输出目录
    output_directory = source_directory / "精炼知识库"
    
    print(f"源目录: {source_directory}")
    print(f"输出目录: {output_directory}")
    
    # 创建生成器并运行
    generator = MemoKnowledgeCardGenerator(source_directory, output_directory)
    generator.run()
