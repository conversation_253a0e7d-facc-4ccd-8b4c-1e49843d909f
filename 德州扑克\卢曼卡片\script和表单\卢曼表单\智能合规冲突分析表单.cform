{"id": "smart-compliance-conflict-form", "fields": [{"id": "scenario", "label": "冲突场景类型", "type": "select", "options": [{"id": "data-localization", "label": "数据本地化要求", "value": "数据本地化要求"}, {"id": "cross-border-transfer", "label": "跨境数据传输", "value": "跨境数据传输"}, {"id": "user-consent", "label": "用户同意机制", "value": "用户同意机制"}, {"id": "data-retention", "label": "数据保留期限", "value": "数据保留期限"}, {"id": "privacy-by-design", "label": "隐私设计原则", "value": "隐私设计原则"}, {"id": "data-minimization", "label": "数据最小化原则", "value": "数据最小化原则"}, {"id": "custom", "label": "其他场景", "value": "custom"}], "description": "选择合规冲突的主要场景"}, {"id": "customScenario", "label": "自定义场景（如选择其他）", "type": "text", "description": "当选择其他场景时填写具体内容"}, {"id": "partya", "label": "甲方（法规方）", "type": "select", "options": [{"id": "pipl", "label": "PIPL个人信息保护法", "value": "PIPL个人信息保护法"}, {"id": "dsl", "label": "数据安全法", "value": "数据安全法"}, {"id": "gdpr", "label": "GDPR通用数据保护条例", "value": "GDPR通用数据保护条例"}, {"id": "ccpa", "label": "CCPA加州消费者隐私法", "value": "CCPA加州消费者隐私法"}, {"id": "cybersecurity", "label": "网络安全法", "value": "网络安全法"}, {"id": "industry-regulation", "label": "行业监管规定", "value": "行业监管规定"}], "description": "选择相关的法规依据"}, {"id": "partyaDetail", "label": "具体法条要求", "type": "textarea", "rows": 2, "description": "填写具体的法条内容和要求"}, {"id": "partyb", "label": "乙方（业务方）", "type": "select", "options": [{"id": "global-business", "label": "全球化业务需求", "value": "全球化业务需求"}, {"id": "user-experience", "label": "用户体验优化", "value": "用户体验优化"}, {"id": "operational-efficiency", "label": "运营效率提升", "value": "运营效率提升"}, {"id": "technical-limitation", "label": "技术架构限制", "value": "技术架构限制"}, {"id": "cost-control", "label": "成本控制需求", "value": "成本控制需求"}, {"id": "innovation-requirement", "label": "创新业务需求", "value": "创新业务需求"}], "description": "选择业务需求的主要类型"}, {"id": "partybDetail", "label": "具体业务需求", "type": "textarea", "rows": 2, "description": "填写具体的业务需求和限制"}, {"id": "risk", "label": "法律风险等级", "type": "select", "options": [{"id": "high", "label": "高风险 - 可能面临重大处罚", "value": "高风险"}, {"id": "medium", "label": "中风险 - 存在合规隐患", "value": "中风险"}, {"id": "low", "label": "低风险 - 影响相对较小", "value": "低风险"}], "description": "评估法律风险等级"}, {"id": "impact", "label": "业务影响类型", "type": "select", "options": [{"id": "revenue-impact", "label": "收入影响", "value": "收入影响"}, {"id": "user-impact", "label": "用户体验影响", "value": "用户体验影响"}, {"id": "operational-impact", "label": "运营效率影响", "value": "运营效率影响"}, {"id": "technical-impact", "label": "技术架构影响", "value": "技术架构影响"}, {"id": "market-impact", "label": "市场竞争影响", "value": "市场竞争影响"}], "description": "选择主要的业务影响类型"}, {"id": "impactDetail", "label": "具体影响分析", "type": "textarea", "rows": 2, "description": "详细描述对业务的具体影响"}, {"id": "solution", "label": "平衡方案", "type": "textarea", "rows": 4, "description": "提供可执行的平衡解决方案"}, {"id": "regulations", "label": "相关法规链接", "type": "text", "description": "相关法规文档的链接或标识"}, {"id": "cases", "label": "类似案例链接", "type": "text", "description": "类似案例的链接或标识"}], "action": {"type": "runScript", "scriptSource": "inline", "code": "async function entry() { const { form, app, requestUrl, Notice } = this.$context; try { const finalScenario = form.scenario === 'custom' ? form.customScenario : form.scenario; const baseTemplate = `### 合规战场：${finalScenario}\\n**对阵双方**：\\n- 甲方：${form.partya} - ${form.partyaDetail}\\n- 乙方：${form.partyb} - ${form.partybDetail}\\n**我的合规判断**：\\n- 法律风险评估：${form.risk}\\n- 业务影响分析：${form.impact} - ${form.impactDetail}\\n- 平衡方案：${form.solution}\\n→ 关联：[[${form.regulations}]] [[${form.cases}]]`; let aiEnhancedContent = ''; const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec'; const enhancedPrompt = `你是一位资深的数据合规专家，现在需要对以下合规冲突分析进行深度的认知碰撞和版权化改造。\\n\\n原始分析：\\n${baseTemplate}\\n\\n请你作为专业的合规顾问，与这个分析进行认知碰撞，提供你独特的专业视角：\\n\\n1. **认知挑战**：对原有分析提出质疑或补充，是否存在遗漏的风险点？\\n2. **深度洞察**：基于你的专业经验，这个冲突的本质是什么？\\n3. **创新观点**：结合监管趋势，提出更具前瞻性的解决思路\\n4. **实战经验**：分享类似冲突的实际处理经验和踩坑教训\\n5. **版权化方案**：形成具有独特价值的专业解决方案\\n\\n要求：\\n- 不要简单重复原内容，要有认知碰撞和挑战\\n- 体现专业深度和独特视角\\n- 提供具体可操作的改进建议\\n- 形成有版权价值的专业内容\\n\\n请以资深合规专家的身份，对这个冲突分析进行深度改造和升华。`; try { const aiResponse = await requestUrl({ url: 'https://api.deepseek.com/v1/chat/completions', method: 'POST', headers: { 'Authorization': `Bearer ${apiKey}`, 'Content-Type': 'application/json' }, body: JSON.stringify({ model: 'deepseek-chat', messages: [{ role: 'user', content: enhancedPrompt }], temperature: 0.8, max_tokens: 2000 }) }); if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) { aiEnhancedContent = aiResponse.json.choices[0].message.content; } } catch (e) { aiEnhancedContent = '(AI认知碰撞暂时不可用，请手动补充专业洞察)'; } const template = `${baseTemplate}\\n\\n---\\n\\n## 🧠 专业认知碰撞与版权化改造\\n\\n${aiEnhancedContent}\\n\\n---\\n\\n## 📝 实战应用记录\\n<!-- 记录这个方案在实际项目中的应用效果 -->\\n\\n\\n## 🔄 方案迭代\\n<!-- 记录方案的进一步优化和改进 -->\\n\\n\\n## 🏷️ 标签\\n#合规冲突分析 #${form.risk} #认知碰撞 #版权化方案 #数据合规 #${new Date().toISOString().split('T')[0]}\\n\\n---\\n*生成时间：${new Date().toLocaleString()} | AI认知碰撞：DeepSeek | 版权化改造完成*`; const today = new Date(); const dateStr = today.toISOString().split('T')[0]; const timeStr = today.toTimeString().split(' ')[0].substring(0, 5).replace(':', ''); const fileName = `合规冲突分析-${finalScenario}-${dateStr}-${timeStr}.md`; const filePath = `工作室/肌肉/生成笔记/合规冲突分析/${fileName}`; const folderPath = '工作室/肌肉/生成笔记/合规冲突分析'; const folder = app.vault.getAbstractFileByPath(folderPath); if (!folder) { await app.vault.createFolder(folderPath); } const file = await app.vault.create(filePath, template); new Notice(`合规冲突分析已创建: ${fileName}`); const leaf = app.workspace.getLeaf(); await leaf.openFile(file); return `✅ 合规冲突分析已生成: ${fileName}`; } catch (error) { console.error('生成合规冲突分析失败:', error); new Notice('生成失败: ' + error.message); return '❌ 生成失败，请检查配置'; } } exports.default = { entry: entry };"}, "title": "智能合规冲突分析表单"}