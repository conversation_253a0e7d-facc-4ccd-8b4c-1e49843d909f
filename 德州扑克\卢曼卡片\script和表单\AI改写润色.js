﻿/**
 * AI改写润色脚本
 * 功能：将内容改写成不同风�? * 作者：Builder
 * 版本�?.0
 */

/**
 * AI改写函数
 * @param {string} token - API密钥
 * @param {string} propertyName - 保存改写内容的属性名
 * @param {string} style - 改写风格
 * @param {string} modelType - AI模型类型，默认为'GLM-4-Flash'
 * @returns {Promise<Object>} 改写结果
 */
async function aiRewrite(token, propertyName, style = 'academic', modelType = 'GLM-4-Flash') {
    try {
        // 验证参数
        if (!token || !propertyName) {
            throw new Error('缺少必要参数：token, propertyName');
        }

        console.log(`开始改写：风格=${style}, 属�?${propertyName}`);
        
        // 获取当前文件内容
        const activeFile = app.workspace.getActiveFile();
        if (!activeFile) {
            throw new Error('请先打开一个文�?);
        }
        
        const content = await app.vault.read(activeFile);
        
        if (!content.trim()) {
            throw new Error('文件内容为空，无法进行改�?);
        }
        
        // 构建改写提示
        const prompt = buildRewritePrompt(content, style);
        
        // 调用AI API
        const rewrittenContent = await callRewriteAPI(prompt, token, modelType);
        
        // 处理改写内容
        const processedRewrite = processRewrite(rewrittenContent, content, style);
        
        // 保存改写内容到属�?        await saveToProperty(activeFile, propertyName, processedRewrite);
        
        // 生成改写统计
        const stats = analyzeRewrite(processedRewrite, content);
        
        new Notice(`改写完成！风格：${style}，字数：${stats.newWordCount}`);
        
        return {
            success: true,
            rewrite: processedRewrite,
            stats,
            style,
            propertyName
        };
        
    } catch (error) {
        console.error('改写失败:', error);
        new Notice(`改写失败: ${error.message}`);
        return { success: false, error: error.message };
    }
}

/**
 * 构建改写提示
 * @param {string} content - 原始内容
 * @param {string} style - 改写风格
 * @returns {string} 改写提示
 */
function buildRewritePrompt(content, style) {
    const stylePrompts = {
        'academic': {
            description: '学术风格',
            requirements: [
                '使用正式的学术语言和专业术�?,
                '采用客观、严谨的表达方式',
                '使用第三人称叙述',
                '增加逻辑连接词和过渡�?,
                '引用相关理论或研究（如适用�?,
                '保持论证的严密性和条理�?
            ]
        },
        'business': {
            description: '商务风格',
            requirements: [
                '使用专业的商务语言',
                '突出重点和关键信�?,
                '采用简洁明了的表达',
                '使用数据和事实支撑观�?,
                '体现商业价值和实用�?,
                '保持专业和权威的语调'
            ]
        },
        'casual': {
            description: '口语化风�?,
            requirements: [
                '使用日常生活中的表达方式',
                '采用亲切、自然的语调',
                '使用简单易懂的词汇',
                '增加互动性和亲和�?,
                '可以使用一些口语化的表�?,
                '保持轻松愉快的氛�?
            ]
        },
        'formal': {
            description: '正式文体',
            requirements: [
                '使用标准的书面语言',
                '采用正式的语法结�?,
                '避免口语化表�?,
                '使用完整的句�?,
                '保持庄重和严肃的语调',
                '遵循正式文体的规�?
            ]
        },
        'creative': {
            description: '创意风格',
            requirements: [
                '使用富有想象力的表达',
                '采用生动的比喻和修辞',
                '增加感情色彩和表现力',
                '使用多样化的句式结构',
                '体现独特的视角和观点',
                '保持新颖和吸引力'
            ]
        },
        'technical': {
            description: '技术风�?,
            requirements: [
                '使用准确的技术术�?,
                '采用清晰的逻辑结构',
                '提供具体的技术细�?,
                '使用步骤化的说明',
                '保持客观和精�?,
                '便于技术人员理�?
            ]
        },
        'persuasive': {
            description: '说服性风�?,
            requirements: [
                '使用有说服力的论�?,
                '采用情感和理性相结合的方�?,
                '突出优势和价�?,
                '使用有力的证据支�?,
                '引导读者接受观�?,
                '保持逻辑性和感染�?
            ]
        },
        'narrative': {
            description: '叙事风格',
            requirements: [
                '使用故事化的表达方式',
                '采用时间顺序或逻辑顺序',
                '增加细节描述和场景感',
                '使用生动的语言',
                '保持故事的连贯�?,
                '吸引读者的注意�?
            ]
        }
    };
    
    const styleConfig = stylePrompts[style] || stylePrompts['formal'];
    
    let prompt = `请将以下内容改写�?{styleConfig.description}，改写要求：\n\n`;
    
    styleConfig.requirements.forEach((req, index) => {
        prompt += `${index + 1}. ${req}\n`;
    });
    
    prompt += `\n改写注意事项：\n`;
    prompt += `- 保持原文的核心信息和主要观点\n`;
    prompt += `- 调整语言风格和表达方式\n`;
    prompt += `- 保持内容的完整性和逻辑性\n`;
    prompt += `- 适当调整句式结构和词汇选择\n`;
    prompt += `- 确保改写后的内容符合目标风格\n\n`;
    
    prompt += `原文内容：\n${content}\n\n`;
    prompt += `请按�?{styleConfig.description}进行改写：`;
    
    return prompt;
}

/**
 * 调用改写API
 * @param {string} prompt - 改写提示
 * @param {string} token - API密钥
 * @param {string} modelType - 模型类型
 * @returns {Promise<string>} 改写结果
 */
async function callRewriteAPI(prompt, token, modelType) {
    const requestBody = {
        model: modelType,
        messages: [
            {
                role: "system",
                content: "你是一个专业的文本改写专家，擅长将内容改写成不同的风格，同时保持原文的核心信息和逻辑结构。你能够准确理解各种写作风格的特点，并进行高质量的改写�?
            },
            {
                role: "user",
                content: prompt
            }
        ],
        temperature: 0.7,
        max_tokens: 3000
    };
    
    const response = await fetch('https://open.bigmodel.cn/api/paas/v4/chat/completions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
    }
    
    const data = await response.json();
    return data.choices[0].message.content;
}

/**
 * 处理改写内容
 * @param {string} rewrittenContent - 原始改写内容
 * @param {string} originalContent - 原始内容
 * @param {string} style - 改写风格
 * @returns {Object} 处理后的改写对象
 */
function processRewrite(rewrittenContent, originalContent, style) {
    // 清理改写内容
    let cleanedRewrite = rewrittenContent
        .replace(/^改写[�?]/i, '')
        .replace(/^改写后[�?]/i, '')
        .replace(/^改写结果[�?]/i, '')
        .trim();
    
    return {
        content: cleanedRewrite,
        style,
        originalLength: originalContent.length,
        rewriteLength: cleanedRewrite.length,
        timestamp: new Date().toISOString(),
        raw: rewrittenContent
    };
}

/**
 * 分析改写统计
 * @param {Object} rewrite - 改写对象
 * @param {string} originalContent - 原始内容
 * @returns {Object} 统计信息
 */
function analyzeRewrite(rewrite, originalContent) {
    const originalWordCount = originalContent.length;
    const newWordCount = rewrite.content.length;
    const lengthChange = newWordCount - originalWordCount;
    const lengthChangePercent = Math.round((lengthChange / originalWordCount) * 100);
    
    const originalSentences = originalContent.split(/[。！�?!?]/).filter(s => s.trim()).length;
    const newSentences = rewrite.content.split(/[。！�?!?]/).filter(s => s.trim()).length;
    
    return {
        originalWordCount,
        newWordCount,
        lengthChange,
        lengthChangePercent,
        originalSentences,
        newSentences,
        style: rewrite.style,
        averageWordsPerSentence: Math.round(newWordCount / newSentences)
    };
}

/**
 * 多风格改�? * @param {string} token - API密钥
 * @param {string} propertyName - 属性名
 * @param {Array} styles - 风格数组
 * @param {string} modelType - 模型类型
 * @returns {Promise<Object>} 多风格改写结�? */
async function multiStyleRewrite(token, propertyName, styles = ['academic', 'business', 'casual'], modelType = 'GLM-4-Flash') {
    try {
        const activeFile = app.workspace.getActiveFile();
        if (!activeFile) {
            throw new Error('请先打开一个文�?);
        }
        
        const content = await app.vault.read(activeFile);
        if (!content.trim()) {
            throw new Error('文件内容为空');
        }
        
        new Notice(`开始多风格改写，共 ${styles.length} 种风�?..`);
        
        const rewrites = [];
        
        for (let i = 0; i < styles.length; i++) {
            const style = styles[i];
            try {
                console.log(`改写风格 ${i + 1}/${styles.length}: ${style}`);
                
                const prompt = buildRewritePrompt(content, style);
                const rewrittenContent = await callRewriteAPI(prompt, token, modelType);
                const processed = processRewrite(rewrittenContent, content, style);
                const stats = analyzeRewrite(processed, content);
                
                rewrites.push({
                    style,
                    content: processed.content,
                    stats
                });
                
                // 添加延迟
                if (i < styles.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
                
            } catch (error) {
                console.error(`改写风格 ${style} 失败:`, error);
                rewrites.push({
                    style,
                    error: error.message,
                    success: false
                });
            }
        }
        
        // 保存所有改写结�?        await saveToProperty(activeFile, propertyName, {
            type: 'multi_style_rewrite',
            rewrites,
            timestamp: new Date().toISOString()
        });
        
        const successCount = rewrites.filter(r => !r.error).length;
        new Notice(`多风格改写完成！成功�?{successCount}/${styles.length}`);
        
        return {
            success: true,
            rewrites,
            successCount,
            totalStyles: styles.length,
            propertyName
        };
        
    } catch (error) {
        console.error('多风格改写失�?', error);
        new Notice(`多风格改写失�? ${error.message}`);
        return { success: false, error: error.message };
    }
}

/**
 * 智能改写（基于目标读者）
 * @param {string} token - API密钥
 * @param {string} propertyName - 属性名
 * @param {Object} target - 目标读者信�? * @param {string} modelType - 模型类型
 * @returns {Promise<Object>} 智能改写结果
 */
async function smartRewrite(token, propertyName, target = {}, modelType = 'GLM-4-Flash') {
    const {
        audience = 'general', // general, expert, beginner, student, professional
        purpose = 'inform', // inform, persuade, entertain, educate, sell
        tone = 'neutral', // neutral, friendly, authoritative, enthusiastic, serious
        complexity = 'medium', // simple, medium, complex
        length = 'maintain' // shorter, maintain, longer
    } = target;
    
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
        throw new Error('请先打开一个文�?);
    }
    
    const content = await app.vault.read(activeFile);
    
    const audiencePrompts = {
        'general': '普通读者，使用通俗易懂的语言',
        'expert': '专业人士，可以使用专业术语和深入分析',
        'beginner': '初学者，需要详细解释和简单表�?,
        'student': '学生群体，注重教育性和启发�?,
        'professional': '职场人士，注重实用性和效率'
    };
    
    const purposePrompts = {
        'inform': '传达信息，保持客观和准确',
        'persuade': '说服读者，使用有说服力的论�?,
        'entertain': '娱乐读者，增加趣味性和吸引�?,
        'educate': '教育读者，注重知识传授和理�?,
        'sell': '销售导向，突出价值和优势'
    };
    
    const tonePrompts = {
        'neutral': '中性语调，保持客观',
        'friendly': '友好语调，亲切温�?,
        'authoritative': '权威语调，专业可�?,
        'enthusiastic': '热情语调，充满活�?,
        'serious': '严肃语调，正式庄�?
    };
    
    const complexityPrompts = {
        'simple': '简化表达，使用基础词汇和短�?,
        'medium': '适中复杂度，平衡可读性和深度',
        'complex': '复杂表达，使用高级词汇和复合�?
    };
    
    const lengthPrompts = {
        'shorter': '压缩内容，保留核心信�?,
        'maintain': '保持原有长度',
        'longer': '扩展内容，增加细节和例子'
    };
    
    let prompt = `请根据以下目标读者特征改写内容：\n\n`;
    prompt += `目标读者：${audiencePrompts[audience]}\n`;
    prompt += `写作目的�?{purposePrompts[purpose]}\n`;
    prompt += `语言风格�?{tonePrompts[tone]}\n`;
    prompt += `复杂程度�?{complexityPrompts[complexity]}\n`;
    prompt += `长度要求�?{lengthPrompts[length]}\n\n`;
    
    prompt += `改写要求：\n`;
    prompt += `1. 根据目标读者调整语言难度和表达方式\n`;
    prompt += `2. 根据写作目的调整内容重点和论证方式\n`;
    prompt += `3. 根据语言风格调整语调和措辞\n`;
    prompt += `4. 保持原文的核心信息和逻辑结构\n\n`;
    
    prompt += `原文内容：\n${content}\n\n请进行智能改写：`;
    
    const rewrittenContent = await callRewriteAPI(prompt, token, modelType);
    const processed = processRewrite(rewrittenContent, content, 'smart');
    
    // 添加目标信息
    processed.target = target;
    
    await saveToProperty(activeFile, propertyName, processed);
    
    const stats = analyzeRewrite(processed, content);
    
    return {
        success: true,
        rewrite: processed,
        stats,
        target,
        propertyName
    };
}

/**
 * 批量改写
 * @param {string} token - API密钥
 * @param {string} filePattern - 文件匹配模式
 * @param {string} style - 改写风格
 * @param {string} modelType - 模型类型
 * @returns {Promise<Object>} 批量改写结果
 */
async function batchRewrite(token, filePattern, style = 'formal', modelType = 'GLM-4-Flash') {
    try {
        const vault = app.vault;
        const files = vault.getMarkdownFiles();
        
        const matchedFiles = files.filter(file => {
            if (filePattern === '*.md') return true;
            return file.path.includes(filePattern);
        });
        
        if (matchedFiles.length === 0) {
            throw new Error('没有找到匹配的文�?);
        }
        
        new Notice(`开始批量改写，�?${matchedFiles.length} 个文�?..`);
        
        const results = [];
        let successCount = 0;
        
        for (let i = 0; i < matchedFiles.length; i++) {
            const file = matchedFiles[i];
            try {
                console.log(`处理文件 ${i + 1}/${matchedFiles.length}: ${file.name}`);
                
                const content = await vault.read(file);
                if (!content.trim()) continue;
                
                const prompt = buildRewritePrompt(content, style);
                const rewrittenContent = await callRewriteAPI(prompt, token, modelType);
                const processed = processRewrite(rewrittenContent, content, style);
                
                await saveToProperty(file, 'AI改写', processed);
                
                const stats = analyzeRewrite(processed, content);
                
                results.push({
                    file: file.name,
                    success: true,
                    stats
                });
                
                successCount++;
                
                // 添加延迟
                await new Promise(resolve => setTimeout(resolve, 2000));
                
            } catch (error) {
                console.error(`处理文件 ${file.name} 失败:`, error);
                results.push({
                    file: file.name,
                    success: false,
                    error: error.message
                });
            }
        }
        
        new Notice(`批量改写完成！成功：${successCount}/${matchedFiles.length}`);
        
        return {
            success: true,
            totalFiles: matchedFiles.length,
            successCount,
            style,
            results
        };
        
    } catch (error) {
        console.error('批量改写失败:', error);
        new Notice(`批量改写失败: ${error.message}`);
        return { success: false, error: error.message };
    }
}

/**
 * 保存到文件属�? * @param {Object} file - 文件对象
 * @param {string} propertyName - 属性名
 * @param {Object} rewrite - 改写对象
 */
async function saveToProperty(file, propertyName, rewrite) {
    await app.fileManager.processFrontMatter(file, (frontmatter) => {
        if (typeof rewrite === 'object' && rewrite.content) {
            frontmatter[propertyName] = rewrite.content;
            frontmatter[`${propertyName}_stats`] = rewrite.stats || analyzeRewrite(rewrite, '');
        } else {
            frontmatter[propertyName] = rewrite;
        }
    });
}

// 导出函数
module.exports = {
    aiRewrite,
    multiStyleRewrite,
    smartRewrite,
    batchRewrite
};

// 使用说明
console.log(`
AI改写润色脚本已加载！

主要函数�?1. aiRewrite(token, propertyName, style, modelType)
2. multiStyleRewrite(token, propertyName, styles, modelType)
3. smartRewrite(token, propertyName, target, modelType)
4. batchRewrite(token, filePattern, style, modelType)

改写风格�?- academic: 学术风格
- business: 商务风格
- casual: 口语化风�?- formal: 正式文体
- creative: 创意风格
- technical: 技术风�?- persuasive: 说服性风�?- narrative: 叙事风格

目标读者：
- audience: 'general', 'expert', 'beginner', 'student', 'professional'
- purpose: 'inform', 'persuade', 'entertain', 'educate', 'sell'
- tone: 'neutral', 'friendly', 'authoritative', 'enthusiastic', 'serious'
- complexity: 'simple', 'medium', 'complex'
- length: 'shorter', 'maintain', 'longer'

使用示例�?// 基础改写
aiRewrite('your-token', '改写内容', 'academic')

// 多风格改�?multiStyleRewrite('your-token', '多风格改�?, ['academic', 'business', 'casual'])

// 智能改写
smartRewrite('your-token', '智能改写', {audience: 'beginner', purpose: 'educate', tone: 'friendly'})

// 批量改写
batchRewrite('your-token', '*.md', 'formal')
`);
