async function updatePracticeDate() {
  const { currentFile } = this;
  const file = currentFile;

  if (!file) {
    new Notice("请先打开一个文件");
    return;
  }

  // 获取当前日期
  const today = new Date();
  const dateString = today.toISOString().split('T')[0]; // YYYY-MM-DD格式
  const timeString = today.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit'
  }); // HH:mm格式

  // 更新frontmatter中的练习日期
  await app.fileManager.processFrontMatter(file, (frontmatter) => {
    frontmatter['练习日期'] = dateString;

    // 如果没有其他字段，也可以初始化
    if (!frontmatter['当前状态']) {
      frontmatter['当前状态'] = '';
    }
    if (!frontmatter['流利度']) {
      frontmatter['流利度'] = '';
    }
    if (!frontmatter['练习感受']) {
      frontmatter['练习感受'] = '';
    }
  });

  // 同时更新文件内容中的模板更新时间
  const content = await app.vault.read(file);
  const updatedContent = content.replace(
    /\*模板最后更新:.*\*/g,
    `*模板最后更新: ${dateString} ${timeString}*`
  );

  if (updatedContent !== content) {
    await app.vault.modify(file, updatedContent);
  }

  new Notice(`✅ 练习日期已更新为: ${dateString}`);
}

exports.default = {
  entry: updatePracticeDate,
  name: "updatePracticeDate",
  description: `自动更新当前文件的练习日期为今天

  使用方法：
  \`updatePracticeDate()\`

  功能：
  - 🗓️ 自动将frontmatter中的"练习日期"更新为当天日期
  - 📝 如果其他字段不存在，会自动初始化
  - ✅ 显示更新成功的提示

  适用于口语练习模板等需要记录练习日期的笔记。
  `,
};
