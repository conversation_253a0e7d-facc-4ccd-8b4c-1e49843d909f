/**
 * 批量提取人味模板脚本
 * 功能：将人味模板文件夹中的所有模板文件复制到新的文件夹中
 */

async function batchExtractHumanTemplates() {
    const { app, Notice } = this;
    
    try {
        // 源文件夹路径
        const sourceFolder = '德州扑克/卢曼卡片/script和表单/人味表单/人味模板';
        // 目标文件夹路径
        const targetFolder = '工作室/肌肉/人味模板库';
        
        console.log('开始批量提取人味模板...');
        
        // 获取源文件夹
        const sourceFolderObj = app.vault.getAbstractFileByPath(sourceFolder);
        if (!sourceFolderObj) {
            new Notice('源文件夹不存在: ' + sourceFolder);
            return;
        }
        
        // 创建目标文件夹
        const targetFolderObj = app.vault.getAbstractFileByPath(targetFolder);
        if (!targetFolderObj) {
            await app.vault.createFolder(targetFolder);
            console.log('已创建目标文件夹: ' + targetFolder);
        }
        
        // 获取源文件夹中的所有文件
        const files = app.vault.getMarkdownFiles().filter(file => 
            file.path.startsWith(sourceFolder + '/')
        );
        
        console.log(`找到 ${files.length} 个模板文件`);
        
        let successCount = 0;
        let errorCount = 0;
        
        // 批量复制文件
        for (const file of files) {
            try {
                // 读取源文件内容
                const content = await app.vault.read(file);
                
                // 生成新的文件名（去掉路径，保留原文件名）
                const originalFileName = file.name;
                const newFileName = originalFileName;
                const newFilePath = `${targetFolder}/${newFileName}`;
                
                // 检查目标文件是否已存在
                const existingFile = app.vault.getAbstractFileByPath(newFilePath);
                if (existingFile) {
                    console.log(`文件已存在，跳过: ${newFileName}`);
                    continue;
                }
                
                // 创建新文件
                await app.vault.create(newFilePath, content);
                console.log(`✅ 已复制: ${originalFileName}`);
                successCount++;
                
            } catch (error) {
                console.error(`❌ 复制失败: ${file.name}`, error);
                errorCount++;
            }
        }
        
        // 显示结果
        const message = `批量提取完成！\n成功: ${successCount} 个\n失败: ${errorCount} 个\n目标位置: ${targetFolder}`;
        new Notice(message);
        console.log(message);
        
        // 打开目标文件夹
        const targetFolderFile = app.vault.getAbstractFileByPath(targetFolder);
        if (targetFolderFile) {
            app.workspace.getLeaf().openFile(targetFolderFile);
        }
        
        return message;
        
    } catch (error) {
        console.error('批量提取失败:', error);
        new Notice('批量提取失败: ' + error.message);
        return '❌ 批量提取失败';
    }
}

// 执行脚本
batchExtractHumanTemplates();
