<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>英语合规表达练习器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .main-content {
            padding: 40px;
        }
        
        .control-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .control-group {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border: 2px solid #e9ecef;
        }
        
        .control-group h3 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .icon {
            font-size: 1.5em;
        }
        
        .btn-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        
        .btn.active {
            background: #007bff;
            color: white;
            transform: scale(1.05);
        }
        
        .practice-area {
            background: white;
            border: 3px solid #e9ecef;
            border-radius: 20px;
            padding: 40px;
            margin-top: 30px;
            min-height: 400px;
        }
        
        .scenario-card {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            border-left: 5px solid #ff6b6b;
        }
        
        .scenario-title {
            font-size: 1.5em;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: bold;
        }
        
        .keywords-container {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .keyword-tag {
            display: inline-block;
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #2c3e50;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 20px;
            font-weight: 500;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .keyword-tag:hover {
            border-color: #007bff;
            transform: scale(1.05);
        }
        
        .template-display {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #2196f3;
        }
        
        .template-text {
            font-family: 'Courier New', monospace;
            font-size: 1.1em;
            color: #1565c0;
            line-height: 1.6;
        }
        
        .practice-input {
            width: 100%;
            min-height: 120px;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            font-family: inherit;
            resize: vertical;
            transition: border-color 0.3s ease;
        }
        
        .practice-input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }
        
        .stats-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .hidden {
            display: none;
        }
        
        @media (max-width: 768px) {
            .control-panel {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 英语合规表达练习器</h1>
            <p>基于您的笔记库进行场景化英语练习</p>
        </div>
        
        <div class="main-content">
            <div class="control-panel">
                <!-- 场景选择 -->
                <div class="control-group">
                    <h3><span class="icon">🎭</span>场景选择</h3>
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="selectScenario('random')">🎲 随机场景</button>
                        <button class="btn btn-secondary" onclick="selectScenario('meeting')">🤝 开会场景</button>
                        <button class="btn btn-secondary" onclick="selectScenario('compliance')">⚖️ 合规场景</button>
                        <button class="btn btn-secondary" onclick="selectScenario('technical')">💻 技术场景</button>
                        <button class="btn btn-secondary" onclick="selectScenario('legal')">📜 法律场景</button>
                    </div>
                </div>
                
                <!-- 难度选择 -->
                <div class="control-group">
                    <h3><span class="icon">📊</span>难度等级</h3>
                    <div class="btn-group">
                        <button class="btn btn-secondary" onclick="selectDifficulty('基础')">🟢 基础</button>
                        <button class="btn btn-primary active" onclick="selectDifficulty('中级')">🟡 中级</button>
                        <button class="btn btn-secondary" onclick="selectDifficulty('高级')">🔴 高级</button>
                    </div>
                </div>
                
                <!-- 练习模式 -->
                <div class="control-group">
                    <h3><span class="icon">🎯</span>练习模式</h3>
                    <div class="btn-group">
                        <button class="btn btn-primary active" onclick="selectMode('template')">📝 模板填空</button>
                        <button class="btn btn-secondary" onclick="selectMode('keywords')">🔤 关键词造句</button>
                        <button class="btn btn-secondary" onclick="selectMode('scenario')">🎬 情景对话</button>
                        <button class="btn btn-secondary" onclick="selectMode('correction')">✏️ 错误纠正</button>
                    </div>
                </div>

                <!-- 可选关键词 -->
                <div class="control-group">
                    <h3><span class="icon">🔤</span>可选关键词</h3>
                    <div style="margin-bottom: 15px;">
                        <button class="btn btn-secondary" onclick="toggleKeywordSelection()">
                            <span id="keywordToggleText">🎲 随机关键词</span>
                        </button>
                    </div>
                    <div id="keywordSelection" class="hidden">
                        <div style="margin-bottom: 10px;">
                            <small style="color: #6c757d;">选择您想要练习的关键词类型：</small>
                        </div>
                        <div class="btn-group" style="flex-wrap: wrap;">
                            <button class="btn btn-secondary" onclick="toggleKeywordCategory('连接词')">连接词</button>
                            <button class="btn btn-secondary" onclick="toggleKeywordCategory('时间表达')">时间表达</button>
                            <button class="btn btn-secondary" onclick="toggleKeywordCategory('义务动词')">义务动词</button>
                            <button class="btn btn-secondary" onclick="toggleKeywordCategory('技术术语')">技术术语</button>
                            <button class="btn btn-secondary" onclick="toggleKeywordCategory('法律术语')">法律术语</button>
                            <button class="btn btn-secondary" onclick="toggleKeywordCategory('合规术语')">合规术语</button>
                        </div>
                        <div id="selectedKeywordsDisplay" style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 8px; min-height: 40px;">
                            <small style="color: #6c757d;">已选择的关键词将显示在这里</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 开始练习按钮 -->
            <div style="text-align: center; margin-bottom: 30px;">
                <button class="btn btn-success" onclick="startPractice()" style="font-size: 1.2em; padding: 15px 40px;">
                    🚀 开始练习
                </button>
            </div>
            
            <!-- 练习区域 -->
            <div class="practice-area" id="practiceArea">
                <div style="text-align: center; color: #6c757d; font-size: 1.2em;">
                    <p>🎯 选择您的练习设置，然后点击"开始练习"</p>
                    <p style="margin-top: 10px; font-size: 1em;">基于您的笔记库，智能生成个性化练习内容</p>
                </div>
            </div>
            
            <!-- 统计面板 -->
            <div class="stats-panel">
                <div class="stat-card">
                    <div class="stat-number" id="practiceCount">0</div>
                    <div class="stat-label">今日练习次数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="accuracyRate">0%</div>
                    <div class="stat-label">准确率</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="streakCount">0</div>
                    <div class="stat-label">连续正确</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalTime">0</div>
                    <div class="stat-label">练习时长(分钟)</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentScenario = 'random';
        let currentDifficulty = '中级';
        let currentMode = 'template';
        let practiceData = {};
        let stats = {
            practiceCount: 0,
            correctCount: 0,
            streakCount: 0,
            totalTime: 0,
            startTime: null
        };

        // 真正基于您笔记的数据结构 - 从实际笔记文件提取
        let scenarioData = {};
        let notesLoaded = false;

        // 从您的17个场景短语模板提取的真实数据
        const realTemplateData = {
            "According_to_EDPB": {
                template: "According_to_[法规文件]_we_[行动]_[结果]_therefore_[目标]_even_[条件]",
                difficulty: "高级",
                frequency: "高频",
                tags: ["立法引用", "技术决策", "权威依据"],
                usage_scenario: "引用权威立法文件支撑技术决策时"
            },
            "Although_PIPL_imposes": {
                template: "Although_[法规条款]_imposes_[限制]_[业务目标]_remains_achievable_through_[解决方案]",
                difficulty: "高级",
                frequency: "高频",
                tags: ["让步转折", "PIPL", "跨境传输"],
                usage_scenario: "化解监管与业务的矛盾"
            },
            "Although_PIPL_restricts": {
                template: "Although_[法规]_restricts_[行为]_through_[方法1]_plus_[方法2]_we_not_only_[结果1]_but_also_[结果2]",
                difficulty: "高级",
                frequency: "中频",
                tags: ["风险对冲", "PIPL限制", "离岸处理"],
                usage_scenario: "风险对冲长难句"
            },
            "As_the_EDPB_emphasize": {
                template: "As_the_[指导文件]_emphasize_[主体]_must_[义务]_to_comply_with_[法规条款]",
                difficulty: "高级",
                frequency: "高频",
                tags: ["权威引用", "EDPB", "用户画像"],
                usage_scenario: "引用权威机构文件提升说服力"
            },
            "Based_on_zero_knowledge": {
                template: "Based_on_[技术方案]_[主体]_evolved_from_[原状态]_to_[新状态]",
                difficulty: "高级",
                frequency: "中频",
                tags: ["技术降维", "零知识证明", "身份验证"],
                usage_scenario: "技术降维打击句"
            },
            "By_deploying_PETs": {
                template: "By_deploying_[技术]_[主体]_not_only_[结果1]_but_also_[结果2]_based_on_[技术原理]",
                difficulty: "高级",
                frequency: "高频",
                tags: ["因果链式", "PETs", "数据最小化"],
                usage_scenario: "展现合规如何反哺业务"
            },
            "Data_controllers_shall": {
                template: "[主体]_shall_[义务动词]_[措施]_pursuant_to_[法规条款]",
                difficulty: "高级",
                frequency: "高频",
                tags: ["法律义务", "句式模板", "GDPR条款"],
                usage_scenario: "表述法律义务时的标准模板"
            },
            "Data_subjects_shall_receive": {
                template: "[主体]_shall_[动作]_[内容]_within_[时间限制]_in_accordance_with_[法规条款]",
                difficulty: "高级",
                frequency: "高频",
                tags: ["数据主体权利", "删除权", "GDPR Article 17"],
                usage_scenario: "回应数据主体权利请求时"
            },
            "Following_automated_DSAR": {
                template: "Following_[系统实施]_[指标]_decreased_from_[原数值]_to_[新数值]_fully_complying_with_[法规要求]",
                difficulty: "高级",
                frequency: "高频",
                tags: ["数据量化", "DSAR", "处理时效"],
                usage_scenario: "用数字证明合规效率"
            },
            "If_enterprises_implement": {
                template: "If_[主体]_implement_[法规要求]_then_[风险类型]_will_be_[结果状态]",
                difficulty: "高级",
                frequency: "高频",
                tags: ["条件假设", "DPIA", "风险管理"],
                usage_scenario: "向管理层论证合规投入的必要性"
            },
            "Looking_ahead_to_2024": {
                template: "Looking_ahead_to_[时间]_we_recommend_[建议动作]_to_address_[未来法规]",
                difficulty: "高级",
                frequency: "中频",
                tags: ["前瞻建议", "联邦学习", "数据治理法案"],
                usage_scenario: "显示对立法趋势的前瞻预判"
            },
            "The_primary_compliance_challenge": {
                template: "The_primary_[问题类型]_lies_in_[难点]_especially_when_[场景]",
                difficulty: "中级",
                frequency: "高频",
                tags: ["合规挑战", "问题分析", "句式模板"],
                usage_scenario: "分析合规难点时的标准句式模板"
            },
            "To_address_问题": {
                template: "To_address_[问题]_implementing_[方案]_with_[具体措施]_would_be_advisable",
                difficulty: "中级",
                frequency: "高频",
                tags: ["解决方案", "建议表述", "句式模板"],
                usage_scenario: "提出建议和解决方案时的标准句式模板"
            },
            "Transfers_to_third_countries": {
                template: "[行为]_to_[对象]_without_[条件]_require_[要求]_under_[法规章节]",
                difficulty: "高级",
                frequency: "高频",
                tags: ["跨境传输", "SCCs", "专业模板"],
                usage_scenario: "描述跨境数据传输要求时"
            },
            "Under_法律名称": {
                template: "Under_[法律名称]_[主体]_must/shall_[义务]",
                difficulty: "中级",
                frequency: "高频",
                tags: ["法律要求", "句式模板", "标准格式"],
                usage_scenario: "描述法律要求时的标准句式模板"
            },
            "We_have_deployed_DSAR": {
                template: "We_have_deployed_[系统]_through_[技术手段]_and_achieved_[时间指标]_for_[法规条款]",
                difficulty: "高级",
                frequency: "高频",
                tags: ["三并列句", "DSAR门户", "自动化流程"],
                usage_scenario: "全面展示合规成果的三维价值"
            }
        };

        // 真实变量数据库（从您的变量笔记文件提取）
        const realVariableDatabase = {
            "法律名称": {
                "基础": ["GDPR", "PIPL", "CCPA"],
                "中级": ["Data Security Law", "Cybersecurity Law", "UK DPA", "Privacy Act"],
                "高级": ["ePrivacy Directive", "Digital Services Act", "AI Act", "Data Governance Act", "DESAM"]
            },
            "主体": {
                "基础": ["Data controllers", "Data processors", "Enterprises"],
                "中级": ["Organizations", "Financial institutions", "Healthcare providers", "Educational institutions"],
                "高级": ["Joint controllers", "Data Protection Officer (DPO)", "Supervisory authorities", "Multinational corporations"]
            },
            "义务": {
                "基础": ["implement", "establish", "maintain"],
                "中级": ["protect", "ensure", "monitor", "report", "notify", "document"],
                "高级": ["assess", "audit", "verify", "rectify", "restrict", "appoint", "designate"]
            },
            "技术方案": {
                "基础": ["AES-256 encryption", "TLS encryption", "access controls"],
                "中级": ["multi-factor authentication", "data masking", "tokenization", "pseudonymization"],
                "高级": ["zero-knowledge proofs", "homomorphic encryption", "federated learning", "differential privacy", "secure multi-party computation"]
            },
            "时间": {
                "基础": ["within 72 hours", "within one month", "immediately"],
                "中级": ["without undue delay", "within 30 calendar days", "annually", "quarterly"],
                "高级": ["within 45 days", "at least annually", "every 24 hours", "real-time", "continuously"]
            },
            "条件": {
                "基础": ["explicit consent", "legitimate interests", "contractual necessity"],
                "中级": ["legal obligation", "vital interests", "public task", "adequacy decisions"],
                "高级": ["Standard Contractual Clauses", "Binding Corporate Rules", "certification mechanisms", "substantial public interest"]
            },
            "问题": {
                "基础": ["data breach risks", "compliance gaps", "unauthorized access"],
                "中级": ["regulatory penalties", "cross-border restrictions", "technical limitations", "policy gaps"],
                "高级": ["algorithmic bias", "processing opacity", "architectural complexity", "regulatory fragmentation"]
            },
            "指导文件": {
                "基础": ["GDPR Article", "PIPL Article", "CCPA Section"],
                "中级": ["EDPB Guidelines", "ICO Guidance", "CNIL Guidelines"],
                "高级": ["EDPB 2021 Supplementary Measures Guidelines", "EDPB 2023 Guidelines on Profiling", "NIST Cybersecurity Framework"]
            },
            "法规条款": {
                "基础": ["GDPR Article 6", "GDPR Article 17", "PIPL Article 38"],
                "中级": ["GDPR Article 32", "GDPR Article 35", "CCPA Section 1798.105"],
                "高级": ["GDPR Article 32(1)(a)", "GDPR Article 17(2)", "CCPA Section 1798.105's 45-day response limit"]
            }
        };

        // 可选关键词池（用户可以自选）
        const optionalKeywords = {
            "连接词": ["pursuant to", "in accordance with", "therefore", "furthermore", "moreover", "consequently", "nevertheless"],
            "时间表达": ["within", "by", "no later than", "immediately", "without undue delay", "annually", "quarterly"],
            "义务动词": ["shall", "must", "implement", "ensure", "establish", "maintain", "monitor"],
            "技术术语": ["encryption", "authentication", "authorization", "pseudonymization", "anonymization", "tokenization"],
            "法律术语": ["adequacy decisions", "SCCs", "BCRs", "legitimate interests", "explicit consent", "supervisory authority"],
            "合规术语": ["compliance", "regulatory", "mandatory", "assessment", "audit", "documentation", "remediation"]
        };

        // 用户选择的关键词
        let selectedOptionalKeywords = [];

        // 选择场景
        function selectScenario(scenario) {
            console.log('选择场景:', scenario);
            currentScenario = scenario;
            updateActiveButton('scenario', scenario);
        }

        // 选择难度
        function selectDifficulty(difficulty) {
            console.log('选择难度:', difficulty);
            currentDifficulty = difficulty;
            updateActiveButton('difficulty', difficulty);
        }

        // 选择模式
        function selectMode(mode) {
            console.log('选择模式:', mode);
            currentMode = mode;
            updateActiveButton('mode', mode);
        }

        // 切换关键词选择模式
        function toggleKeywordSelection() {
            const keywordSelection = document.getElementById('keywordSelection');
            const toggleText = document.getElementById('keywordToggleText');

            if (keywordSelection.classList.contains('hidden')) {
                keywordSelection.classList.remove('hidden');
                toggleText.textContent = '🎯 自选关键词';
            } else {
                keywordSelection.classList.add('hidden');
                toggleText.textContent = '🎲 随机关键词';
                selectedOptionalKeywords = [];
                updateSelectedKeywordsDisplay();
            }
        }

        // 切换关键词类别
        function toggleKeywordCategory(category) {
            const button = event.target;
            const keywords = optionalKeywords[category];

            if (button.classList.contains('active')) {
                // 取消选择
                button.classList.remove('active');
                selectedOptionalKeywords = selectedOptionalKeywords.filter(k => !keywords.includes(k));
            } else {
                // 选择
                button.classList.add('active');
                selectedOptionalKeywords = [...new Set([...selectedOptionalKeywords, ...keywords])];
            }

            updateSelectedKeywordsDisplay();
        }

        // 更新已选择关键词显示
        function updateSelectedKeywordsDisplay() {
            const display = document.getElementById('selectedKeywordsDisplay');

            if (selectedOptionalKeywords.length === 0) {
                display.innerHTML = '<small style="color: #6c757d;">已选择的关键词将显示在这里</small>';
            } else {
                display.innerHTML = selectedOptionalKeywords.map(keyword =>
                    `<span class="keyword-tag" style="margin: 2px;">${keyword}</span>`
                ).join('');
            }
        }

        // 根据难度获取变量
        function getVariablesByDifficulty(variableType, difficulty) {
            const variables = realVariableDatabase[variableType];
            if (!variables) return [];

            switch (difficulty) {
                case '基础':
                    return variables['基础'] || [];
                case '中级':
                    return [...(variables['基础'] || []), ...(variables['中级'] || [])];
                case '高级':
                    return [...(variables['基础'] || []), ...(variables['中级'] || []), ...(variables['高级'] || [])];
                default:
                    return variables['中级'] || [];
            }
        }

        // 根据难度过滤模板
        function getTemplatesByDifficulty(difficulty) {
            return Object.values(realTemplateData).filter(template => {
                if (difficulty === '基础') {
                    return template.difficulty === '基础' || template.difficulty === '中级';
                } else if (difficulty === '中级') {
                    return template.difficulty === '中级' || template.difficulty === '高级';
                } else {
                    return template.difficulty === '高级';
                }
            });
        }

        // 更新按钮状态
        function updateActiveButton(type, value) {
            let selector = '';

            // 根据类型确定选择器
            switch(type) {
                case 'scenario':
                    selector = '.control-panel .control-group:nth-child(1) .btn';
                    break;
                case 'difficulty':
                    selector = '.control-panel .control-group:nth-child(2) .btn';
                    break;
                case 'mode':
                    selector = '.control-panel .control-group:nth-child(3) .btn';
                    break;
                default:
                    console.warn('未知的按钮类型:', type);
                    return;
            }

            console.log(`更新${type}按钮状态:`, value, '选择器:', selector);

            // 更新按钮状态
            const buttons = document.querySelectorAll(selector);
            console.log(`找到${buttons.length}个按钮`);

            let activeSet = false;
            buttons.forEach((btn, index) => {
                btn.classList.remove('active');

                // 检查按钮是否匹配当前值
                const btnText = btn.textContent.trim();
                const onclick = btn.getAttribute('onclick') || '';

                console.log(`按钮${index}: "${btnText}", onclick: "${onclick}"`);

                if (btnText.includes(value) || onclick.includes(`'${value}'`)) {
                    btn.classList.add('active');
                    activeSet = true;
                    console.log(`激活按钮${index}: "${btnText}"`);
                }
            });

            if (!activeSet) {
                console.warn(`没有找到匹配的按钮: ${type} = ${value}`);
            }
        }

        // 开始练习
        function startPractice() {
            stats.startTime = Date.now();
            const practiceArea = document.getElementById('practiceArea');
            
            // 生成练习内容
            const content = generatePracticeContent();
            practiceArea.innerHTML = content;
            
            // 更新统计
            stats.practiceCount++;
            updateStats();
        }

        // 生成练习内容（基于真实笔记数据）
        function generatePracticeContent() {
            // 根据难度获取合适的模板
            const availableTemplates = getTemplatesByDifficulty(currentDifficulty);
            if (availableTemplates.length === 0) {
                return '<p>没有找到适合当前难度的模板，请尝试其他难度等级。</p>';
            }

            // 随机选择一个模板
            const selectedTemplate = availableTemplates[Math.floor(Math.random() * availableTemplates.length)];

            // 生成关键词（优先使用用户选择的关键词）
            let keywords = [];
            if (selectedOptionalKeywords.length > 0) {
                keywords = getRandomKeywords(selectedOptionalKeywords, 5);
            } else {
                // 根据模板类型和难度生成关键词
                keywords = generateKeywordsForTemplate(selectedTemplate, currentDifficulty);
            }

            // 生成场景描述
            const scenarioDesc = generateScenarioDescription(selectedTemplate);

            switch (currentMode) {
                case 'template':
                    return generateTemplateMode(selectedTemplate, keywords, scenarioDesc);
                case 'keywords':
                    return generateKeywordsMode(selectedTemplate, keywords, scenarioDesc);
                case 'scenario':
                    return generateScenarioMode(selectedTemplate, scenarioDesc, keywords);
                case 'correction':
                    return generateCorrectionMode(selectedTemplate, scenarioDesc);
                default:
                    return generateTemplateMode(selectedTemplate, keywords, scenarioDesc);
            }
        }

        // 为模板生成相关关键词
        function generateKeywordsForTemplate(template, difficulty) {
            let keywords = [];

            // 根据模板中的变量类型生成关键词
            const templateStr = template.template;

            if (templateStr.includes('[法律名称]') || templateStr.includes('[法规文件]')) {
                keywords.push(...getRandomKeywords(getVariablesByDifficulty('法律名称', difficulty), 2));
            }
            if (templateStr.includes('[主体]')) {
                keywords.push(...getRandomKeywords(getVariablesByDifficulty('主体', difficulty), 2));
            }
            if (templateStr.includes('[义务]') || templateStr.includes('[动作]')) {
                keywords.push(...getRandomKeywords(getVariablesByDifficulty('义务', difficulty), 2));
            }
            if (templateStr.includes('[技术方案]') || templateStr.includes('[技术]')) {
                keywords.push(...getRandomKeywords(getVariablesByDifficulty('技术方案', difficulty), 2));
            }
            if (templateStr.includes('[时间]') || templateStr.includes('[时间限制]')) {
                keywords.push(...getRandomKeywords(getVariablesByDifficulty('时间', difficulty), 1));
            }

            // 添加一些通用的专业词汇
            const generalKeywords = ['pursuant to', 'in accordance with', 'implement', 'ensure', 'compliance'];
            keywords.push(...getRandomKeywords(generalKeywords, 2));

            return [...new Set(keywords)]; // 去重
        }

        // 生成场景描述
        function generateScenarioDescription(template) {
            const scenarios = [
                "向董事会汇报合规状况",
                "与监管机构沟通合规措施",
                "向客户解释数据处理依据",
                "与技术团队讨论安全要求",
                "制定数据保护政策",
                "处理数据主体权利请求",
                "评估跨境数据传输风险",
                "应对数据泄露事件"
            ];

            return scenarios[Math.floor(Math.random() * scenarios.length)];
        }

        // 模板填空模式（基于真实笔记数据）
        function generateTemplateMode(templateData, keywords, scenario) {
            const difficultyColor = {
                '基础': '#28a745',
                '中级': '#ffc107',
                '高级': '#dc3545'
            };

            const difficultyIcon = {
                '基础': '🟢',
                '中级': '🟡',
                '高级': '🔴'
            };

            return `
                <div class="scenario-card">
                    <div class="scenario-title">
                        📝 模板填空练习
                        <span style="color: ${difficultyColor[templateData.difficulty]};">
                            ${difficultyIcon[templateData.difficulty]} ${templateData.difficulty}
                        </span>
                    </div>
                    <p><strong>场景：</strong>${scenario}</p>
                    <p><strong>使用场景：</strong>${templateData.usage_scenario}</p>
                    <p><strong>标签：</strong>${templateData.tags.map(tag => `<span class="keyword-tag" style="font-size: 0.8em;">${tag}</span>`).join(' ')}</p>
                </div>

                <div class="template-display">
                    <div class="template-text">${templateData.template}</div>
                    <div style="margin-top: 10px; font-size: 0.9em; color: #666;">
                        <strong>模板说明：</strong>请将方括号中的变量替换为具体内容
                    </div>
                </div>

                <div class="keywords-container">
                    <h4>💡 建议使用的关键词：</h4>
                    ${keywords.map(k => `<span class="keyword-tag">${k}</span>`).join('')}
                    ${selectedOptionalKeywords.length > 0 ?
                        `<div style="margin-top: 10px;"><small style="color: #007bff;">✨ 包含您选择的关键词</small></div>` :
                        ''
                    }
                </div>

                <div style="margin: 20px 0;">
                    <label for="practiceInput" style="font-weight: bold; margin-bottom: 10px; display: block;">
                        ✍️ 请根据模板结构填写完整的专业句子：
                    </label>
                    <textarea id="practiceInput" class="practice-input"
                              placeholder="请将模板中的变量替换为具体内容，构造一个完整的专业表达..."></textarea>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-success" onclick="checkAnswer()" style="margin-right: 10px;">
                        ✅ 检查答案
                    </button>
                    <button class="btn btn-primary" onclick="startPractice()">
                        🔄 下一题
                    </button>
                    <button class="btn btn-secondary" onclick="showHint('${templateData.template}')">
                        💡 提示
                    </button>
                </div>
            `;
        }

        // 关键词造句模式
        function generateKeywordsMode(title, keywords, scenario) {
            const selectedKeywords = getRandomKeywords(keywords, 3);
            return `
                <div class="scenario-card">
                    <div class="scenario-title">🔤 ${title} - 关键词造句</div>
                    <p><strong>场景：</strong>${scenario}</p>
                </div>
                
                <div class="keywords-container">
                    <h4>🎯 必须使用的关键词：</h4>
                    ${selectedKeywords.map(k => `<span class="keyword-tag" style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);">${k}</span>`).join('')}
                </div>
                
                <div style="margin: 20px 0;">
                    <label for="practiceInput" style="font-weight: bold; margin-bottom: 10px; display: block;">
                        ✍️ 请使用上述关键词造一个完整的句子：
                    </label>
                    <textarea id="practiceInput" class="practice-input" 
                              placeholder="请确保使用所有给定的关键词..."></textarea>
                </div>
                
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-success" onclick="checkKeywordUsage()" style="margin-right: 10px;">
                        ✅ 检查关键词
                    </button>
                    <button class="btn btn-primary" onclick="startPractice()">
                        🔄 下一题
                    </button>
                </div>
            `;
        }

        // 情景对话模式
        function generateScenarioMode(title, scenario, keywords) {
            const situations = [
                "您需要向客户解释数据处理的法律依据",
                "监管机构询问您的合规措施",
                "技术团队需要了解安全要求",
                "董事会要求汇报合规状况",
                "与法律顾问讨论风险评估"
            ];
            const situation = situations[Math.floor(Math.random() * situations.length)];
            
            return `
                <div class="scenario-card">
                    <div class="scenario-title">🎬 ${title} - 情景对话</div>
                    <p><strong>场景：</strong>${scenario}</p>
                    <p><strong>情况：</strong>${situation}</p>
                </div>
                
                <div class="keywords-container">
                    <h4>💼 建议使用的专业词汇：</h4>
                    ${keywords.map(k => `<span class="keyword-tag">${k}</span>`).join('')}
                </div>
                
                <div style="margin: 20px 0;">
                    <label for="practiceInput" style="font-weight: bold; margin-bottom: 10px; display: block;">
                        🗣️ 请写出您的专业回应：
                    </label>
                    <textarea id="practiceInput" class="practice-input" 
                              placeholder="请用专业、准确的英语表达您的观点..."></textarea>
                </div>
                
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-success" onclick="evaluateResponse()" style="margin-right: 10px;">
                        📊 评估回应
                    </button>
                    <button class="btn btn-primary" onclick="startPractice()">
                        🔄 下一题
                    </button>
                </div>
            `;
        }

        // 错误纠正模式
        function generateCorrectionMode(title, template, scenario) {
            const incorrectSentences = [
                "Company must do encryption for protect data",
                "We delete user data when they ask us",
                "Data transfer to USA need permission from government",
                "User can access their data anytime they want",
                "We keep data as long as we need for business"
            ];
            const incorrect = incorrectSentences[Math.floor(Math.random() * incorrectSentences.length)];
            
            return `
                <div class="scenario-card">
                    <div class="scenario-title">✏️ ${title} - 错误纠正</div>
                    <p><strong>场景：</strong>${scenario}</p>
                </div>
                
                <div style="background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #ffc107;">
                    <h4 style="color: #856404; margin-bottom: 10px;">❌ 错误表达：</h4>
                    <p style="font-style: italic; color: #856404; font-size: 1.1em;">"${incorrect}"</p>
                </div>
                
                <div style="margin: 20px 0;">
                    <label for="practiceInput" style="font-weight: bold; margin-bottom: 10px; display: block;">
                        ✅ 请写出正确的专业表达：
                    </label>
                    <textarea id="practiceInput" class="practice-input" 
                              placeholder="请提供准确、专业的英语表达..."></textarea>
                </div>
                
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-success" onclick="checkCorrection()" style="margin-right: 10px;">
                        ✅ 检查纠正
                    </button>
                    <button class="btn btn-primary" onclick="startPractice()">
                        🔄 下一题
                    </button>
                </div>
            `;
        }

        // 获取随机关键词
        function getRandomKeywords(keywords, count) {
            const shuffled = [...keywords].sort(() => 0.5 - Math.random());
            return shuffled.slice(0, Math.min(count, keywords.length));
        }

        // 检查答案
        function checkAnswer() {
            const input = document.getElementById('practiceInput').value.trim();
            if (!input) {
                alert('请先输入您的答案！');
                return;
            }
            
            // 简单的评估逻辑
            const score = evaluateAnswer(input);
            showFeedback(score, input);
            
            if (score >= 70) {
                stats.correctCount++;
                stats.streakCount++;
            } else {
                stats.streakCount = 0;
            }
            
            updateStats();
        }

        // 检查关键词使用
        function checkKeywordUsage() {
            const input = document.getElementById('practiceInput').value.trim();
            if (!input) {
                alert('请先输入您的句子！');
                return;
            }
            
            // 检查关键词使用情况
            const keywords = document.querySelectorAll('.keyword-tag');
            const usedKeywords = [];
            const missedKeywords = [];
            
            keywords.forEach(tag => {
                const keyword = tag.textContent.trim();
                if (input.toLowerCase().includes(keyword.toLowerCase())) {
                    usedKeywords.push(keyword);
                } else {
                    missedKeywords.push(keyword);
                }
            });
            
            showKeywordFeedback(usedKeywords, missedKeywords, input);
        }

        // 评估回应
        function evaluateResponse() {
            const input = document.getElementById('practiceInput').value.trim();
            if (!input) {
                alert('请先输入您的回应！');
                return;
            }
            
            const score = evaluateAnswer(input);
            showDetailedFeedback(score, input);
        }

        // 检查纠正
        function checkCorrection() {
            const input = document.getElementById('practiceInput').value.trim();
            if (!input) {
                alert('请先输入您的纠正！');
                return;
            }
            
            const score = evaluateAnswer(input);
            showCorrectionFeedback(score, input);
        }

        // 评估答案
        function evaluateAnswer(input) {
            let score = 50; // 基础分
            
            // 长度检查
            if (input.length > 20) score += 10;
            if (input.length > 50) score += 10;
            
            // 专业词汇检查
            const professionalTerms = ['pursuant', 'accordance', 'implement', 'ensure', 'compliance', 'regulatory', 'appropriate', 'measures'];
            professionalTerms.forEach(term => {
                if (input.toLowerCase().includes(term)) score += 5;
            });
            
            // 语法结构检查
            if (input.includes('shall') || input.includes('must')) score += 10;
            if (input.includes('Article') || input.includes('GDPR') || input.includes('PIPL')) score += 10;
            
            return Math.min(100, score);
        }

        // 显示反馈
        function showFeedback(score, input) {
            let feedback = '';
            let color = '';
            
            if (score >= 90) {
                feedback = '🎉 优秀！您的表达非常专业和准确！';
                color = '#28a745';
            } else if (score >= 70) {
                feedback = '👍 很好！表达基本准确，可以继续改进。';
                color = '#ffc107';
            } else {
                feedback = '💪 继续努力！建议多使用专业术语和标准句式。';
                color = '#dc3545';
            }
            
            const feedbackDiv = document.createElement('div');
            feedbackDiv.style.cssText = `
                background: ${color}20;
                border: 2px solid ${color};
                color: ${color};
                padding: 15px;
                border-radius: 10px;
                margin-top: 15px;
                font-weight: bold;
            `;
            feedbackDiv.innerHTML = `
                <p>${feedback}</p>
                <p style="margin-top: 10px; font-size: 0.9em;">得分: ${score}/100</p>
            `;
            
            const practiceArea = document.getElementById('practiceArea');
            const existingFeedback = practiceArea.querySelector('.feedback');
            if (existingFeedback) {
                existingFeedback.remove();
            }
            feedbackDiv.className = 'feedback';
            practiceArea.appendChild(feedbackDiv);
        }

        // 显示关键词反馈
        function showKeywordFeedback(used, missed, input) {
            const feedbackDiv = document.createElement('div');
            feedbackDiv.style.cssText = `
                background: #f8f9fa;
                border: 2px solid #007bff;
                padding: 20px;
                border-radius: 10px;
                margin-top: 15px;
            `;
            
            let content = '<h4>📊 关键词使用情况：</h4>';
            
            if (used.length > 0) {
                content += `<p style="color: #28a745; margin: 10px 0;"><strong>✅ 已使用：</strong> ${used.join(', ')}</p>`;
            }
            
            if (missed.length > 0) {
                content += `<p style="color: #dc3545; margin: 10px 0;"><strong>❌ 未使用：</strong> ${missed.join(', ')}</p>`;
            }
            
            const score = Math.round((used.length / (used.length + missed.length)) * 100);
            content += `<p style="margin-top: 15px;"><strong>完成度：${score}%</strong></p>`;
            
            feedbackDiv.innerHTML = content;
            feedbackDiv.className = 'feedback';
            
            const practiceArea = document.getElementById('practiceArea');
            const existingFeedback = practiceArea.querySelector('.feedback');
            if (existingFeedback) {
                existingFeedback.remove();
            }
            practiceArea.appendChild(feedbackDiv);
        }

        // 显示详细反馈
        function showDetailedFeedback(score, input) {
            showFeedback(score, input);
        }

        // 显示纠正反馈
        function showCorrectionFeedback(score, input) {
            showFeedback(score, input);
        }

        // 显示智能提示
        function showHint(template = '') {
            let hints = [];

            // 根据模板生成针对性提示
            if (template) {
                if (template.includes('[法律名称]') || template.includes('[法规文件]')) {
                    hints.push("💡 法律名称示例：GDPR, PIPL, CCPA, Data Security Law");
                }
                if (template.includes('[主体]')) {
                    hints.push("💡 主体示例：Data controllers, Data processors, Enterprises");
                }
                if (template.includes('[义务]') || template.includes('[动作]')) {
                    hints.push("💡 义务动词：shall implement, must establish, should maintain");
                }
                if (template.includes('[技术方案]') || template.includes('[技术]')) {
                    hints.push("💡 技术方案：AES-256 encryption, zero-knowledge proofs, federated learning");
                }
                if (template.includes('[时间]')) {
                    hints.push("💡 时间表达：within 72 hours, without undue delay, annually");
                }
                if (template.includes('[法规条款]')) {
                    hints.push("💡 法规条款：GDPR Article 32, PIPL Article 38, CCPA Section 1798.105");
                }
            }

            // 通用提示
            const generalHints = [
                "💡 使用 'pursuant to' 或 'in accordance with' 来引用法律条款",
                "💡 使用 'shall' 而不是 'must' 来表达法律义务",
                "💡 使用被动语态使表达更加正式",
                "💡 确保句子结构完整，包含主语、谓语、宾语",
                "💡 使用专业术语提升表达的权威性"
            ];

            if (hints.length === 0) {
                hints = generalHints;
            } else {
                hints.push(...generalHints.slice(0, 2)); // 添加一些通用提示
            }

            const hint = hints[Math.floor(Math.random() * hints.length)];

            // 创建更好的提示显示
            const hintDiv = document.createElement('div');
            hintDiv.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                z-index: 1000;
                max-width: 400px;
                text-align: center;
            `;
            hintDiv.innerHTML = `
                <h4 style="margin-bottom: 15px; color: #007bff;">💡 提示</h4>
                <p style="margin-bottom: 20px; line-height: 1.5;">${hint}</p>
                <button onclick="this.parentElement.remove()" class="btn btn-primary">知道了</button>
            `;

            document.body.appendChild(hintDiv);

            // 3秒后自动消失
            setTimeout(() => {
                if (hintDiv.parentElement) {
                    hintDiv.remove();
                }
            }, 5000);
        }

        // 更新统计
        function updateStats() {
            document.getElementById('practiceCount').textContent = stats.practiceCount;
            
            const accuracy = stats.practiceCount > 0 ? 
                Math.round((stats.correctCount / stats.practiceCount) * 100) : 0;
            document.getElementById('accuracyRate').textContent = accuracy + '%';
            
            document.getElementById('streakCount').textContent = stats.streakCount;
            
            if (stats.startTime) {
                const minutes = Math.round((Date.now() - stats.startTime) / 60000);
                document.getElementById('totalTime').textContent = minutes;
            }
        }

        // 初始化函数
        function initializeApp() {
            // 设置默认按钮状态
            updateActiveButton('scenario', 'random');
            updateActiveButton('difficulty', '中级');
            updateActiveButton('mode', 'template');

            // 加载保存的统计数据
            const savedStats = localStorage.getItem('englishPracticeStats');
            if (savedStats) {
                stats = { ...stats, ...JSON.parse(savedStats) };
                updateStats();
            }

            // 定期保存统计数据
            setInterval(() => {
                localStorage.setItem('englishPracticeStats', JSON.stringify(stats));
            }, 30000);

            console.log('🎯 英语练习插件已初始化');
            console.log('当前设置:', { currentScenario, currentDifficulty, currentMode });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });
    </script>
</body>
</html>
