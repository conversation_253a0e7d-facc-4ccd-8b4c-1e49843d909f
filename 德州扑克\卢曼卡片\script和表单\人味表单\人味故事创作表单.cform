{
  "id": "human-touch-story-form",
  "fields": [
    {
      "id": "date",
      "label": "创作日期",
      "type": "text",
      "defaultValue": "{{date:YYYY-MM-DD}}",
      "description": "故事创作的日期"
    },
    {
      "id": "storyType",
      "label": "故事类型",
      "type": "select",
      "options": [
        {"id": "client-encounter", "label": "客户奇遇记", "value": "客户奇遇记"},
        {"id": "industry-observation", "label": "行业众生相", "value": "行业众生相"},
        {"id": "life-metaphor", "label": "生活隐喻故事", "value": "生活隐喻故事"},
        {"id": "childhood-memory", "label": "童年记忆连接", "value": "童年记忆连接"},
        {"id": "family-wisdom", "label": "家庭智慧传承", "value": "家庭智慧传承"},
        {"id": "daily-epiphany", "label": "日常顿悟时刻", "value": "日常顿悟时刻"},
        {"id": "custom", "label": "其他类型", "value": "custom"}
      ],
      "description": "选择人味故事的类型"
    },
    {
      "id": "customStoryType",
      "label": "自定义故事类型（如选择其他）",
      "type": "text",
      "description": "当选择其他类型时填写具体内容"
    },
    {
      "id": "protagonist",
      "label": "主人公",
      "type": "select",
      "options": [
        {"id": "myself", "label": "我自己", "value": "我自己"},
        {"id": "client", "label": "客户", "value": "客户"},
        {"id": "colleague", "label": "同事", "value": "同事"},
        {"id": "family-member", "label": "家人", "value": "家人"},
        {"id": "stranger", "label": "陌生人", "value": "陌生人"},
        {"id": "industry-figure", "label": "行业人物", "value": "行业人物"}
      ],
      "description": "选择故事的主人公"
    },
    {
      "id": "coreConflict",
      "label": "核心冲突",
      "type": "textarea",
      "rows": 3,
      "description": "故事的核心冲突或矛盾点"
    },
    {
      "id": "keyDialogue",
      "label": "关键对话",
      "type": "textarea",
      "rows": 3,
      "description": "故事中最关键的对话内容"
    },
    {
      "id": "emotionalTurning",
      "label": "情感转折点",
      "type": "textarea",
      "rows": 2,
      "description": "故事中的情感转折或高潮时刻"
    },
    {
      "id": "lifeWisdom",
      "label": "生活智慧",
      "type": "textarea",
      "rows": 3,
      "description": "故事要传达的生活智慧或洞察"
    },
    {
      "id": "professionalInsight",
      "label": "专业洞察",
      "type": "textarea",
      "rows": 3,
      "description": "与合规专业相关的洞察或思考"
    },
    {
      "id": "metaphorObject",
      "label": "隐喻物件",
      "type": "select",
      "options": [
        {"id": "food", "label": "食物", "value": "食物"},
        {"id": "plant", "label": "植物", "value": "植物"},
        {"id": "weather", "label": "天气", "value": "天气"},
        {"id": "tool", "label": "工具", "value": "工具"},
        {"id": "animal", "label": "动物", "value": "动物"},
        {"id": "building", "label": "建筑", "value": "建筑"},
        {"id": "book", "label": "书籍", "value": "书籍"}
      ],
      "description": "选择故事中的隐喻物件"
    },
    {
      "id": "sensoryDetail",
      "label": "感官细节",
      "type": "textarea",
      "rows": 2,
      "description": "故事中的感官细节（视觉、听觉、嗅觉、触觉、味觉）"
    },
    {
      "id": "writingTechnique",
      "label": "写作技巧",
      "type": "select",
      "options": [
        {"id": "contrast", "label": "对比反差", "value": "对比反差"},
        {"id": "foreshadowing", "label": "伏笔呼应", "value": "伏笔呼应"},
        {"id": "detail-magnify", "label": "细节放大", "value": "细节放大"},
        {"id": "time-jump", "label": "时空跳跃", "value": "时空跳跃"},
        {"id": "inner-monologue", "label": "内心独白", "value": "内心独白"},
        {"id": "scene-switching", "label": "场景切换", "value": "场景切换"}
      ],
      "description": "选择主要使用的写作技巧"
    },
    {
      "id": "targetEmotion",
      "label": "目标情感",
      "type": "select",
      "options": [
        {"id": "empathy", "label": "共鸣", "value": "共鸣"},
        {"id": "reflection", "label": "反思", "value": "反思"},
        {"id": "warmth", "label": "温暖", "value": "温暖"},
        {"id": "enlightenment", "label": "启发", "value": "启发"},
        {"id": "humor", "label": "幽默", "value": "幽默"},
        {"id": "touching", "label": "感动", "value": "感动"},
        {"id": "surprise", "label": "惊喜", "value": "惊喜"}
      ],
      "description": "希望读者产生的目标情感"
    },
    {
      "id": "applicationPurpose",
      "label": "应用目的",
      "type": "select",
      "options": [
        {"id": "article-opening", "label": "文章开头", "value": "文章开头"},
        {"id": "case-illustration", "label": "案例说明", "value": "案例说明"},
        {"id": "concept-explanation", "label": "概念解释", "value": "概念解释"},
        {"id": "emotional-connection", "label": "情感连接", "value": "情感连接"},
        {"id": "brand-story", "label": "品牌故事", "value": "品牌故事"},
        {"id": "social-content", "label": "社交内容", "value": "社交内容"}
      ],
      "description": "选择故事的主要应用目的"
    }
  ],
  "action": {
    "type": "runScript",
    "scriptSource": "inline",
    "code": "async function entry() {\n  const { form, app, requestUrl, Notice } = this.$context;\n  \n  try {\n    // 调试：打印所有可能的form属性\n    console.log('=== 人味故事表单数据调试 ===');\n    console.log('form对象:', form);\n    console.log('form的所有属性:', Object.keys(form));\n    for (let key in form) {\n      console.log(`${key}: ${form[key]}`);\n    }\n    \n    // 获取表单数据\n    const date = form.date || new Date().toISOString().split('T')[0];\n    const storyType = form.storyType || form['storyType'] || '客户奇遇记';\n    const customStoryType = form.customStoryType || form['customStoryType'] || '';\n    const protagonist = form.protagonist || form['protagonist'] || '我自己';\n    const coreConflict = form.coreConflict || form['coreConflict'] || '核心冲突描述';\n    const keyDialogue = form.keyDialogue || form['keyDialogue'] || '关键对话内容';\n    const emotionalTurning = form.emotionalTurning || form['emotionalTurning'] || '情感转折时刻';\n    const lifeWisdom = form.lifeWisdom || form['lifeWisdom'] || '生活智慧洞察';\n    const professionalInsight = form.professionalInsight || form['professionalInsight'] || '专业洞察思考';\n    const metaphorObject = form.metaphorObject || form['metaphorObject'] || '食物';\n    const sensoryDetail = form.sensoryDetail || form['sensoryDetail'] || '感官细节描述';\n    const writingTechnique = form.writingTechnique || form['writingTechnique'] || '对比反差';\n    const targetEmotion = form.targetEmotion || form['targetEmotion'] || '共鸣';\n    const applicationPurpose = form.applicationPurpose || form['applicationPurpose'] || '文章开头';\n    \n    const finalStoryType = storyType === 'custom' ? (customStoryType || '自定义故事') : storyType;\n    \n    // 生成当前日期（20250803格式）\n    const today = new Date();\n    const year = today.getFullYear();\n    const month = (today.getMonth() + 1).toString().padStart(2, '0');\n    const day = today.getDate().toString().padStart(2, '0');\n    const dateStr = `${year}${month}${day}`;\n    const timeStr = today.getHours().toString().padStart(2, '0') + today.getMinutes().toString().padStart(2, '0');\n    \n    // 使用YAML前置元数据结构\n    const yamlFrontmatter = `---\ndate: ${dateStr}\nstoryType: ${finalStoryType}\nprotagonist: ${protagonist}\nmetaphorObject: ${metaphorObject}\nwritingTechnique: ${writingTechnique}\ntargetEmotion: ${targetEmotion}\napplicationPurpose: ${applicationPurpose}\ntags:\n  - 人味故事\n  - ${finalStoryType}\n  - ${targetEmotion}\n  - ${metaphorObject}\n  - 创作素材\ncreatedBy: 人味故事创作系统\naiModel: DeepSeek\n---`;\n    \n    // 生成基础内容模板\n    const baseTemplate = `# 人味故事：${finalStoryType}\n\n## 🎭 故事框架\n\n**主人公**：${protagonist}\n**隐喻物件**：${metaphorObject}\n**写作技巧**：${writingTechnique}\n**目标情感**：${targetEmotion}\n\n## 📖 故事素材\n\n### 核心冲突\n${coreConflict}\n\n### 关键对话\n> "${keyDialogue}"\n\n### 情感转折点\n${emotionalTurning}\n\n### 感官细节\n${sensoryDetail}\n\n## 💡 洞察提炼\n\n### 生活智慧\n${lifeWisdom}\n\n### 专业洞察\n${professionalInsight}\n\n## 🎯 应用场景\n\n**主要用途**：${applicationPurpose}`;\n\n    // AI人味故事创作\n    let aiEnhancedContent = '';\n    const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec';\n    \n    const enhancedPrompt = `你是一位擅长人味故事创作的作家。请基于以下素材，创作一个有温度、有洞察的人味故事：\n\n故事素材：\n${baseTemplate}\n\n请你运用人味创作技巧，将这些素材编织成一个完整的故事：\n\n1. **故事结构**：运用${writingTechnique}的技巧，构建有张力的故事结构\n2. **人物塑造**：让${protagonist}这个角色立体生动，有血有肉\n3. **细节渲染**：用${sensoryDetail}等感官细节增强故事的画面感\n4. **情感递进**：通过情节推进，最终达到${targetEmotion}的情感效果\n5. **隐喻运用**：巧妙运用${metaphorObject}作为隐喻，连接专业洞察与生活智慧\n6. **对话设计**：让对话自然生动，体现人物性格和故事主题\n\n要求：\n- 故事要有完整的起承转合\n- 语言要有温度和质感，避免AI化表达\n- 专业洞察要自然融入，不生硬说教\n- 结尾要有余味，让人回味\n- 字数控制在800-1200字\n\n请创作一个能够用于${applicationPurpose}的人味故事。`;\n    \n    try {\n      const aiResponse = await requestUrl({\n        url: 'https://api.deepseek.com/v1/chat/completions',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: 'deepseek-chat',\n          messages: [{ role: 'user', content: enhancedPrompt }],\n          temperature: 0.9,\n          max_tokens: 2500\n        })\n      });\n      \n      if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n        aiEnhancedContent = aiResponse.json.choices[0].message.content;\n      }\n    } catch (e) {\n      aiEnhancedContent = '(AI故事创作暂时不可用，请手动补充故事内容)';\n    }\n\n    // 生成完整的markdown内容\n    const fullTemplate = `${yamlFrontmatter}\n\n${baseTemplate}\n\n---\n\n## ✨ AI人味故事创作\n\n${aiEnhancedContent}\n\n---\n\n## 📝 故事应用记录\n\n<!-- 记录这个故事在实际创作中的使用情况 -->\n\n## 🔄 故事迭代优化\n\n<!-- 记录故事的进一步加工和优化 -->\n\n---\n\n*创作时间：${new Date().toLocaleString('zh-CN')} | AI故事创作：DeepSeek | 人味故事库*`;\n    \n    // 生成文件名（使用20250803格式）\n    const fileName = `人味故事-${finalStoryType}-${dateStr}-${timeStr}.md`;\n    const filePath = `工作室/肌肉/生成笔记/人味故事/${fileName}`;\n    \n    // 确保文件夹存在\n    const folderPath = '工作室/肌肉/生成笔记/人味故事';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    // 创建文件\n    const file = await app.vault.create(filePath, fullTemplate);\n    new Notice(`人味故事已创建: ${fileName}`);\n    \n    // 打开新创建的文件\n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 人味故事已生成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('生成人味故事失败:', error);\n    new Notice('生成失败: ' + error.message);\n    return `❌ 生成失败: ${error.message}`;\n  }\n}\n\nexports.default = {\n  entry: entry\n};"
  },
  "title": "人味故事创作表单"
}
