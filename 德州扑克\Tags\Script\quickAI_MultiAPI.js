// 多API支持的快速AI助手 - 支持智谱清言、OpenAI、<PERSON>、通义千问等
async function quickAI(apiKey, promptText, saveOptions = "property", apiProvider = "zhipu", modelName = null) {
  if (!promptText || !apiKey) {
    new Notice("❌ 请设置API密钥和提示词");
    return null;
  }

  const { currentFile } = this;
  const file = currentFile;
  const fileContent = await app.vault.cachedRead(file);
  const title = file.basename;

  // 构建完整提示词
  const fullPrompt = `${promptText}\n\n文件标题：${title}\n文件内容：\n${fileContent}\n\n请直接输出结果，不需要额外解释。`;

  // 显示处理提示
  new Notice(`🤖 ${getProviderName(apiProvider)} 处理中...`, 2000);

  try {
    // 根据API提供商调用不同的API
    const content = await callAPI(apiProvider, apiKey, fullPrompt, modelName);
    
    if (!content) {
      new Notice("❌ AI返回结果为空");
      return null;
    }

    // 根据保存选项处理结果
    await handleSaveOptions(saveOptions, content, file, promptText, apiProvider);

    return content;

  } catch (error) {
    new Notice(`❌ ${getProviderName(apiProvider)} 处理失败: ${error.message}`);
    console.error("AI处理错误:", error);
    return null;
  }
}

// 获取API提供商名称
function getProviderName(provider) {
  const names = {
    'zhipu': '智谱清言',
    'openai': 'OpenAI',
    'claude': 'Claude',
    'qianwen': '通义千问',
    'gemini': 'Gemini',
    'deepseek': 'DeepSeek'
  };
  return names[provider] || provider;
}

// 调用不同的API
async function callAPI(provider, apiKey, prompt, modelName) {
  switch (provider.toLowerCase()) {
    case 'zhipu':
      return await callZhipuAPI(apiKey, prompt, modelName || "GLM-4-Flash");
    
    case 'openai':
      return await callOpenAI(apiKey, prompt, modelName || "gpt-3.5-turbo");
    
    case 'claude':
      return await callClaudeAPI(apiKey, prompt, modelName || "claude-3-haiku-20240307");
    
    case 'qianwen':
      return await callQianwenAPI(apiKey, prompt, modelName || "qwen-turbo");
    
    case 'gemini':
      return await callGeminiAPI(apiKey, prompt, modelName || "gemini-pro");
    
    case 'deepseek':
      return await callDeepSeekAPI(apiKey, prompt, modelName || "deepseek-chat");
    
    default:
      throw new Error(`不支持的API提供商: ${provider}`);
  }
}

// 智谱清言API
async function callZhipuAPI(apiKey, prompt, model) {
  const response = await obsidian.requestUrl({
    method: "POST",
    url: "https://open.bigmodel.cn/api/paas/v4/chat/completions",
    headers: {
      Authorization: `Bearer ${apiKey}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      model: model,
      messages: [{ role: "user", content: prompt }],
      temperature: 0.7,
      max_tokens: 2000,
    }),
  });

  return response.json.choices?.[0]?.message?.content;
}

// OpenAI API
async function callOpenAI(apiKey, prompt, model) {
  const response = await obsidian.requestUrl({
    method: "POST",
    url: "https://api.openai.com/v1/chat/completions",
    headers: {
      Authorization: `Bearer ${apiKey}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      model: model,
      messages: [{ role: "user", content: prompt }],
      temperature: 0.7,
      max_tokens: 2000,
    }),
  });

  return response.json.choices?.[0]?.message?.content;
}

// Claude API (Anthropic)
async function callClaudeAPI(apiKey, prompt, model) {
  const response = await obsidian.requestUrl({
    method: "POST",
    url: "https://api.anthropic.com/v1/messages",
    headers: {
      "x-api-key": apiKey,
      "Content-Type": "application/json",
      "anthropic-version": "2023-06-01"
    },
    body: JSON.stringify({
      model: model,
      max_tokens: 2000,
      messages: [{ role: "user", content: prompt }],
    }),
  });

  return response.json.content?.[0]?.text;
}

// 通义千问API
async function callQianwenAPI(apiKey, prompt, model) {
  const response = await obsidian.requestUrl({
    method: "POST",
    url: "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation",
    headers: {
      Authorization: `Bearer ${apiKey}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      model: model,
      input: {
        messages: [{ role: "user", content: prompt }]
      },
      parameters: {
        temperature: 0.7,
        max_tokens: 2000,
      }
    }),
  });

  return response.json.output?.choices?.[0]?.message?.content;
}

// Gemini API
async function callGeminiAPI(apiKey, prompt, model) {
  const response = await obsidian.requestUrl({
    method: "POST",
    url: `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${apiKey}`,
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      contents: [{
        parts: [{ text: prompt }]
      }],
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 2000,
      }
    }),
  });

  return response.json.candidates?.[0]?.content?.parts?.[0]?.text;
}

// DeepSeek API
async function callDeepSeekAPI(apiKey, prompt, model) {
  const response = await obsidian.requestUrl({
    method: "POST",
    url: "https://api.deepseek.com/v1/chat/completions",
    headers: {
      Authorization: `Bearer ${apiKey}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      model: model,
      messages: [{ role: "user", content: prompt }],
      temperature: 0.7,
      max_tokens: 2000,
    }),
  });

  return response.json.choices?.[0]?.message?.content;
}

// 处理保存选项
async function handleSaveOptions(saveOptions, content, file, promptText, apiProvider) {
  const timestamp = new Date().toLocaleString();
  const providerName = getProviderName(apiProvider);

  switch (saveOptions) {
    case "property":
      app.fileManager.processFrontMatter(file, (frontmatter) => {
        frontmatter["AI结果"] = content.trim();
        frontmatter["处理时间"] = timestamp;
        frontmatter["提示词"] = promptText;
        frontmatter["AI提供商"] = providerName;
      });
      new Notice(`✅ ${providerName} 结果已保存到文件属性`);
      break;

    case "content":
      const currentContent = await app.vault.read(file);
      const newContent = currentContent + `\n\n## ${providerName} 处理结果\n\n**提示词**: ${promptText}\n**处理时间**: ${timestamp}\n\n${content}`;
      await app.vault.modify(file, newContent);
      new Notice(`✅ ${providerName} 结果已追加到文件正文`);
      break;

    case "both":
      // 保存到属性
      app.fileManager.processFrontMatter(file, (frontmatter) => {
        frontmatter["AI结果"] = content.trim();
        frontmatter["处理时间"] = timestamp;
        frontmatter["提示词"] = promptText;
        frontmatter["AI提供商"] = providerName;
      });
      // 保存到正文
      const currentContent2 = await app.vault.read(file);
      const newContent2 = currentContent2 + `\n\n## ${providerName} 处理结果\n\n**提示词**: ${promptText}\n**处理时间**: ${timestamp}\n\n${content}`;
      await app.vault.modify(file, newContent2);
      new Notice(`✅ ${providerName} 结果已同时保存到属性和正文`);
      break;

    case "copy":
      await navigator.clipboard.writeText(content);
      new Notice(`✅ ${providerName} 结果已复制到剪贴板`);
      break;

    case "ask":
      const userChoice = await showSaveOptionsModal(content, providerName);
      if (userChoice && userChoice !== "ask") {
        await handleSaveOptions(userChoice, content, file, promptText, apiProvider);
      }
      break;

    default:
      app.fileManager.processFrontMatter(file, (frontmatter) => {
        frontmatter["AI结果"] = content.trim();
        frontmatter["处理时间"] = timestamp;
        frontmatter["提示词"] = promptText;
        frontmatter["AI提供商"] = providerName;
      });
      new Notice(`✅ ${providerName} 结果已保存到文件属性`);
  }
}

// 显示保存选项模态框
async function showSaveOptionsModal(content, providerName) {
  return new Promise((resolve) => {
    const modal = new obsidian.Modal(app);
    modal.titleEl.setText(`🤖 ${providerName} 处理完成 - 选择保存方式`);

    const container = modal.contentEl.createDiv();

    // 结果预览
    container.createEl("h4", { text: "处理结果预览：" });
    const preview = container.createEl("div", {
      attr: {
        style: "max-height: 200px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; margin: 10px 0; background: #f9f9f9; border-radius: 5px; font-family: monospace; white-space: pre-wrap; font-size: 12px;"
      }
    });
    preview.textContent = content.length > 500 ? content.substring(0, 500) + "..." : content;

    // 保存选项
    container.createEl("h4", { text: "请选择保存方式：" });

    const buttonContainer = container.createDiv();
    buttonContainer.style.display = "grid";
    buttonContainer.style.gridTemplateColumns = "1fr 1fr";
    buttonContainer.style.gap = "8px";
    buttonContainer.style.marginTop = "15px";

    const buttons = [
      { text: "🏷️ 保存到属性", action: "property", style: "background: #4CAF50; color: white;" },
      { text: "📝 保存到正文", action: "content", style: "background: #2196F3; color: white;" },
      { text: "📋 同时保存", action: "both", style: "background: #FF9800; color: white;" },
      { text: "📄 复制到剪贴板", action: "copy", style: "background: #9C27B0; color: white;" }
    ];

    buttons.forEach(btn => {
      const button = buttonContainer.createEl("button", { text: btn.text });
      button.style.cssText = btn.style + " padding: 8px; border: none; border-radius: 5px; cursor: pointer; font-size: 12px; font-weight: bold;";
      button.onclick = () => {
        modal.close();
        resolve(btn.action);
      };
    });

    // 取消按钮
    const cancelContainer = container.createDiv();
    cancelContainer.style.textAlign = "center";
    cancelContainer.style.marginTop = "10px";

    const cancelBtn = cancelContainer.createEl("button", { text: "❌ 取消" });
    cancelBtn.style.cssText = "background: #f44336; color: white; padding: 6px 15px; border: none; border-radius: 5px; cursor: pointer; font-size: 12px;";
    cancelBtn.onclick = () => {
      modal.close();
      resolve(null);
    };

    modal.open();
  });
}

exports.default = {
  entry: quickAI,
  name: "quickAI_MultiAPI",
  description: `多API支持的快速AI助手 - 支持6大主流AI服务商

  🌟 支持的AI服务商：
  - 🇨🇳 智谱清言 (zhipu) - GLM-4-Flash 免费
  - 🇺🇸 OpenAI (openai) - GPT-3.5/GPT-4
  - 🇺🇸 Claude (claude) - Claude-3 系列
  - 🇨🇳 通义千问 (qianwen) - Qwen 系列
  - 🇺🇸 Gemini (gemini) - Google Gemini
  - 🇨🇳 DeepSeek (deepseek) - DeepSeek Chat

  📖 基础用法：
  \`quickAI('API密钥', '提示词')\`                                    // 默认智谱清言
  \`quickAI('API密钥', '提示词', 'property', 'openai')\`              // 使用OpenAI
  \`quickAI('API密钥', '提示词', 'content', 'claude')\`               // 使用Claude
  \`quickAI('API密钥', '提示词', 'both', 'qianwen', 'qwen-plus')\`    // 指定模型

  🎯 参数说明：
  1. apiKey - API密钥
  2. promptText - 提示词
  3. saveOptions - 保存方式 (property/content/both/copy/ask)
  4. apiProvider - API提供商 (zhipu/openai/claude/qianwen/gemini/deepseek)
  5. modelName - 模型名称 (可选，使用默认模型)

  💾 保存选项：
  - "property" - 保存到文件属性（frontmatter）
  - "content" - 追加到文件正文末尾
  - "both" - 同时保存到属性和正文
  - "copy" - 复制到剪贴板
  - "ask" - 弹窗询问用户选择

  🔧 使用示例：
  \`quickAI('sk-xxx', '请总结这篇文章', 'property', 'openai')\`
  \`quickAI('your-key', '翻译成英文', 'content', 'claude')\`
  \`quickAI('api-key', '生成大纲', 'both', 'qianwen')\`
  \`quickAI('token', '提取关键词', 'ask', 'gemini')\`

  ⚙️ 默认模型：
  - 智谱清言: GLM-4-Flash (免费)
  - OpenAI: gpt-3.5-turbo
  - Claude: claude-3-haiku-20240307
  - 通义千问: qwen-turbo
  - Gemini: gemini-pro
  - DeepSeek: deepseek-chat

  💡 使用建议：
  - 智谱清言：免费额度，适合日常使用
  - OpenAI：功能强大，英文处理优秀
  - Claude：逻辑推理能力强，适合分析
  - 通义千问：中文理解好，本土化优势
  - Gemini：多模态能力，Google生态
  - DeepSeek：代码能力强，性价比高

  🔑 API密钥获取：
  - 智谱清言: https://open.bigmodel.cn/
  - OpenAI: https://platform.openai.com/
  - Claude: https://console.anthropic.com/
  - 通义千问: https://dashscope.aliyuncs.com/
  - Gemini: https://makersuite.google.com/
  - DeepSeek: https://platform.deepseek.com/
  `,
};
