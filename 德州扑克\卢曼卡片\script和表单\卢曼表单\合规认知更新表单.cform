{"id": "compliance-cognition-update-form", "fields": [{"id": "date", "label": "日期", "type": "text", "defaultValue": "{{date:YYYY-MM-DD}}", "description": "认知更新的日期"}, {"id": "old-understanding", "label": "被颠覆的旧理解", "type": "textarea", "rows": 3, "description": "原有的合规认知"}, {"id": "new-evidence", "label": "新证据来源", "type": "textarea", "rows": 3, "description": "新法规、判例或监管指导"}, {"id": "update-path", "label": "认知更新路径", "type": "textarea", "rows": 4, "description": "思考过程和更新路径"}, {"id": "practice-adjustment", "label": "实务调整", "type": "textarea", "rows": 3, "description": "需要修正的合规实践"}, {"id": "related-cases", "label": "关联案例", "type": "text", "description": "其他认知更新案例的链接"}], "action": {"id": "generate-compliance-cognition-update", "type": "runScript", "targetFolder": "", "fileName": "", "scriptSource": "inline", "code": "async function entry() {\n  const { form, app, requestUrl, Notice } = this.$context;\n  \n  try {\n    // 生成AI优化的内容\n    let aiEnhancedContent = '';\n    const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec';\n    \n    const prompt = `你是资深的合规专家。请基于以下认知更新信息，提供深度分析：\n\n旧理解：${form['old-understanding']}\n新证据：${form['new-evidence']}\n更新路径：${form['update-path']}\n实务调整：${form['practice-adjustment']}\n\n请提供：\n1. 这次认知更新的深层意义和影响\n2. 可能引发的连锁反应和其他需要调整的认知\n3. 实务操作中的具体注意事项\n4. 如何避免类似的认知盲区\n\n要求：深入、前瞻、实用。`;\n    \n    try {\n      const aiResponse = await requestUrl({\n        url: 'https://api.deepseek.com/v1/chat/completions',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: \"deepseek-chat\",\n          messages: [{ role: \"user\", content: prompt }],\n          temperature: 0.7,\n          max_tokens: 1200\n        })\n      });\n      \n      if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n        aiEnhancedContent = aiResponse.json.choices[0].message.content;\n      }\n    } catch (e) {\n      aiEnhancedContent = '(AI分析暂时不可用，请手动补充深度分析)';\n    }\n    \n    // 生成完整内容\n    const template = `### ${form.date} 合规认知迭代\n**被颠覆的旧理解**：${form['old-understanding']}\n**新证据来源**：${form['new-evidence']}\n**认知更新路径**：${form['update-path']}\n**实务调整**：我需要修正的合规实践${form['practice-adjustment']}\n→ 关联案例：[[${form['related-cases']}]]\n\n---\n\n## 🤖 AI深度分析\n\n${aiEnhancedContent}\n\n---\n\n## 📝 后续跟踪\n<!-- 记录这次认知更新的后续实践效果 -->\n\n\n## 🏷️ 标签\n#合规认知更新 #认知迭代 #数据合规 #${form.date}\n\n---\n*生成时间：${new Date().toLocaleString()} | AI助手：DeepSeek*`;\n    \n    // 创建文件\n    const fileName = `合规认知更新-${form.date}.md`;\n    const filePath = `工作室/肌肉/生成笔记/合规认知更新/${fileName}`;\n    \n    // 检查并创建目录\n    const folderPath = '工作室/肌肉/生成笔记/合规认知更新';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    // 创建文件\n    const file = await app.vault.create(filePath, template);\n    new Notice(`合规认知更新已创建: ${fileName}`);\n    \n    // 打开新创建的文件\n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 合规认知更新已生成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('生成合规认知更新失败:', error);\n    new Notice('生成失败: ' + error.message);\n    return '❌ 生成失败，请检查配置';\n  }\n}\n\nexports.default = {\n  entry: entry,\n};"}, "title": "合规认知更新表单"}