{"id": "smart-compliance-thinking-form", "fields": [{"id": "date", "label": "日期", "type": "text", "defaultValue": "{{date:YYYY-MM-DD}}", "description": "思考日期"}, {"id": "topic", "label": "合规思考主题", "type": "select", "options": [{"id": "data-cross-border", "label": "数据跨境传输合规", "value": "数据跨境传输合规"}, {"id": "privacy-protection", "label": "个人信息保护", "value": "个人信息保护"}, {"id": "data-security", "label": "数据安全管理", "value": "数据安全管理"}, {"id": "consent-mechanism", "label": "用户同意机制", "value": "用户同意机制"}, {"id": "data-processing", "label": "数据处理合规", "value": "数据处理合规"}, {"id": "regulatory-compliance", "label": "监管合规应对", "value": "监管合规应对"}, {"id": "custom", "label": "自定义主题", "value": "custom"}], "description": "选择合规思考的主题类型"}, {"id": "customTopic", "label": "自定义主题（如选择自定义）", "type": "text", "description": "当选择自定义主题时填写"}, {"id": "trigger", "label": "触发事件", "type": "select", "options": [{"id": "new-regulation", "label": "新法规出台", "value": "新法规出台"}, {"id": "regulatory-inquiry", "label": "监管机构询问", "value": "监管机构询问"}, {"id": "business-requirement", "label": "业务需求变化", "value": "业务需求变化"}, {"id": "compliance-incident", "label": "合规事件发生", "value": "合规事件发生"}, {"id": "industry-case", "label": "行业案例启发", "value": "行业案例启发"}, {"id": "technical-change", "label": "技术架构调整", "value": "技术架构调整"}, {"id": "custom", "label": "其他事件", "value": "custom"}], "description": "选择引发思考的触发事件"}, {"id": "customTrigger", "label": "其他触发事件（如选择其他）", "type": "text", "description": "当选择其他事件时填写具体内容"}, {"id": "conflict", "label": "合规冲突类型", "type": "select", "options": [{"id": "law-vs-business", "label": "法规要求 vs 业务需求", "value": "法规要求 vs 业务需求"}, {"id": "privacy-vs-efficiency", "label": "隐私保护 vs 运营效率", "value": "隐私保护 vs 运营效率"}, {"id": "security-vs-usability", "label": "数据安全 vs 用户体验", "value": "数据安全 vs 用户体验"}, {"id": "compliance-vs-innovation", "label": "合规要求 vs 技术创新", "value": "合规要求 vs 技术创新"}, {"id": "local-vs-global", "label": "本地法规 vs 全球化需求", "value": "本地法规 vs 全球化需求"}, {"id": "cost-vs-compliance", "label": "成本控制 vs 合规投入", "value": "成本控制 vs 合规投入"}], "description": "选择主要的合规冲突类型"}, {"id": "precedent1", "label": "相关先例1", "type": "select", "options": [{"id": "gdpr-case", "label": "GDPR相关案例", "value": "GDPR相关案例"}, {"id": "pipl-case", "label": "PIPL相关案例", "value": "PIPL相关案例"}, {"id": "industry-practice", "label": "行业最佳实践", "value": "行业最佳实践"}, {"id": "regulatory-guidance", "label": "监管指导意见", "value": "监管指导意见"}, {"id": "court-decision", "label": "法院判决案例", "value": "法院判决案例"}, {"id": "custom", "label": "其他先例", "value": "custom"}], "description": "选择第一个相关先例类型"}, {"id": "risk", "label": "核心合规风险", "type": "select", "options": [{"id": "data-breach", "label": "数据泄露风险", "value": "数据泄露风险"}, {"id": "regulatory-penalty", "label": "监管处罚风险", "value": "监管处罚风险"}, {"id": "privacy-violation", "label": "隐私侵权风险", "value": "隐私侵权风险"}, {"id": "cross-border-violation", "label": "跨境传输违规", "value": "跨境传输违规"}, {"id": "consent-invalid", "label": "用户同意无效", "value": "用户同意无效"}, {"id": "data-retention", "label": "数据留存违规", "value": "数据留存违规"}, {"id": "access-control", "label": "访问控制缺失", "value": "访问控制缺失"}], "description": "选择核心的合规风险类型"}, {"id": "gap", "label": "现有方案不足", "type": "textarea", "rows": 2, "description": "描述现有解决方案的具体不足之处"}, {"id": "solution", "label": "创新解决方案", "type": "textarea", "rows": 3, "description": "提出的创新解决方案"}, {"id": "steps", "label": "实施步骤", "type": "textarea", "rows": 3, "description": "具体的实施步骤"}, {"id": "control", "label": "风险控制措施", "type": "select", "options": [{"id": "technical-control", "label": "技术控制措施", "value": "技术控制措施"}, {"id": "process-control", "label": "流程控制措施", "value": "流程控制措施"}, {"id": "management-control", "label": "管理控制措施", "value": "管理控制措施"}, {"id": "legal-control", "label": "法律控制措施", "value": "法律控制措施"}, {"id": "monitoring-control", "label": "监控预警措施", "value": "监控预警措施"}], "description": "选择主要的风险控制措施类型"}], "action": {"type": "runScript", "scriptSource": "inline", "code": "async function entry() {\n  const { form, app, requestUrl, Notice } = this.$context;\n  \n  // 调试：打印form对象\n  console.log('Form data:', form);\n  \n  try {\n    // 获取表单数据，提供默认值\n    const date = form.date || new Date().toISOString().split('T')[0];\n    const topic = form.topic || '未填写主题';\n    const customTopic = form.customTopic || '';\n    const trigger = form.trigger || '未填写触发事件';\n    const customTrigger = form.customTrigger || '';\n    const conflict = form.conflict || '未填写冲突';\n    const precedent1 = form.precedent1 || '相关先例1';\n    const risk = form.risk || '未填写风险';\n    const gap = form.gap || '未填写方案不足';\n    const solution = form.solution || '未填写创新方案';\n    const steps = form.steps || '未填写实施步骤';\n    const control = form.control || '未填写控制措施';\n    \n    const finalTopic = topic === 'custom' ? customTopic : topic;\n    const finalTrigger = trigger === 'custom' ? customTrigger : trigger;\n    \n    const baseTemplate = `### ${date}-合规思考：${finalTopic}\n**触发事件**：${finalTrigger}\n- 合规冲突：${conflict}\n- 相关先例：[[${precedent1}]] [[相关案例2]]\n**分析过程**：\n1. 核心合规风险：${risk}\n2. 现有解决方案不足：${gap}\n3. 我的创新方案：${solution}\n**落地验证**：\n- 可实施步骤：${steps}\n- 风险控制点：${control}\n→ 关联：[[后续跟踪]]`;\n\n    let aiEnhancedContent = '';\n    const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec';\n    \n    const enhancedPrompt = `你是一位资深的合规专家，现在需要对以下合规思考内容进行深度的认知碰撞和版权化改造。\n\n原始内容：\n${baseTemplate}\n\n请你作为专业的合规顾问，与这个思考进行认知碰撞，提供你独特的专业视角：\n\n1. **认知挑战**：对原有分析提出质疑或补充，展现不同的专业角度\n2. **深度洞察**：基于你的专业经验，提供更深层的分析和见解\n3. **创新观点**：结合行业趋势，提出具有前瞻性的观点\n4. **实战经验**：分享类似场景的实际处理经验和教训\n5. **版权化思考**：形成具有独特价值的专业判断和建议\n\n要求：\n- 不要简单重复原内容，要有认知碰撞\n- 体现专业深度和独特视角\n- 提供具体可操作的建议\n- 形成有版权价值的专业内容\n\n请以专业顾问的身份，对这个合规思考进行深度改造和升华。`;\n    \n    try {\n      const aiResponse = await requestUrl({\n        url: 'https://api.deepseek.com/v1/chat/completions',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: 'deepseek-chat',\n          messages: [{ role: 'user', content: enhancedPrompt }],\n          temperature: 0.8,\n          max_tokens: 2000\n        })\n      });\n      \n      if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n        aiEnhancedContent = aiResponse.json.choices[0].message.content;\n      }\n    } catch (e) {\n      aiEnhancedContent = '(AI认知碰撞暂时不可用，请手动补充专业洞察)';\n    }\n\n    const template = `${baseTemplate}\n\n---\n\n## 🧠 专业认知碰撞与版权化改造\n\n${aiEnhancedContent}\n\n---\n\n## 📝 实践反馈记录\n<!-- 记录后续实践中的反馈和调整 -->\n\n\n## 🔄 认知迭代\n<!-- 记录认知的进一步演化 -->\n\n\n## 🏷️ 标签\n#每日合规思考 #合规创新 #认知碰撞 #版权化思考 #数据合规\n\n---\n*生成时间：${new Date().toLocaleString()} | AI认知碰撞：DeepSeek | 版权化改造完成*`;\n    \n    // 简化文件名格式\n    const today = new Date();\n    const dateStr = today.getDate().toString().padStart(2, '0'); // 只要日期，如 03\n    const timeStr = today.getHours().toString().padStart(2, '0') + today.getMinutes().toString().padStart(2, '0'); // 如 0922\n    const fileName = `合规思考-${finalTopic}-${dateStr}-${timeStr}.md`;\n    const filePath = `工作室/肌肉/生成笔记/每日合规思考/${fileName}`;\n    \n    const folderPath = '工作室/肌肉/生成笔记/每日合规思考';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    const file = await app.vault.create(filePath, template);\n    new Notice(`每日合规思考已创建: ${fileName}`);\n    \n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 每日合规思考已生成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('生成每日合规思考失败:', error);\n    new Notice('生成失败: ' + error.message);\n    return '❌ 生成失败，请检查配置';\n  }\n}\n\nexports.default = {\n  entry: entry\n};"}, "title": "智能合规思考表单"}