﻿/**
 * AI关键词提取脚�? * 功能：自动提取文章关键词和标�? * 作者：Builder
 * 版本�?.0
 */

/**
 * AI关键词提取函�? * @param {string} token - API密钥
 * @param {string} propertyName - 保存关键词的属性名
 * @param {number} keywordCount - 关键词数量，默认�?0
 * @param {string} modelType - AI模型类型，默认为'GLM-4-Flash'
 * @returns {Promise<Object>} 提取结果
 */
async function aiKeywords(token, propertyName, keywordCount = 10, modelType = 'GLM-4-Flash') {
    try {
        // 验证参数
        if (!token || !propertyName) {
            throw new Error('缺少必要参数：token, propertyName');
        }

        console.log(`开始提取关键词：数�?${keywordCount}, 属�?${propertyName}`);
        
        // 获取当前文件内容
        const activeFile = app.workspace.getActiveFile();
        if (!activeFile) {
            throw new Error('请先打开一个文�?);
        }
        
        const content = await app.vault.read(activeFile);
        
        if (!content.trim()) {
            throw new Error('文件内容为空');
        }
        
        // 构建关键词提取提�?        const prompt = `请从以下文章中提�?{keywordCount}个最重要的关键词，要求：

1. 关键词应该准确反映文章的核心主题和重要概�?2. 优先选择专业术语、核心概念、重要人物、地点、事件等
3. 避免过于通用的词汇（如：的、是、有、等�?4. 关键词应该具有检索价值和分类意义
5. 请按重要性排�?6. 每个关键词用逗号分隔
7. 只返回关键词列表，不要其他解�?
文章内容�?${content}`;
        
        // 调用AI API
        const keywords = await callKeywordAPI(prompt, token, modelType);
        
        // 处理关键�?        const processedKeywords = processKeywords(keywords);
        
        // 保存关键词到属性和标签
        await saveKeywords(activeFile, propertyName, processedKeywords);
        
        new Notice(`关键词提取完成！提取�?${processedKeywords.length} 个关键词`);
        
        return {
            success: true,
            keywordCount: processedKeywords.length,
            keywords: processedKeywords,
            propertyName
        };
        
    } catch (error) {
        console.error('关键词提取失�?', error);
        new Notice(`关键词提取失�? ${error.message}`);
        return { success: false, error: error.message };
    }
}

/**
 * 调用关键词提取API
 * @param {string} prompt - 提取提示
 * @param {string} token - API密钥
 * @param {string} modelType - 模型类型
 * @returns {Promise<string>} 关键词结�? */
async function callKeywordAPI(prompt, token, modelType) {
    const requestBody = {
        model: modelType,
        messages: [
            {
                role: "system",
                content: "你是一个专业的文本分析专家，擅长从文章中提取最有价值的关键词。请准确识别文章的核心概念、重要术语和关键信息点�?
            },
            {
                role: "user",
                content: prompt
            }
        ],
        temperature: 0.3,
        max_tokens: 1000
    };
    
    const response = await fetch('https://open.bigmodel.cn/api/paas/v4/chat/completions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
    }
    
    const data = await response.json();
    return data.choices[0].message.content;
}

/**
 * 处理关键词字符串
 * @param {string} keywordsString - 原始关键词字符串
 * @returns {Array} 处理后的关键词数�? */
function processKeywords(keywordsString) {
    // 清理和分割关键词
    const keywords = keywordsString
        .split(/[,，、\n]/) // 支持多种分隔�?        .map(keyword => keyword.trim())
        .filter(keyword => keyword && keyword.length > 0)
        .filter(keyword => keyword.length <= 20) // 过滤过长的关键词
        .slice(0, 20); // 限制最大数�?    
    // 去重
    return [...new Set(keywords)];
}

/**
 * 保存关键词到文件属性和标签
 * @param {Object} file - 文件对象
 * @param {string} propertyName - 属性名
 * @param {Array} keywords - 关键词数�? */
async function saveKeywords(file, propertyName, keywords) {
    await app.fileManager.processFrontMatter(file, (frontmatter) => {
        // 保存到指定属�?        frontmatter[propertyName] = keywords;
        
        // 同时添加到tags属�?        if (!frontmatter.tags) {
            frontmatter.tags = [];
        }
        
        // 合并关键词到标签，避免重�?        const existingTags = Array.isArray(frontmatter.tags) ? frontmatter.tags : [];
        const newTags = keywords.filter(keyword => !existingTags.includes(keyword));
        frontmatter.tags = [...existingTags, ...newTags];
    });
}

/**
 * 智能关键词提取（基于文档类型�? * @param {string} token - API密钥
 * @param {string} propertyName - 属性名
 * @param {string} documentType - 文档类型
 * @param {string} modelType - 模型类型
 * @returns {Promise<Object>} 提取结果
 */
async function smartKeywords(token, propertyName, documentType = 'general', modelType = 'GLM-4-Flash') {
    const typePrompts = {
        'academic': '学术论文关键词提取，重点关注：研究方法、理论概念、专业术语、研究对�?,
        'business': '商业文档关键词提取，重点关注：业务流程、市场术语、公司名称、产品服�?,
        'technical': '技术文档关键词提取，重点关注：技术栈、编程语言、工具框架、技术概�?,
        'legal': '法律文档关键词提取，重点关注：法条条款、法律术语、案例要素、程序步�?,
        'medical': '医学文档关键词提取，重点关注：疾病名称、医学术语、治疗方法、药物名�?,
        'news': '新闻文档关键词提取，重点关注：人物地点、事件时间、机构组织、关键数�?,
        'general': '通用文档关键词提取，重点关注：核心概念、重要信息、关键术�?
    };
    
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
        throw new Error('请先打开一个文�?);
    }
    
    const content = await app.vault.read(activeFile);
    const typePrompt = typePrompts[documentType] || typePrompts['general'];
    
    const prompt = `${typePrompt}\n\n请从以下${documentType}类型的文档中提取10-15个最重要的关键词：\n\n${content}`;
    
    const keywords = await callKeywordAPI(prompt, token, modelType);
    const processedKeywords = processKeywords(keywords);
    
    await saveKeywords(activeFile, propertyName, processedKeywords);
    
    return {
        success: true,
        documentType,
        keywords: processedKeywords,
        propertyName
    };
}

/**
 * 批量关键词提�? * @param {string} token - API密钥
 * @param {string} filePattern - 文件匹配模式
 * @param {number} keywordCount - 关键词数�? * @param {string} modelType - 模型类型
 * @returns {Promise<Object>} 批量提取结果
 */
async function batchKeywords(token, filePattern, keywordCount = 10, modelType = 'GLM-4-Flash') {
    try {
        const vault = app.vault;
        const files = vault.getMarkdownFiles();
        
        const matchedFiles = files.filter(file => {
            if (filePattern === '*.md') return true;
            return file.path.includes(filePattern);
        });
        
        if (matchedFiles.length === 0) {
            throw new Error('没有找到匹配的文�?);
        }
        
        new Notice(`开始批量提取关键词，共 ${matchedFiles.length} 个文�?..`);
        
        const results = [];
        let successCount = 0;
        
        for (let i = 0; i < matchedFiles.length; i++) {
            const file = matchedFiles[i];
            try {
                console.log(`处理文件 ${i + 1}/${matchedFiles.length}: ${file.name}`);
                
                const content = await vault.read(file);
                if (!content.trim()) continue;
                
                const prompt = `请从以下文章中提�?{keywordCount}个最重要的关键词，用逗号分隔：\n\n${content}`;
                const keywords = await callKeywordAPI(prompt, token, modelType);
                const processedKeywords = processKeywords(keywords);
                
                await saveKeywords(file, 'AI关键�?, processedKeywords);
                
                results.push({
                    file: file.name,
                    success: true,
                    keywords: processedKeywords
                });
                
                successCount++;
                
                // 添加延迟
                await new Promise(resolve => setTimeout(resolve, 1500));
                
            } catch (error) {
                console.error(`处理文件 ${file.name} 失败:`, error);
                results.push({
                    file: file.name,
                    success: false,
                    error: error.message
                });
            }
        }
        
        new Notice(`批量关键词提取完成！成功�?{successCount}/${matchedFiles.length}`);
        
        return {
            success: true,
            totalFiles: matchedFiles.length,
            successCount,
            results
        };
        
    } catch (error) {
        console.error('批量关键词提取失�?', error);
        new Notice(`批量关键词提取失�? ${error.message}`);
        return { success: false, error: error.message };
    }
}

// 导出函数
module.exports = {
    aiKeywords,
    smartKeywords,
    batchKeywords
};

// 使用说明
console.log(`
AI关键词提取脚本已加载�?
主要函数�?1. aiKeywords(token, propertyName, keywordCount, modelType)
2. smartKeywords(token, propertyName, documentType, modelType)
3. batchKeywords(token, filePattern, keywordCount, modelType)

文档类型�?- academic: 学术论文
- business: 商业文档
- technical: 技术文�?- legal: 法律文档
- medical: 医学文档
- news: 新闻文档
- general: 通用文档

使用示例�?// 提取10个关键词
aiKeywords('your-token', '关键�?, 10)

// 智能提取学术论文关键�?smartKeywords('your-token', '学术关键�?, 'academic')

// 批量提取关键�?batchKeywords('your-token', '*.md', 8)
`);
