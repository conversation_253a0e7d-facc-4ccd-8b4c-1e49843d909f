---
title: "计算技能冷却时间与对手行动频率对应"
source: "[[教学法—佐助的战斗计算能力 → 德州扑克的“精准决策”佐助在战斗中计算“伊邪那岐的冷却时间”和“团藏的剩余写轮眼数量”，本质上是一种动态资源管理与概率博弈。]]"
tags: ["技能冷却", "行动频率", "下注频率"]
keywords: ["技能冷却", "下注频率", "无敌状态"]
created: 2025-08-02
type: 原子笔记
---

# 计算技能冷却时间与对手行动频率对应

- 伊邪那岐每颗眼持续约60秒，佐助默算时间，确保在团藏‘无敌状态’结束时攻击。
- 计算对手的‘下注频率’（比如他是否每3手牌就诈唬一次？）。


---

## 元信息
- **来源笔记**: [[教学法—佐助的战斗计算能力 → 德州扑克的“精准决策”佐助在战斗中计算“伊邪那岐的冷却时间”和“团藏的剩余写轮眼数量”，本质上是一种动态资源管理与概率博弈。]]
- **创建时间**: 2025/8/3 07:31:00
- **标签**: #技能冷却 #行动频率 #下注频率
- **关键词**: 技能冷却, 下注频率, 无敌状态

## 相关链接
- 返回原笔记: [[教学法—佐助的战斗计算能力 → 德州扑克的“精准决策”佐助在战斗中计算“伊邪那岐的冷却时间”和“团藏的剩余写轮眼数量”，本质上是一种动态资源管理与概率博弈。]]
