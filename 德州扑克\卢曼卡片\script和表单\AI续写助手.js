﻿/**
 * AI续写助手脚本
 * 功能：基于现有内容智能续�? * 作者：Builder
 * 版本�?.0
 */

/**
 * AI续写函数
 * @param {string} token - API密钥
 * @param {string} propertyName - 保存续写内容的属性名
 * @param {number} continueLength - 续写长度（字数），默认为500
 * @param {string} modelType - AI模型类型，默认为'GLM-4-Flash'
 * @returns {Promise<Object>} 续写结果
 */
async function aiContinue(token, propertyName, continueLength = 500, modelType = 'GLM-4-Flash') {
    try {
        // 验证参数
        if (!token || !propertyName) {
            throw new Error('缺少必要参数：token, propertyName');
        }

        console.log(`开始续写：长度=${continueLength}�? 属�?${propertyName}`);
        
        // 获取当前文件内容
        const activeFile = app.workspace.getActiveFile();
        if (!activeFile) {
            throw new Error('请先打开一个文�?);
        }
        
        const content = await app.vault.read(activeFile);
        
        if (!content.trim()) {
            throw new Error('文件内容为空，无法进行续�?);
        }
        
        // 分析内容类型和风�?        const contentAnalysis = analyzeContentStyle(content);
        
        // 构建续写提示
        const prompt = buildContinuePrompt(content, continueLength, contentAnalysis);
        
        // 调用AI API
        const continuation = await callContinueAPI(prompt, token, modelType);
        
        // 处理续写内容
        const processedContinuation = processContinuation(continuation, content);
        
        // 保存续写内容到属�?        await saveToProperty(activeFile, propertyName, processedContinuation);
        
        // 生成续写统计
        const stats = analyzeContinuation(processedContinuation);
        
        new Notice(`续写完成！生�?${stats.wordCount} 字，${stats.paragraphCount} 段`);
        
        return {
            success: true,
            continuation: processedContinuation,
            stats,
            contentAnalysis,
            propertyName
        };
        
    } catch (error) {
        console.error('续写失败:', error);
        new Notice(`续写失败: ${error.message}`);
        return { success: false, error: error.message };
    }
}

/**
 * 分析内容风格
 * @param {string} content - 原始内容
 * @returns {Object} 内容分析结果
 */
function analyzeContentStyle(content) {
    const analysis = {
        type: 'unknown',
        style: 'neutral',
        tone: 'formal',
        structure: 'paragraph',
        language: 'chinese'
    };
    
    // 检测内容类�?    if (content.includes('摘要') || content.includes('关键�?) || content.includes('参考文�?)) {
        analysis.type = 'academic';
    } else if (content.includes('```') || content.includes('function') || content.includes('class')) {
        analysis.type = 'technical';
    } else if (content.includes('故事') || content.includes('小说') || content.includes('情节')) {
        analysis.type = 'narrative';
    } else if (content.includes('报告') || content.includes('分析') || content.includes('建议')) {
        analysis.type = 'report';
    } else if (content.includes('教程') || content.includes('步骤') || content.includes('如何')) {
        analysis.type = 'tutorial';
    } else {
        analysis.type = 'article';
    }
    
    // 检测写作风�?    if (content.includes('我们') || content.includes('�?) || content.includes('大家')) {
        analysis.style = 'conversational';
    } else if (content.includes('研究表明') || content.includes('数据显示') || content.includes('根据')) {
        analysis.style = 'analytical';
    } else if (content.includes('�?) || content.includes('�?) || content.includes('真的')) {
        analysis.style = 'expressive';
    }
    
    // 检测语�?    if (content.includes('专业') || content.includes('技�?) || content.includes('系统')) {
        analysis.tone = 'professional';
    } else if (content.includes('简�?) || content.includes('容易') || content.includes('轻松')) {
        analysis.tone = 'casual';
    }
    
    // 检测结�?    if (content.includes('第一') || content.includes('首先') || content.includes('其次')) {
        analysis.structure = 'numbered';
    } else if (content.includes('- ') || content.includes('* ')) {
        analysis.structure = 'bullet';
    }
    
    return analysis;
}

/**
 * 构建续写提示
 * @param {string} content - 原始内容
 * @param {number} continueLength - 续写长度
 * @param {Object} analysis - 内容分析
 * @returns {string} 续写提示
 */
function buildContinuePrompt(content, continueLength, analysis) {
    const typePrompts = {
        'academic': '请以学术写作的风格继续，保持严谨性和逻辑性，使用专业术语',
        'technical': '请以技术文档的风格继续，保持准确性和实用性，提供具体的技术细�?,
        'narrative': '请以叙事的风格继续，保持故事的连贯性和吸引力，发展情节',
        'report': '请以报告的风格继续，保持客观性和条理性，提供数据支撑',
        'tutorial': '请以教程的风格继续，保持清晰的步骤说明和实用�?,
        'article': '请以文章的风格继续，保持观点的连贯性和可读�?
    };
    
    const stylePrompts = {
        'conversational': '使用对话式的语言，与读者建立联�?,
        'analytical': '使用分析性的语言，提供深入的见解',
        'expressive': '使用富有表现力的语言，增强感染力'
    };
    
    const tonePrompts = {
        'professional': '保持专业的语�?,
        'casual': '保持轻松的语�?,
        'formal': '保持正式的语�?
    };
    
    let prompt = `请基于以下内容进行智能续写，要求：\n\n`;
    
    // 添加类型特定要求
    if (typePrompts[analysis.type]) {
        prompt += `1. ${typePrompts[analysis.type]}\n`;
    }
    
    // 添加风格要求
    if (stylePrompts[analysis.style]) {
        prompt += `2. ${stylePrompts[analysis.style]}\n`;
    }
    
    // 添加语调要求
    if (tonePrompts[analysis.tone]) {
        prompt += `3. ${tonePrompts[analysis.tone]}\n`;
    }
    
    prompt += `4. 续写长度�?{continueLength}字\n`;
    prompt += `5. 保持与原文的风格一致性和逻辑连贯性\n`;
    prompt += `6. 自然地承接原文内容，避免重复\n`;
    prompt += `7. 提供有价值的新信息或观点\n`;
    prompt += `8. 保持原文的结构特点\n\n`;
    
    prompt += `原文内容：\n${content}\n\n`;
    prompt += `请从原文结尾自然地继续写作：`;
    
    return prompt;
}

/**
 * 调用续写API
 * @param {string} prompt - 续写提示
 * @param {string} token - API密钥
 * @param {string} modelType - 模型类型
 * @returns {Promise<string>} 续写结果
 */
async function callContinueAPI(prompt, token, modelType) {
    const requestBody = {
        model: modelType,
        messages: [
            {
                role: "system",
                content: "你是一个专业的写作助手，擅长分析文本风格并进行高质量的续写。你能够保持原文的语言风格、逻辑结构和主题方向，同时提供有价值的新内容�?
            },
            {
                role: "user",
                content: prompt
            }
        ],
        temperature: 0.7,
        max_tokens: 2000
    };
    
    const response = await fetch('https://open.bigmodel.cn/api/paas/v4/chat/completions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
    }
    
    const data = await response.json();
    return data.choices[0].message.content;
}

/**
 * 处理续写内容
 * @param {string} continuation - 原始续写内容
 * @param {string} originalContent - 原始内容
 * @returns {Object} 处理后的续写对象
 */
function processContinuation(continuation, originalContent) {
    // 清理续写内容
    let cleanedContinuation = continuation
        .replace(/^续写[�?]/i, '')
        .replace(/^继续[�?]/i, '')
        .trim();
    
    // 检测重复内�?    const lastSentence = getLastSentence(originalContent);
    if (lastSentence && cleanedContinuation.includes(lastSentence)) {
        cleanedContinuation = cleanedContinuation.replace(lastSentence, '').trim();
    }
    
    return {
        content: cleanedContinuation,
        originalLength: originalContent.length,
        continuationLength: cleanedContinuation.length,
        timestamp: new Date().toISOString(),
        raw: continuation
    };
}

/**
 * 获取最后一句话
 * @param {string} content - 内容
 * @returns {string} 最后一句话
 */
function getLastSentence(content) {
    const sentences = content.split(/[。！�?!?]/);
    return sentences[sentences.length - 2]?.trim() || '';
}

/**
 * 分析续写统计
 * @param {Object} continuation - 续写对象
 * @returns {Object} 统计信息
 */
function analyzeContinuation(continuation) {
    const content = continuation.content;
    const wordCount = content.length;
    const paragraphCount = content.split('\n\n').filter(p => p.trim()).length;
    const sentenceCount = content.split(/[。！�?!?]/).filter(s => s.trim()).length;
    
    return {
        wordCount,
        paragraphCount,
        sentenceCount,
        averageWordsPerSentence: Math.round(wordCount / sentenceCount),
        continuationRatio: Math.round((continuation.continuationLength / continuation.originalLength) * 100)
    };
}

/**
 * 创意续写（多个方向）
 * @param {string} token - API密钥
 * @param {string} propertyName - 属性名
 * @param {number} optionCount - 续写选项数量
 * @param {number} continueLength - 续写长度
 * @param {string} modelType - 模型类型
 * @returns {Promise<Object>} 创意续写结果
 */
async function creativeContinue(token, propertyName, optionCount = 3, continueLength = 300, modelType = 'GLM-4-Flash') {
    try {
        const activeFile = app.workspace.getActiveFile();
        if (!activeFile) {
            throw new Error('请先打开一个文�?);
        }
        
        const content = await app.vault.read(activeFile);
        if (!content.trim()) {
            throw new Error('文件内容为空');
        }
        
        const directions = [
            '深入分析和详细阐�?,
            '提供具体案例和实�?,
            '从不同角度进行探�?,
            '总结要点和提出建�?,
            '引入新的观点和思�?
        ];
        
        const options = [];
        
        for (let i = 0; i < Math.min(optionCount, directions.length); i++) {
            const direction = directions[i];
            const prompt = `请基于以下内容进行续写，续写方向�?{direction}\n\n续写要求：\n1. 长度�?{continueLength}字\n2. ${direction}\n3. 保持与原文的连贯性\n4. 提供有价值的新内容\n\n原文内容：\n${content}\n\n请继续写作：`;
            
            const continuation = await callContinueAPI(prompt, token, modelType);
            const processed = processContinuation(continuation, content);
            
            options.push({
                direction,
                content: processed.content,
                stats: analyzeContinuation(processed)
            });
            
            // 添加延迟
            if (i < optionCount - 1) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        
        // 保存所有选项
        await saveToProperty(activeFile, propertyName, {
            type: 'creative_continue',
            options,
            timestamp: new Date().toISOString()
        });
        
        new Notice(`创意续写完成！生�?${options.length} 个续写选项`);
        
        return {
            success: true,
            options,
            propertyName
        };
        
    } catch (error) {
        console.error('创意续写失败:', error);
        new Notice(`创意续写失败: ${error.message}`);
        return { success: false, error: error.message };
    }
}

/**
 * 智能续写（基于上下文�? * @param {string} token - API密钥
 * @param {string} propertyName - 属性名
 * @param {Object} context - 上下文信�? * @param {number} continueLength - 续写长度
 * @param {string} modelType - 模型类型
 * @returns {Promise<Object>} 智能续写结果
 */
async function smartContinue(token, propertyName, context = {}, continueLength = 500, modelType = 'GLM-4-Flash') {
    const {
        targetAudience = 'general',
        purpose = 'inform',
        tone = 'neutral',
        includeExamples = false,
        includeConclusion = false
    } = context;
    
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
        throw new Error('请先打开一个文�?);
    }
    
    const content = await app.vault.read(activeFile);
    
    let prompt = `请基于以下内容进行智能续写，上下文要求：\n\n`;
    prompt += `- 目标读者：${targetAudience}\n`;
    prompt += `- 写作目的�?{purpose}\n`;
    prompt += `- 语言风格�?{tone}\n`;
    
    if (includeExamples) {
        prompt += `- 包含具体例子和案例\n`;
    }
    
    if (includeConclusion) {
        prompt += `- 包含总结或结论\n`;
    }
    
    prompt += `- 续写长度：约${continueLength}字\n\n`;
    prompt += `原文内容：\n${content}\n\n请继续写作：`;
    
    const continuation = await callContinueAPI(prompt, token, modelType);
    const processed = processContinuation(continuation, content);
    
    // 添加上下文信�?    processed.context = context;
    
    await saveToProperty(activeFile, propertyName, processed);
    
    const stats = analyzeContinuation(processed);
    
    return {
        success: true,
        continuation: processed,
        stats,
        context,
        propertyName
    };
}

/**
 * 批量续写
 * @param {string} token - API密钥
 * @param {string} filePattern - 文件匹配模式
 * @param {number} continueLength - 续写长度
 * @param {string} modelType - 模型类型
 * @returns {Promise<Object>} 批量续写结果
 */
async function batchContinue(token, filePattern, continueLength = 300, modelType = 'GLM-4-Flash') {
    try {
        const vault = app.vault;
        const files = vault.getMarkdownFiles();
        
        const matchedFiles = files.filter(file => {
            if (filePattern === '*.md') return true;
            return file.path.includes(filePattern);
        });
        
        if (matchedFiles.length === 0) {
            throw new Error('没有找到匹配的文�?);
        }
        
        new Notice(`开始批量续写，�?${matchedFiles.length} 个文�?..`);
        
        const results = [];
        let successCount = 0;
        
        for (let i = 0; i < matchedFiles.length; i++) {
            const file = matchedFiles[i];
            try {
                console.log(`处理文件 ${i + 1}/${matchedFiles.length}: ${file.name}`);
                
                const content = await vault.read(file);
                if (!content.trim()) continue;
                
                const contentAnalysis = analyzeContentStyle(content);
                const prompt = buildContinuePrompt(content, continueLength, contentAnalysis);
                const continuation = await callContinueAPI(prompt, token, modelType);
                const processed = processContinuation(continuation, content);
                
                await saveToProperty(file, 'AI续写', processed);
                
                const stats = analyzeContinuation(processed);
                
                results.push({
                    file: file.name,
                    success: true,
                    stats
                });
                
                successCount++;
                
                // 添加延迟
                await new Promise(resolve => setTimeout(resolve, 2000));
                
            } catch (error) {
                console.error(`处理文件 ${file.name} 失败:`, error);
                results.push({
                    file: file.name,
                    success: false,
                    error: error.message
                });
            }
        }
        
        new Notice(`批量续写完成！成功：${successCount}/${matchedFiles.length}`);
        
        return {
            success: true,
            totalFiles: matchedFiles.length,
            successCount,
            results
        };
        
    } catch (error) {
        console.error('批量续写失败:', error);
        new Notice(`批量续写失败: ${error.message}`);
        return { success: false, error: error.message };
    }
}

/**
 * 保存到文件属�? * @param {Object} file - 文件对象
 * @param {string} propertyName - 属性名
 * @param {Object} continuation - 续写对象
 */
async function saveToProperty(file, propertyName, continuation) {
    await app.fileManager.processFrontMatter(file, (frontmatter) => {
        if (typeof continuation === 'object' && continuation.content) {
            frontmatter[propertyName] = continuation.content;
            frontmatter[`${propertyName}_stats`] = continuation.stats || analyzeContinuation(continuation);
        } else {
            frontmatter[propertyName] = continuation;
        }
    });
}

// 导出函数
module.exports = {
    aiContinue,
    creativeContinue,
    smartContinue,
    batchContinue
};

// 使用说明
console.log(`
AI续写助手脚本已加载！

主要函数�?1. aiContinue(token, propertyName, continueLength, modelType)
2. creativeContinue(token, propertyName, optionCount, continueLength, modelType)
3. smartContinue(token, propertyName, context, continueLength, modelType)
4. batchContinue(token, filePattern, continueLength, modelType)

上下文选项�?- targetAudience: 'general', 'academic', 'business', 'technical'
- purpose: 'inform', 'persuade', 'entertain', 'educate'
- tone: 'neutral', 'formal', 'casual', 'professional'
- includeExamples: true/false
- includeConclusion: true/false

使用示例�?// 基础续写
aiContinue('your-token', '续写内容', 500)

// 创意续写（多个选项�?creativeContinue('your-token', '创意续写', 3, 300)

// 智能续写
smartContinue('your-token', '智能续写', {targetAudience: 'academic', includeExamples: true})

// 批量续写
batchContinue('your-token', '*.md', 300)
`);
