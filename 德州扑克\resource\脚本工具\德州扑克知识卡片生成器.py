#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
德州扑克知识卡片生成器
将德州扑克笔记按小标题段落切分成独立的知识卡片
每个知识卡片使用关键词或关键句子作为标题
"""

import os
import re
import json
from pathlib import Path
from datetime import datetime
import hashlib

class PokerKnowledgeCardGenerator:
    def __init__(self, source_dir, output_dir):
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        self.cards_dir = self.output_dir / "知识卡片"
        self.index_dir = self.output_dir / "索引"
        self.cards_dir.mkdir(exist_ok=True)
        self.index_dir.mkdir(exist_ok=True)
        
        self.all_cards = []
        
    def extract_key_phrase(self, content):
        """从内容中提取关键词或关键句子作为标题"""
        lines = content.split('\n')
        
        # 优先查找粗体标记的关键词
        for line in lines:
            bold_matches = re.findall(r'\*\*(.*?)\*\*', line)
            if bold_matches:
                # 取第一个粗体内容作为关键词
                key_phrase = bold_matches[0].strip()
                if len(key_phrase) > 3 and len(key_phrase) < 50:
                    return key_phrase
        
        # 查找包含关键概念的句子
        key_patterns = [
            r'.*?(GTO|概率|策略|下注|加注|弃牌|诈唬|价值|位置|筹码).*?[。！？]',
            r'.*?(牌型|同花|顺子|对子|听牌|成牌).*?[。！？]',
            r'.*?(松凶|紧弱|平衡|激进|保守).*?[。！？]',
            r'.*?(心理|博弈|读人|判断|分析).*?[。！？]'
        ]
        
        for line in lines:
            line = line.strip()
            if len(line) > 10 and len(line) < 100:
                for pattern in key_patterns:
                    if re.search(pattern, line):
                        # 清理句子，去掉标点
                        clean_line = re.sub(r'[。！？，、：；]', '', line)
                        if len(clean_line) > 5 and len(clean_line) < 80:
                            return clean_line
        
        # 如果没找到合适的关键句，使用第一个有意义的句子
        for line in lines:
            line = line.strip()
            if (len(line) > 8 and len(line) < 60 and 
                not line.startswith('#') and 
                not line.startswith('-') and
                not line.startswith('*') and
                '：' in line or '是' in line or '为' in line):
                return re.sub(r'[。！？，、：；]', '', line)
        
        return "德州扑克知识点"
    
    def clean_title(self, title):
        """清理标题"""
        # 去掉markdown标记
        title = re.sub(r'#+\s*', '', title)
        title = re.sub(r'\*\*(.*?)\*\*', r'\1', title)
        title = re.sub(r'\*(.*?)\*', r'\1', title)
        
        # 去掉序号和特殊符号
        title = re.sub(r'^[\d\.\s\-\*\#]+', '', title)
        title = re.sub(r'^[✅🏆📌⚡️💡🔍🌰❌📊🎯🚀💰⭐️\s]+', '', title)
        title = re.sub(r'[：:]+$', '', title)
        
        return title.strip()
    
    def extract_sections(self, content, file_path):
        """提取小标题段落"""
        sections = []
        lines = content.split('\n')
        current_section = None
        current_content = []
        
        for i, line in enumerate(lines):
            # 检测标题行
            is_title = (
                line.startswith('###') or 
                line.startswith('####') or
                re.match(r'^\s*\d+\.\s*\*\*.*\*\*', line) or
                re.match(r'^\s*\*\*\d+\.\s*.*\*\*', line) or
                (line.strip().endswith('：') and len(line.strip()) < 50) or
                re.match(r'^[✅🏆📌⚡️💡🔍🌰❌📊🎯🚀💰⭐️]\s*\*\*.*\*\*', line)
            )
            
            if is_title:
                # 保存上一个段落
                if current_section and current_content:
                    content_text = '\n'.join(current_content).strip()
                    if len(content_text) > 100:  # 只保留有足够内容的段落
                        # 提取关键词作为标题
                        key_title = self.extract_key_phrase(content_text)
                        sections.append({
                            'title': key_title,
                            'original_title': current_section,
                            'content': content_text,
                            'source_file': file_path.name
                        })
                
                # 开始新段落
                current_section = self.clean_title(line)
                current_content = [line]
                
            else:
                if current_section:
                    current_content.append(line)
                # 如果还没有标题，但内容足够多，也创建一个段落
                elif len(current_content) > 20:
                    content_text = '\n'.join(current_content).strip()
                    if len(content_text) > 100:
                        key_title = self.extract_key_phrase(content_text)
                        sections.append({
                            'title': key_title,
                            'original_title': '开头段落',
                            'content': content_text,
                            'source_file': file_path.name
                        })
                    current_content = [line]
                else:
                    current_content.append(line)
        
        # 保存最后一个段落
        if current_section and current_content:
            content_text = '\n'.join(current_content).strip()
            if len(content_text) > 100:
                key_title = self.extract_key_phrase(content_text)
                sections.append({
                    'title': key_title,
                    'original_title': current_section,
                    'content': content_text,
                    'source_file': file_path.name
                })
        
        return sections
    
    def generate_tags(self, title, content, source_file):
        """生成标签"""
        tags = ['德州扑克']
        
        # 基于内容生成标签
        content_lower = content.lower()
        title_lower = title.lower()
        
        # 策略类标签
        if any(word in content_lower for word in ['gto', '博弈', '最优']):
            tags.append('GTO策略')
        if any(word in content_lower for word in ['概率', '赔率', '期望']):
            tags.append('概率分析')
        if any(word in content_lower for word in ['下注', '加注', '跟注']):
            tags.append('下注策略')
        if any(word in content_lower for word in ['诈唬', 'bluff', '虚张声势']):
            tags.append('诈唬技巧')
        if any(word in content_lower for word in ['位置', '早位', '晚位', '按钮']):
            tags.append('位置策略')
        if any(word in content_lower for word in ['筹码', '深筹码', '短筹码']):
            tags.append('筹码管理')
        
        # 玩家类型标签
        if any(word in content_lower for word in ['松凶', 'lag']):
            tags.append('松凶型')
        if any(word in content_lower for word in ['紧弱', 'nit']):
            tags.append('紧弱型')
        if any(word in content_lower for word in ['平衡', '混合']):
            tags.append('平衡型')
        
        # 牌型标签
        if any(word in content_lower for word in ['同花', '顺子', '对子', '三条', '满堂红']):
            tags.append('牌型分析')
        if any(word in content_lower for word in ['听牌', '成牌', '补牌']):
            tags.append('听牌策略')
        
        # 心理标签
        if any(word in content_lower for word in ['心理', '读人', '判断', '观察']):
            tags.append('心理博弈')
        if any(word in content_lower for word in ['复盘', '分析', '改进']):
            tags.append('复盘技巧')
        
        return list(set(tags))
    
    def safe_filename(self, title):
        """生成安全的文件名"""
        safe_title = re.sub(r'[<>:"/\\|?*\n\r\t]', '_', title)
        safe_title = re.sub(r'[_\s]+', '_', safe_title)
        safe_title = safe_title.strip('_')
        
        if len(safe_title) > 50:
            safe_title = safe_title[:50].rstrip('_')
            
        return safe_title if safe_title else "知识卡片"
    
    def create_knowledge_card(self, section, card_id):
        """创建知识卡片"""
        title = section['title']
        original_title = section['original_title']
        content = section['content']
        source_file = section['source_file']
        
        tags = self.generate_tags(title, content, source_file)
        
        card_content = f"""---
id: {card_id}
title: {title}
original_title: {original_title}
source: [[{source_file}]]
tags: {', '.join(f'#{tag}' for tag in tags)}
created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
---

# {title}

> 原标题: {original_title}

{content}

---

## 相关信息
- 来源笔记: [[{source_file}]]
- 创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 标签: {', '.join(f'#{tag}' for tag in tags)}
"""
        
        return {
            'id': card_id,
            'title': title,
            'original_title': original_title,
            'source_file': source_file,
            'tags': tags,
            'created_time': datetime.now().isoformat()
        }, card_content

    def process_file(self, file_path):
        """处理单个文件"""
        print(f"处理文件: {file_path.name}")

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"  读取失败: {e}")
            return

        # 跳过太短的文件
        if len(content) < 200:
            print(f"  文件内容太短，跳过")
            return

        sections = self.extract_sections(content, file_path)

        if not sections:
            print(f"  未找到可提取的段落")
            return

        print(f"  提取到 {len(sections)} 个知识点")

        for section in sections:
            # 生成卡片ID
            card_id = hashlib.md5(f"{file_path.name}_{section['title']}".encode()).hexdigest()[:8]

            # 创建卡片
            metadata, card_content = self.create_knowledge_card(section, card_id)

            # 生成文件名
            safe_title = self.safe_filename(section['title'])
            card_filename = f"{safe_title}.md"
            card_path = self.cards_dir / card_filename

            # 避免重名
            counter = 1
            while card_path.exists():
                card_filename = f"{safe_title}_{counter}.md"
                card_path = self.cards_dir / card_filename
                counter += 1

            # 保存卡片
            with open(card_path, 'w', encoding='utf-8') as f:
                f.write(card_content)

            self.all_cards.append(metadata)
            print(f"    ✓ {section['title']}")

    def create_index_files(self):
        """创建索引文件"""
        # 标签索引
        tags_index = {}
        for card in self.all_cards:
            for tag in card['tags']:
                if tag not in tags_index:
                    tags_index[tag] = []
                tags_index[tag].append(card)

        tags_content = "# 德州扑克知识卡片标签索引\n\n"
        for tag, cards in sorted(tags_index.items()):
            tags_content += f"## #{tag}\n\n"
            for card in cards:
                safe_title = self.safe_filename(card['title'])
                tags_content += f"- [[{safe_title}]] - {card['title']}\n"
            tags_content += "\n"

        with open(self.index_dir / "标签索引.md", 'w', encoding='utf-8') as f:
            f.write(tags_content)

        # 来源文件索引
        source_index = {}
        for card in self.all_cards:
            source = card['source_file']
            if source not in source_index:
                source_index[source] = []
            source_index[source].append(card)

        source_content = "# 德州扑克知识卡片来源索引\n\n"
        for source, cards in sorted(source_index.items()):
            source_content += f"## {source}\n\n"
            for card in cards:
                safe_title = self.safe_filename(card['title'])
                source_content += f"- [[{safe_title}]] - {card['title']}\n"
            source_content += "\n"

        with open(self.index_dir / "来源索引.md", 'w', encoding='utf-8') as f:
            f.write(source_content)

        # 总索引
        total_content = f"""# 德州扑克知识卡片总索引

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
总卡片数: {len(self.all_cards)}

## 所有知识卡片

"""
        for card in sorted(self.all_cards, key=lambda x: x['title']):
            safe_title = self.safe_filename(card['title'])
            tags_str = ', '.join(f'#{tag}' for tag in card['tags'])
            total_content += f"- [[{safe_title}]] - {card['title']} ({tags_str})\n"

        with open(self.index_dir / "总索引.md", 'w', encoding='utf-8') as f:
            f.write(total_content)

    def run(self):
        """运行生成器"""
        print(f"德州扑克知识卡片生成器")
        print(f"源目录: {self.source_dir}")
        print(f"输出目录: {self.output_dir}")
        print("=" * 60)

        # 找到所有md文件
        md_files = []
        for file_path in self.source_dir.rglob("*.md"):
            # 跳过已经生成的知识卡片文件
            if "知识卡片" not in str(file_path) and "索引" not in str(file_path):
                md_files.append(file_path)

        print(f"找到 {len(md_files)} 个笔记文件")

        # 处理每个文件
        for file_path in md_files:
            self.process_file(file_path)

        # 创建索引
        if self.all_cards:
            print("\n创建索引文件...")
            self.create_index_files()

            print(f"\n✅ 完成！生成了 {len(self.all_cards)} 个知识卡片")
            print(f"📁 保存在: {self.output_dir}")
            print(f"📊 标签索引: {self.index_dir / '标签索引.md'}")
            print(f"📋 总索引: {self.index_dir / '总索引.md'}")
        else:
            print("\n❌ 未生成任何知识卡片")

if __name__ == "__main__":
    # 设置源目录和输出目录
    current_dir = Path(__file__).parent
    source_dir = current_dir.parent  # 德州扑克目录
    output_dir = source_dir / "知识卡片库"

    # 运行生成器
    generator = PokerKnowledgeCardGenerator(source_dir, output_dir)
    generator.run()
