{"id": "cross-border-data-transfer-form", "fields": [{"id": "scenario", "label": "具体场景", "type": "text", "description": "跨境数据传输的具体场景"}, {"id": "data-source", "label": "数据源", "type": "text", "description": "数据的来源地"}, {"id": "transit-node", "label": "中转节点", "type": "text", "description": "数据传输的中转节点"}, {"id": "destination", "label": "目标地", "type": "text", "description": "数据传输的目标地"}, {"id": "china-regulations", "label": "中国适用法规", "type": "textarea", "rows": 2, "description": "PIPL、数据安全法等相关条文"}, {"id": "destination-regulations", "label": "目标国法规", "type": "textarea", "rows": 2, "description": "GDPR或其他当地法规"}, {"id": "international-agreements", "label": "国际协议", "type": "textarea", "rows": 2, "description": "SCC/BCR/认证机制等"}, {"id": "scc-path", "label": "标准合同条款", "type": "textarea", "rows": 2, "description": "标准合同条款的合规路径"}, {"id": "certification-mechanism", "label": "认证机制", "type": "textarea", "rows": 2, "description": "相关认证机制"}, {"id": "regulatory-filing", "label": "监管备案", "type": "textarea", "rows": 2, "description": "监管备案要求"}, {"id": "technical-measures", "label": "技术保障措施", "type": "textarea", "rows": 3, "description": "技术层面的保障措施"}, {"id": "risk-control-matrix", "label": "风险控制矩阵", "type": "textarea", "rows": 3, "description": "风险控制的具体措施"}, {"id": "similar-transfer", "label": "类似传输场景", "type": "text", "description": "类似传输场景的链接"}, {"id": "regulatory-dynamics", "label": "监管动态", "type": "text", "description": "相关监管动态的链接"}], "action": {"id": "generate-cross-border-transfer", "type": "runScript", "targetFolder": "", "fileName": "", "scriptSource": "inline", "code": "async function entry() {\n  const { form, app, requestUrl, Notice } = this.$context;\n  \n  try {\n    // 生成AI优化的内容\n    let aiEnhancedContent = '';\n    const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec';\n    \n    const prompt = `你是跨境数据传输合规专家。请基于以下信息，提供专业的合规分析：\n\n传输场景：${form.scenario}\n传输路径：${form['data-source']} → ${form['transit-node']} → ${form.destination}\n中国法规：${form['china-regulations']}\n目标国法规：${form['destination-regulations']}\n国际协议：${form['international-agreements']}\n标准合同条款：${form['scc-path']}\n认证机制：${form['certification-mechanism']}\n监管备案：${form['regulatory-filing']}\n技术措施：${form['technical-measures']}\n风险控制：${form['risk-control-matrix']}\n\n请提供：\n1. 跨境传输合规路径的优劣分析\n2. 潜在的合规风险点和应对策略\n3. 技术和管理措施的完善建议\n4. 监管趋势对该方案的影响分析\n\n要求：专业、全面、前瞻性。`;\n    \n    try {\n      const aiResponse = await requestUrl({\n        url: 'https://api.deepseek.com/v1/chat/completions',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: \"deepseek-chat\",\n          messages: [{ role: \"user\", content: prompt }],\n          temperature: 0.7,\n          max_tokens: 1800\n        })\n      });\n      \n      if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n        aiEnhancedContent = aiResponse.json.choices[0].message.content;\n      }\n    } catch (e) {\n      aiEnhancedContent = '(AI分析暂时不可用，请手动补充深度分析)';\n    }\n    \n    // 生成完整内容\n    const template = `### 跨境传输合规分析：${form.scenario}\n**传输路径**：${form['data-source']} → ${form['transit-node']} → ${form.destination}\n**适用法规**：\n- 中国：${form['china-regulations']}\n- 目标国：${form['destination-regulations']}\n- 国际协议：${form['international-agreements']}\n**合规路径选择**：\n1. 标准合同条款：${form['scc-path']}\n2. 认证机制：${form['certification-mechanism']}\n3. 监管备案：${form['regulatory-filing']}\n**技术保障措施**：${form['technical-measures']}\n**风险控制矩阵**：${form['risk-control-matrix']}\n→ 关联：[[${form['similar-transfer']}]] [[${form['regulatory-dynamics']}]]\n\n---\n\n## 🤖 AI专业分析\n\n${aiEnhancedContent}\n\n---\n\n## 📝 实施跟踪\n<!-- 记录实际实施过程中的问题和调整 -->\n\n\n## 🏷️ 标签\n#跨境数据传输 #数据合规 #国际法规 #${new Date().toISOString().split('T')[0]}\n\n---\n*生成时间：${new Date().toLocaleString()} | AI助手：DeepSeek*`;\n    \n    // 创建文件\n    const today = new Date();\n    const dateStr = today.toISOString().split('T')[0];\n    const timeStr = today.toTimeString().split(' ')[0].substring(0, 5).replace(':', '');\n    const fileName = `跨境数据传输-${form.scenario}-${dateStr}-${timeStr}.md`;\n    const filePath = `工作室/肌肉/生成笔记/跨境数据传输/${fileName}`;\n    \n    // 检查并创建目录\n    const folderPath = '工作室/肌肉/生成笔记/跨境数据传输';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    // 创建文件\n    const file = await app.vault.create(filePath, template);\n    new Notice(`跨境数据传输分析已创建: ${fileName}`);\n    \n    // 打开新创建的文件\n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 跨境数据传输分析已生成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('生成跨境数据传输分析失败:', error);\n    new Notice('生成失败: ' + error.message);\n    return '❌ 生成失败，请检查配置';\n  }\n}\n\nexports.default = {\n  entry: entry,\n};"}, "title": "跨境数据传输表单"}