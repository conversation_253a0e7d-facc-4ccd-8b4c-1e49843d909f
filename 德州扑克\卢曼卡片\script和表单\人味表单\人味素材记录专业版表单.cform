{
  "id": "professional-human-material-form",
  "fields": [
    {
      "id": "date",
      "label": "记录日期",
      "type": "text",
      "defaultValue": "{{date:YYYY-MM-DD}}",
      "description": "素材记录日期"
    },
    {
      "id": "materialCategory",
      "label": "素材类别",
      "type": "select",
      "options": [
        {"id": "conflict-scene", "label": "【冲突现场】(中景)", "value": "冲突现场"},
        {"id": "industry-undercurrent", "label": "【行业暗流】(大景)", "value": "行业暗流"},
        {"id": "soul-questioning", "label": "【灵魂拷问】(特写)", "value": "灵魂拷问"},
        {"id": "evidence-archive", "label": "【证据存档】", "value": "证据存档"}
      ],
      "description": "选择素材的类别（相当于摄影的景别）"
    },
    {
      "id": "originalWords",
      "label": "客户/同事的原话（带行业黑话）",
      "type": "textarea",
      "rows": 3,
      "description": "记录原话，保持行业黑话和真实感"
    },
    {
      "id": "bodyLanguageDetail",
      "label": "身体语言/环境细节",
      "type": "textarea",
      "rows": 2,
      "description": "如：他说这话时转着婚戒，背后屏幕正播放'诚信经营'标语"
    },
    {
      "id": "regulatoryDeformation",
      "label": "监管动态的落地变形",
      "type": "textarea",
      "rows": 3,
      "description": "如：网信办新规要求'明示收集目的'，但同行都在用户协议里埋了5000字解释"
    },
    {
      "id": "industryCollectiveBehavior",
      "label": "行业集体行为",
      "type": "textarea",
      "rows": 2,
      "description": "如：3家客户上周同时要求删除'数据保留期限'具体数字"
    },
    {
      "id": "counterIntuitiveInsight",
      "label": "你的反常识洞察",
      "type": "textarea",
      "rows": 3,
      "description": "如：企业总说'用户不在乎隐私'，但投诉最多的恰恰是VIP客户"
    },
    {
      "id": "metaphorComplaint",
      "label": "神吐槽/比喻",
      "type": "textarea",
      "rows": 2,
      "description": "如：现在的隐私政策像手术同意书，但医生不会把条款印成蚂蚁大小"
    },
    {
      "id": "evidenceScreenshot",
      "label": "可脱敏使用的文件截图",
      "type": "textarea",
      "rows": 2,
      "description": "如：客户V1版合同第7条：'数据可能传输到所有合作伙伴'"
    },
    {
      "id": "dialogueFragment",
      "label": "对话片段录音（文字摘要）",
      "type": "textarea",
      "rows": 2,
      "description": "重要对话的文字摘要"
    },
    {
      "id": "applicationScene",
      "label": "应用场景",
      "type": "select",
      "options": [
        {"id": "article-opening", "label": "文章开头", "value": "文章开头"},
        {"id": "case-analysis", "label": "案例分析", "value": "案例分析"},
        {"id": "industry-report", "label": "行业报告", "value": "行业报告"},
        {"id": "client-education", "label": "客户教育", "value": "客户教育"},
        {"id": "social-media", "label": "社交媒体", "value": "社交媒体"},
        {"id": "book-chapter", "label": "出书章节", "value": "出书章节"},
        {"id": "speech-material", "label": "演讲素材", "value": "演讲素材"}
      ],
      "description": "这个素材最适合的应用场景"
    },
    {
      "id": "emotionalTone",
      "label": "情感基调",
      "type": "select",
      "options": [
        {"id": "helpless-irony", "label": "无奈讽刺", "value": "无奈讽刺"},
        {"id": "angry-criticism", "label": "愤怒批判", "value": "愤怒批判"},
        {"id": "warm-understanding", "label": "温暖理解", "value": "温暖理解"},
        {"id": "humorous-ridicule", "label": "幽默调侃", "value": "幽默调侃"},
        {"id": "deep-reflection", "label": "深度反思", "value": "深度反思"},
        {"id": "professional-analysis", "label": "专业分析", "value": "专业分析"}
      ],
      "description": "选择这个素材的情感基调"
    },
    {
      "id": "industryType",
      "label": "涉及行业",
      "type": "select",
      "options": [
        {"id": "ecommerce", "label": "电商零售", "value": "电商零售"},
        {"id": "fintech", "label": "金融科技", "value": "金融科技"},
        {"id": "healthcare", "label": "医疗健康", "value": "医疗健康"},
        {"id": "education", "label": "在线教育", "value": "在线教育"},
        {"id": "social-media", "label": "社交媒体", "value": "社交媒体"},
        {"id": "gaming", "label": "游戏娱乐", "value": "游戏娱乐"},
        {"id": "iot-smart", "label": "物联网/智能硬件", "value": "物联网/智能硬件"},
        {"id": "ai-bigdata", "label": "AI/大数据", "value": "AI/大数据"},
        {"id": "cross-industry", "label": "跨行业通用", "value": "跨行业通用"}
      ],
      "description": "选择涉及的主要行业"
    },
    {
      "id": "complianceArea",
      "label": "合规领域",
      "type": "select",
      "options": [
        {"id": "data-cross-border", "label": "数据跨境", "value": "数据跨境"},
        {"id": "user-consent", "label": "用户同意", "value": "用户同意"},
        {"id": "data-security", "label": "数据安全", "value": "数据安全"},
        {"id": "privacy-policy", "label": "隐私政策", "value": "隐私政策"},
        {"id": "data-retention", "label": "数据留存", "value": "数据留存"},
        {"id": "third-party-sharing", "label": "第三方共享", "value": "第三方共享"},
        {"id": "regulatory-response", "label": "监管应对", "value": "监管应对"},
        {"id": "user-rights", "label": "用户权利", "value": "用户权利"}
      ],
      "description": "选择主要涉及的合规领域"
    },
    {
      "id": "urgencyLevel",
      "label": "素材热度",
      "type": "select",
      "options": [
        {"id": "hot-topic", "label": "热点素材（立即使用）", "value": "热点素材"},
        {"id": "high-value", "label": "高价值（本周处理）", "value": "高价值"},
        {"id": "medium-value", "label": "中等价值（本月处理）", "value": "中等价值"},
        {"id": "archive", "label": "存档备用", "value": "存档备用"}
      ],
      "description": "评估素材的时效性和价值"
    }
  ],
  "action": {
    "type": "runScript",
    "scriptSource": "inline",
    "code": "async function entry() {\n  const { form, app, requestUrl, Notice } = this.$context;\n  \n  try {\n    // 调试：打印所有可能的form属性\n    console.log('=== 人味素材记录专业版表单数据调试 ===');\n    console.log('form对象:', form);\n    console.log('form的所有属性:', Object.keys(form));\n    for (let key in form) {\n      console.log(`${key}: ${form[key]}`);\n    }\n    \n    // 获取表单数据\n    const date = form.date || new Date().toISOString().split('T')[0];\n    const materialCategory = form.materialCategory || form['materialCategory'] || '冲突现场';\n    const originalWords = form.originalWords || form['originalWords'] || '客户原话记录';\n    const bodyLanguageDetail = form.bodyLanguageDetail || form['bodyLanguageDetail'] || '身体语言和环境细节';\n    const regulatoryDeformation = form.regulatoryDeformation || form['regulatoryDeformation'] || '监管落地变形';\n    const industryCollectiveBehavior = form.industryCollectiveBehavior || form['industryCollectiveBehavior'] || '行业集体行为';\n    const counterIntuitiveInsight = form.counterIntuitiveInsight || form['counterIntuitiveInsight'] || '反常识洞察';\n    const metaphorComplaint = form.metaphorComplaint || form['metaphorComplaint'] || '神吐槽比喻';\n    const evidenceScreenshot = form.evidenceScreenshot || form['evidenceScreenshot'] || '证据截图';\n    const dialogueFragment = form.dialogueFragment || form['dialogueFragment'] || '对话片段';\n    const applicationScene = form.applicationScene || form['applicationScene'] || '文章开头';\n    const emotionalTone = form.emotionalTone || form['emotionalTone'] || '无奈讽刺';\n    const industryType = form.industryType || form['industryType'] || '电商零售';\n    const complianceArea = form.complianceArea || form['complianceArea'] || '数据跨境';\n    const urgencyLevel = form.urgencyLevel || form['urgencyLevel'] || '高价值';\n    \n    // 生成当前日期（20250803格式）\n    const today = new Date();\n    const year = today.getFullYear();\n    const month = (today.getMonth() + 1).toString().padStart(2, '0');\n    const day = today.getDate().toString().padStart(2, '0');\n    const dateStr = `${year}${month}${day}`;\n    const timeStr = today.getHours().toString().padStart(2, '0') + today.getMinutes().toString().padStart(2, '0');\n    \n    // 使用YAML前置元数据结构\n    const yamlFrontmatter = `---\ndate: ${dateStr}\nmaterialCategory: ${materialCategory}\nindustryType: ${industryType}\ncomplianceArea: ${complianceArea}\nemotionalTone: ${emotionalTone}\napplicationScene: ${applicationScene}\nurgencyLevel: ${urgencyLevel}\ntags:\n  - 人味素材专业版\n  - ${materialCategory}\n  - ${industryType}\n  - ${complianceArea}\n  - ${emotionalTone}\n  - ${urgencyLevel}\ncreatedBy: 人味素材记录专业版系统\naiModel: DeepSeek\n---`;\n    \n    // 生成基础内容模板（按专业版模板结构）\n    const baseTemplate = `# 人味素材记录专业版 - ${dateStr}\n\n## 📋 素材分类\n\n**类别：** ${materialCategory}\n**行业：** ${industryType}\n**合规领域：** ${complianceArea}\n**情感基调：** ${emotionalTone}\n\n## 🎯 【${materialCategory}】核心内容\n\n### 客户/同事的原话（带行业黑话）\n> "${originalWords}"\n\n### 身体语言/环境细节\n${bodyLanguageDetail}\n\n## 🌊 行业观察\n\n### 监管动态的落地变形\n${regulatoryDeformation}\n\n### 行业集体行为\n${industryCollectiveBehavior}\n\n## 💡 深度洞察\n\n### 你的反常识洞察\n${counterIntuitiveInsight}\n\n### 神吐槽/比喻\n> "${metaphorComplaint}"\n\n## 📎 证据存档\n\n### 可脱敏使用的文件截图\n${evidenceScreenshot}\n\n### 对话片段录音（文字摘要）\n${dialogueFragment}\n\n## 🎯 应用规划\n\n**主要应用场景：** ${applicationScene}\n**素材热度：** ${urgencyLevel}\n\n---\n\n**快速标签：** #${industryType} #${complianceArea} #${materialCategory} #${emotionalTone}`;\n\n    // AI专业素材深度加工\n    let aiEnhancedContent = '';\n    const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec';\n    \n    const enhancedPrompt = `你是一位资深的合规内容创作专家。请基于以下专业版人味素材记录，进行深度的专业加工：\n\n素材记录：\n${baseTemplate}\n\n请你从以下专业角度进行深度加工：\n\n1. **素材价值分析**：这个${materialCategory}类型的素材，在${industryType}行业中的独特价值是什么？\n2. **专业洞察提炼**：从${complianceArea}的角度，这个素材揭示了什么深层问题？\n3. **创作角度建议**：如何将这个素材用于${applicationScene}？提供3-5个具体的创作角度\n4. **行业连接**：这个素材可以和${industryType}行业的哪些热点话题或趋势连接？\n5. **情感处理**：如何运用${emotionalTone}的基调，让这个素材更有感染力？\n6. **应用策略**：考虑到${urgencyLevel}的时效性，应该如何安排使用优先级？\n\n要求：\n- 体现对${industryType}行业和${complianceArea}领域的专业理解\n- 提供具体可操作的创作建议\n- 分析要有深度，避免泛泛而谈\n- 考虑素材的商业价值和传播价值\n\n请以资深合规内容专家的身份，帮助将这个素材的价值最大化。`;\n    \n    try {\n      const aiResponse = await requestUrl({\n        url: 'https://api.deepseek.com/v1/chat/completions',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: 'deepseek-chat',\n          messages: [{ role: 'user', content: enhancedPrompt }],\n          temperature: 0.8,\n          max_tokens: 2000\n        })\n      });\n      \n      if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n        aiEnhancedContent = aiResponse.json.choices[0].message.content;\n      }\n    } catch (e) {\n      aiEnhancedContent = '(AI专业素材加工暂时不可用，请手动补充专业分析)';\n    }\n\n    // 生成完整的markdown内容\n    const fullTemplate = `${yamlFrontmatter}\n\n${baseTemplate}\n\n---\n\n## 🔍 AI专业素材深度加工\n\n${aiEnhancedContent}\n\n---\n\n## 📝 创作应用记录\n\n<!-- 记录这个素材在实际创作中的使用情况 -->\n\n## 🔄 素材迭代优化\n\n<!-- 记录素材的进一步加工和优化 -->\n\n## 📊 效果追踪\n\n<!-- 记录使用这个素材的内容的传播效果 -->\n\n---\n\n*记录时间：${new Date().toLocaleString('zh-CN')} | AI专业加工：DeepSeek | 专业素材库*`;\n    \n    // 生成文件名（使用20250803格式）\n    const fileName = `专业素材-${materialCategory}-${industryType}-${dateStr}-${timeStr}.md`;\n    const filePath = `工作室/肌肉/生成笔记/专业素材记录/${fileName}`;\n    \n    // 确保文件夹存在\n    const folderPath = '工作室/肌肉/生成笔记/专业素材记录';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    // 创建文件\n    const file = await app.vault.create(filePath, fullTemplate);\n    new Notice(`专业素材记录已创建: ${fileName}`);\n    \n    // 打开新创建的文件\n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 专业素材记录已生成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('生成专业素材记录失败:', error);\n    new Notice('生成失败: ' + error.message);\n    return `❌ 生成失败: ${error.message}`;\n  }\n}\n\nexports.default = {\n  entry: entry\n};"
  },
  "title": "人味素材记录专业版表单"
}
