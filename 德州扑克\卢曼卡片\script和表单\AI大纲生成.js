﻿/**
 * AI大纲生成脚本
 * 功能：为长文章生成结构化大纲
 * 作者：Builder
 * 版本�?.0
 */

/**
 * AI大纲生成函数
 * @param {string} token - API密钥
 * @param {string} propertyName - 保存大纲的属性名
 * @param {number} outlineDepth - 大纲层级深度，默认为3
 * @param {string} modelType - AI模型类型，默认为'GLM-4-Flash'
 * @returns {Promise<Object>} 生成结果
 */
async function aiOutline(token, propertyName, outlineDepth = 3, modelType = 'GLM-4-Flash') {
    try {
        // 验证参数
        if (!token || !propertyName) {
            throw new Error('缺少必要参数：token, propertyName');
        }

        console.log(`开始生成大纲：层级=${outlineDepth}, 属�?${propertyName}`);
        
        // 获取当前文件内容
        const activeFile = app.workspace.getActiveFile();
        if (!activeFile) {
            throw new Error('请先打开一个文�?);
        }
        
        const content = await app.vault.read(activeFile);
        
        if (!content.trim()) {
            throw new Error('文件内容为空');
        }
        
        // 构建大纲生成提示
        const prompt = `请为以下文章生成一个清晰的${outlineDepth}级层级大纲，要求�?
1. 大纲应该准确反映文章的逻辑结构和主要内�?2. 使用标准的大纲格式（I. A. 1. a. �?1. 1.1 1.1.1 格式�?3. 每个层级都要有简洁明确的标题
4. 大纲层级不超�?{outlineDepth}�?5. 突出文章的核心论点和关键信息
6. 保持逻辑清晰，层次分�?7. 适合作为文章导航和快速浏�?
文章内容�?${content}`;
        
        // 调用AI API
        const outline = await callOutlineAPI(prompt, token, modelType);
        
        // 处理大纲
        const processedOutline = processOutline(outline);
        
        // 保存大纲到属�?        await saveToProperty(activeFile, propertyName, processedOutline);
        
        // 生成大纲统计
        const stats = analyzeOutline(processedOutline);
        
        new Notice(`大纲生成完成！共 ${stats.totalItems} 个条目，${stats.maxDepth} 级层次`);
        
        return {
            success: true,
            outline: processedOutline,
            stats,
            propertyName
        };
        
    } catch (error) {
        console.error('大纲生成失败:', error);
        new Notice(`大纲生成失败: ${error.message}`);
        return { success: false, error: error.message };
    }
}

/**
 * 调用大纲生成API
 * @param {string} prompt - 生成提示
 * @param {string} token - API密钥
 * @param {string} modelType - 模型类型
 * @returns {Promise<string>} 大纲结果
 */
async function callOutlineAPI(prompt, token, modelType) {
    const requestBody = {
        model: modelType,
        messages: [
            {
                role: "system",
                content: "你是一个专业的文档结构分析专家，擅长为各种类型的文章创建清晰、逻辑性强的大纲。你的大纲应该能够帮助读者快速理解文章的整体结构和核心内容�?
            },
            {
                role: "user",
                content: prompt
            }
        ],
        temperature: 0.3,
        max_tokens: 2000
    };
    
    const response = await fetch('https://open.bigmodel.cn/api/paas/v4/chat/completions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
    }
    
    const data = await response.json();
    return data.choices[0].message.content;
}

/**
 * 处理大纲字符�? * @param {string} outlineString - 原始大纲字符�? * @returns {Object} 处理后的大纲对象
 */
function processOutline(outlineString) {
    const lines = outlineString.split('\n').filter(line => line.trim());
    const outline = {
        title: '文章大纲',
        items: [],
        raw: outlineString
    };
    
    for (const line of lines) {
        const trimmedLine = line.trim();
        if (!trimmedLine) continue;
        
        // 检测大纲层�?        const level = detectOutlineLevel(trimmedLine);
        const text = cleanOutlineText(trimmedLine);
        
        if (text) {
            outline.items.push({
                level,
                text,
                raw: trimmedLine
            });
        }
    }
    
    return outline;
}

/**
 * 检测大纲层�? * @param {string} line - 大纲�? * @returns {number} 层级深度
 */
function detectOutlineLevel(line) {
    // 检测数字编号格�?(1. 1.1 1.1.1)
    const numberMatch = line.match(/^(\d+\.)+/);
    if (numberMatch) {
        return numberMatch[0].split('.').length - 1;
    }
    
    // 检测罗马数字格�?(I. A. 1. a.)
    if (/^[IVX]+\./i.test(line)) return 1;
    if (/^[A-Z]\./i.test(line)) return 2;
    if (/^\d+\./i.test(line)) return 3;
    if (/^[a-z]\./i.test(line)) return 4;
    
    // 检测缩�?    const indentMatch = line.match(/^(\s*)/);;
    if (indentMatch) {
        const indentLevel = Math.floor(indentMatch[1].length / 2) + 1;
        return Math.min(indentLevel, 6);
    }
    
    // 检测Markdown标题
    const headerMatch = line.match(/^#+/);
    if (headerMatch) {
        return headerMatch[0].length;
    }
    
    return 1;
}

/**
 * 清理大纲文本
 * @param {string} text - 原始文本
 * @returns {string} 清理后的文本
 */
function cleanOutlineText(text) {
    return text
        .replace(/^[\s\d\.IVXivx\-\*\+#]+/, '') // 移除编号和标�?        .replace(/^[A-Za-z]\.\s*/, '') // 移除字母编号
        .trim();
}

/**
 * 分析大纲统计信息
 * @param {Object} outline - 大纲对象
 * @returns {Object} 统计信息
 */
function analyzeOutline(outline) {
    const items = outline.items || [];
    const levels = items.map(item => item.level);
    
    return {
        totalItems: items.length,
        maxDepth: Math.max(...levels, 0),
        levelCounts: levels.reduce((acc, level) => {
            acc[level] = (acc[level] || 0) + 1;
            return acc;
        }, {})
    };
}

/**
 * 智能大纲生成（基于文档类型）
 * @param {string} token - API密钥
 * @param {string} propertyName - 属性名
 * @param {string} documentType - 文档类型
 * @param {number} outlineDepth - 大纲深度
 * @param {string} modelType - 模型类型
 * @returns {Promise<Object>} 生成结果
 */
async function smartOutline(token, propertyName, documentType = 'general', outlineDepth = 3, modelType = 'GLM-4-Flash') {
    const typePrompts = {
        'academic': '学术论文大纲，重点突出：研究背景、文献综述、研究方法、结果分析、结论讨�?,
        'business': '商业文档大纲，重点突出：执行摘要、市场分析、策略规划、实施方案、风险评�?,
        'technical': '技术文档大纲，重点突出：技术概述、系统架构、实现细节、测试验证、部署指�?,
        'report': '报告文档大纲，重点突出：概述、背景、发现、分析、建议、结�?,
        'tutorial': '教程文档大纲，重点突出：学习目标、基础知识、步骤指导、实践练习、总结回顾',
        'article': '文章大纲，重点突出：引言、主要观点、论证过程、案例分析、总结',
        'general': '通用文档大纲，重点突出：主要内容、逻辑结构、关键信�?
    };
    
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
        throw new Error('请先打开一个文�?);
    }
    
    const content = await app.vault.read(activeFile);
    const typePrompt = typePrompts[documentType] || typePrompts['general'];
    
    const prompt = `${typePrompt}\n\n请为以下${documentType}类型的文档生�?{outlineDepth}级大纲：\n\n${content}`;
    
    const outline = await callOutlineAPI(prompt, token, modelType);
    const processedOutline = processOutline(outline);
    
    await saveToProperty(activeFile, propertyName, processedOutline);
    
    const stats = analyzeOutline(processedOutline);
    
    return {
        success: true,
        documentType,
        outline: processedOutline,
        stats,
        propertyName
    };
}

/**
 * 交互式大纲生�? * @param {string} token - API密钥
 * @param {string} propertyName - 属性名
 * @param {Object} options - 选项配置
 * @param {string} modelType - 模型类型
 * @returns {Promise<Object>} 生成结果
 */
async function interactiveOutline(token, propertyName, options = {}, modelType = 'GLM-4-Flash') {
    const {
        includeDetails = false,
        includePageNumbers = false,
        style = 'hierarchical', // hierarchical, bullet, numbered
        focus = 'balanced' // structure, content, balanced
    } = options;
    
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
        throw new Error('请先打开一个文�?);
    }
    
    const content = await app.vault.read(activeFile);
    
    let prompt = `请为以下文章生成大纲，配置要求：\n`;
    
    if (includeDetails) {
        prompt += `- 包含详细说明和要点\n`;
    }
    
    if (includePageNumbers) {
        prompt += `- 包含页码或章节引用\n`;
    }
    
    prompt += `- 大纲样式�?{style}\n`;
    prompt += `- 重点关注�?{focus}\n\n`;
    
    prompt += `文章内容：\n${content}`;
    
    const outline = await callOutlineAPI(prompt, token, modelType);
    const processedOutline = processOutline(outline);
    
    // 添加配置信息
    processedOutline.options = options;
    
    await saveToProperty(activeFile, propertyName, processedOutline);
    
    const stats = analyzeOutline(processedOutline);
    
    return {
        success: true,
        outline: processedOutline,
        stats,
        options,
        propertyName
    };
}

/**
 * 批量大纲生成
 * @param {string} token - API密钥
 * @param {string} filePattern - 文件匹配模式
 * @param {number} outlineDepth - 大纲深度
 * @param {string} modelType - 模型类型
 * @returns {Promise<Object>} 批量生成结果
 */
async function batchOutline(token, filePattern, outlineDepth = 3, modelType = 'GLM-4-Flash') {
    try {
        const vault = app.vault;
        const files = vault.getMarkdownFiles();
        
        const matchedFiles = files.filter(file => {
            if (filePattern === '*.md') return true;
            return file.path.includes(filePattern);
        });
        
        if (matchedFiles.length === 0) {
            throw new Error('没有找到匹配的文�?);
        }
        
        new Notice(`开始批量生成大纲，�?${matchedFiles.length} 个文�?..`);
        
        const results = [];
        let successCount = 0;
        
        for (let i = 0; i < matchedFiles.length; i++) {
            const file = matchedFiles[i];
            try {
                console.log(`处理文件 ${i + 1}/${matchedFiles.length}: ${file.name}`);
                
                const content = await vault.read(file);
                if (!content.trim()) continue;
                
                const prompt = `请为以下文章生成${outlineDepth}级大纲：\n\n${content}`;
                const outline = await callOutlineAPI(prompt, token, modelType);
                const processedOutline = processOutline(outline);
                
                await saveToProperty(file, 'AI大纲', processedOutline);
                
                const stats = analyzeOutline(processedOutline);
                
                results.push({
                    file: file.name,
                    success: true,
                    stats
                });
                
                successCount++;
                
                // 添加延迟
                await new Promise(resolve => setTimeout(resolve, 1500));
                
            } catch (error) {
                console.error(`处理文件 ${file.name} 失败:`, error);
                results.push({
                    file: file.name,
                    success: false,
                    error: error.message
                });
            }
        }
        
        new Notice(`批量大纲生成完成！成功：${successCount}/${matchedFiles.length}`);
        
        return {
            success: true,
            totalFiles: matchedFiles.length,
            successCount,
            results
        };
        
    } catch (error) {
        console.error('批量大纲生成失败:', error);
        new Notice(`批量大纲生成失败: ${error.message}`);
        return { success: false, error: error.message };
    }
}

/**
 * 保存到文件属�? * @param {Object} file - 文件对象
 * @param {string} propertyName - 属性名
 * @param {Object} outline - 大纲对象
 */
async function saveToProperty(file, propertyName, outline) {
    // 格式化大纲为Markdown
    const formattedOutline = outline.items.map(item => {
        const indent = '  '.repeat(item.level - 1);
        return `${indent}- ${item.text}`;
    }).join('\n');
    
    await app.fileManager.processFrontMatter(file, (frontmatter) => {
        frontmatter[propertyName] = formattedOutline;
        frontmatter[`${propertyName}_stats`] = analyzeOutline(outline);
    });
}

// 导出函数
module.exports = {
    aiOutline,
    smartOutline,
    interactiveOutline,
    batchOutline
};

// 使用说明
console.log(`
AI大纲生成脚本已加载！

主要函数�?1. aiOutline(token, propertyName, outlineDepth, modelType)
2. smartOutline(token, propertyName, documentType, outlineDepth, modelType)
3. interactiveOutline(token, propertyName, options, modelType)
4. batchOutline(token, filePattern, outlineDepth, modelType)

文档类型�?- academic: 学术论文
- business: 商业文档
- technical: 技术文�?- report: 报告文档
- tutorial: 教程文档
- article: 文章
- general: 通用文档

使用示例�?// 生成3级大�?aiOutline('your-token', '文章大纲', 3)

// 生成学术论文大纲
smartOutline('your-token', '论文大纲', 'academic', 4)

// 交互式大纲生�?interactiveOutline('your-token', '详细大纲', {includeDetails: true, style: 'numbered'})

// 批量生成大纲
batchOutline('your-token', '*.md', 3)
`);
