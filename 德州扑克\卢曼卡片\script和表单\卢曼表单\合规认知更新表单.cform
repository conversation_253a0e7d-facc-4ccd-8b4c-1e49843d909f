{"id": "compliance-cognition-update-form", "fields": [{"id": "date", "label": "日期", "type": "text", "defaultValue": "{{date:YYYY-MM-DD}}", "description": "认知更新的日期"}, {"id": "old-understanding", "label": "被颠覆的旧理解", "type": "textarea", "rows": 3, "description": "原有的合规认知"}, {"id": "new-evidence", "label": "新证据来源", "type": "textarea", "rows": 3, "description": "新法规、判例或监管指导"}, {"id": "update-path", "label": "认知更新路径", "type": "textarea", "rows": 4, "description": "思考过程和更新路径"}, {"id": "practice-adjustment", "label": "实务调整", "type": "textarea", "rows": 3, "description": "需要修正的合规实践"}, {"id": "related-cases", "label": "关联案例", "type": "text", "description": "其他认知更新案例的链接"}], "action": {"id": "generate-compliance-cognition-update", "type": "runScript", "targetFolder": "", "fileName": "", "scriptSource": "inline", "code": "async function entry() {\n  const { form } = this.$context;\n  \n  const template = `### ${form.date} 合规认知迭代\n**被颠覆的旧理解**：${form['old-understanding']}\n**新证据来源**：${form['new-evidence']}\n**认知更新路径**：${form['update-path']}\n**实务调整**：我需要修正的合规实践${form['practice-adjustment']}\n→ 关联案例：[[${form['related-cases']}]]`;\n  \n  return template;\n}\n\nexports.default = {\n  entry: entry,\n};"}, "title": "合规认知更新表单"}