/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var Yi=Object.create;var or=Object.defineProperty;var Hi=Object.getOwnPropertyDescriptor;var Gi=Object.getOwnPropertyNames;var Vi=Object.getPrototypeOf,Wi=Object.prototype.hasOwnProperty;var Fn=r=>or(r,"__esModule",{value:!0});var Ki=(r,e)=>{Fn(r);for(var t in e)or(r,t,{get:e[t],enumerable:!0})},Ui=(r,e,t)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of Gi(e))!Wi.call(r,n)&&n!=="default"&&or(r,n,{get:()=>e[n],enumerable:!(t=Hi(e,n))||t.enumerable});return r},V=r=>Ui(Fn(or(r!=null?Yi(Vi(r)):{},"default",r&&r.__esModule&&"default"in r?{get:()=>r.default,enumerable:!0}:{value:r,enumerable:!0})),r);var Mn=(()=>{for(var r=new Uint8Array(128),e=0;e<64;e++)r[e<26?e+65:e<52?e+71:e<62?e-4:e*4-205]=e;return t=>{for(var n=t.length,o=new Uint8Array((n-(t[n-1]=="=")-(t[n-2]=="="))*3/4|0),s=0,a=0;s<n;){var A=r[t.charCodeAt(s++)],c=r[t.charCodeAt(s++)],d=r[t.charCodeAt(s++)],f=r[t.charCodeAt(s++)];o[a++]=A<<2|c>>4,o[a++]=c<<4|d>>2,o[a++]=d<<6|f}return o}})();Ki(exports,{default:()=>hn});var vr=V(require("obsidian"));var M=V(require("obsidian"));var Pn=V(require("obsidian"));function K(r){let e=new Pn.Notice("",8e3);r instanceof w&&r.console_msg?(e.noticeEl.innerHTML=`<b>Templater Error</b>:<br/>${r.message}<br/>Check console for more information`,console.error("Templater Error:",r.message,`
`,r.console_msg)):e.noticeEl.innerHTML=`<b>Templater Error</b>:<br/>${r.message}`}var w=class extends Error{constructor(e,t){super(e);this.console_msg=t;this.name=this.constructor.name,Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}};async function ye(r,e){try{return await r()}catch(t){return t instanceof w?K(t):K(new w(e,t.message)),null}}function ce(r,e){try{return r()}catch(t){return K(new w(e,t.message)),null}}var Ce=V(require("obsidian"));function sr(r){return new Promise(e=>setTimeout(e,r))}function Bn(r){return r.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function qn(){return/(<%(?:-|_)?\s*[*~]{0,1})\+((?:.|\s)*?%>)/g}function zi(r,e){e=(0,Ce.normalizePath)(e);let t=r.vault.getAbstractFileByPath(e);if(!t)throw new w(`Folder "${e}" doesn't exist`);if(!(t instanceof Ce.TFolder))throw new w(`${e} is a file, not a folder`);return t}function lt(r,e){e=(0,Ce.normalizePath)(e);let t=r.vault.getAbstractFileByPath(e);if(!t)throw new w(`File "${e}" doesn't exist`);if(!(t instanceof Ce.TFile))throw new w(`${e} is a folder, not a file`);return t}function Ie(r,e){let t=zi(r,e),n=[];return Ce.Vault.recurseChildren(t,o=>{o instanceof Ce.TFile&&n.push(o)}),n.sort((o,s)=>o.path.localeCompare(s.path)),n}function pt(r,e,t){if(t<0||t===r.length)return;let n=r[e];r[e]=r[t],r[t]=n}function xt(r){return r.workspace.activeEditor?.file??r.workspace.getActiveFile()}function On(r){let e=r.lastIndexOf("/");return e!==-1?r.slice(0,e):""}function kr(r){return r!==null&&typeof r=="object"}function Cn(r){let e=r.toString(),t=e.indexOf("(");return e.substring(t+1,e.indexOf(")")).replace(/ /g,"").split(",")}var ii=V(require("obsidian"));var ri=V(require("obsidian"));var W="top",ee="bottom",Q="right",U="left",ar="auto",et=[W,ee,Q,U],We="start",ct="end",In="clippingParents",lr="viewport",Et="popper",Sn="reference",Tr=et.reduce(function(r,e){return r.concat([e+"-"+We,e+"-"+ct])},[]),pr=[].concat(et,[ar]).reduce(function(r,e){return r.concat([e,e+"-"+We,e+"-"+ct])},[]),Ji="beforeRead",Xi="read",Qi="afterRead",Zi="beforeMain",eo="main",to="afterMain",ro="beforeWrite",no="write",io="afterWrite",Dn=[Ji,Xi,Qi,Zi,eo,to,ro,no,io];function re(r){return r?(r.nodeName||"").toLowerCase():null}function L(r){if(r==null)return window;if(r.toString()!=="[object Window]"){var e=r.ownerDocument;return e&&e.defaultView||window}return r}function he(r){var e=L(r).Element;return r instanceof e||r instanceof Element}function te(r){var e=L(r).HTMLElement;return r instanceof e||r instanceof HTMLElement}function kt(r){if(typeof ShadowRoot=="undefined")return!1;var e=L(r).ShadowRoot;return r instanceof e||r instanceof ShadowRoot}function oo(r){var e=r.state;Object.keys(e.elements).forEach(function(t){var n=e.styles[t]||{},o=e.attributes[t]||{},s=e.elements[t];!te(s)||!re(s)||(Object.assign(s.style,n),Object.keys(o).forEach(function(a){var A=o[a];A===!1?s.removeAttribute(a):s.setAttribute(a,A===!0?"":A)}))})}function so(r){var e=r.state,t={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,t.popper),e.styles=t,e.elements.arrow&&Object.assign(e.elements.arrow.style,t.arrow),function(){Object.keys(e.elements).forEach(function(n){var o=e.elements[n],s=e.attributes[n]||{},a=Object.keys(e.styles.hasOwnProperty(n)?e.styles[n]:t[n]),A=a.reduce(function(c,d){return c[d]="",c},{});!te(o)||!re(o)||(Object.assign(o.style,A),Object.keys(s).forEach(function(c){o.removeAttribute(c)}))})}}var $n={name:"applyStyles",enabled:!0,phase:"write",fn:oo,effect:so,requires:["computeStyles"]};function ne(r){return r.split("-")[0]}var xe=Math.max,At=Math.min,Ke=Math.round;function Tt(){var r=navigator.userAgentData;return r!=null&&r.brands&&Array.isArray(r.brands)?r.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function Dt(){return!/^((?!chrome|android).)*safari/i.test(Tt())}function je(r,e,t){e===void 0&&(e=!1),t===void 0&&(t=!1);var n=r.getBoundingClientRect(),o=1,s=1;e&&te(r)&&(o=r.offsetWidth>0&&Ke(n.width)/r.offsetWidth||1,s=r.offsetHeight>0&&Ke(n.height)/r.offsetHeight||1);var a=he(r)?L(r):window,A=a.visualViewport,c=!Dt()&&t,d=(n.left+(c&&A?A.offsetLeft:0))/o,f=(n.top+(c&&A?A.offsetTop:0))/s,b=n.width/o,k=n.height/s;return{width:b,height:k,top:f,right:d+b,bottom:f+k,left:d,x:d,y:f}}function ut(r){var e=je(r),t=r.offsetWidth,n=r.offsetHeight;return Math.abs(e.width-t)<=1&&(t=e.width),Math.abs(e.height-n)<=1&&(n=e.height),{x:r.offsetLeft,y:r.offsetTop,width:t,height:n}}function $t(r,e){var t=e.getRootNode&&e.getRootNode();if(r.contains(e))return!0;if(t&&kt(t)){var n=e;do{if(n&&r.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function Ae(r){return L(r).getComputedStyle(r)}function Fr(r){return["table","td","th"].indexOf(re(r))>=0}function ie(r){return((he(r)?r.ownerDocument:r.document)||window.document).documentElement}function Ue(r){return re(r)==="html"?r:r.assignedSlot||r.parentNode||(kt(r)?r.host:null)||ie(r)}function Nn(r){return!te(r)||Ae(r).position==="fixed"?null:r.offsetParent}function ao(r){var e=/firefox/i.test(Tt()),t=/Trident/i.test(Tt());if(t&&te(r)){var n=Ae(r);if(n.position==="fixed")return null}var o=Ue(r);for(kt(o)&&(o=o.host);te(o)&&["html","body"].indexOf(re(o))<0;){var s=Ae(o);if(s.transform!=="none"||s.perspective!=="none"||s.contain==="paint"||["transform","perspective"].indexOf(s.willChange)!==-1||e&&s.willChange==="filter"||e&&s.filter&&s.filter!=="none")return o;o=o.parentNode}return null}function Ee(r){for(var e=L(r),t=Nn(r);t&&Fr(t)&&Ae(t).position==="static";)t=Nn(t);return t&&(re(t)==="html"||re(t)==="body"&&Ae(t).position==="static")?e:t||ao(r)||e}function mt(r){return["top","bottom"].indexOf(r)>=0?"x":"y"}function ft(r,e,t){return xe(r,At(e,t))}function Rn(r,e,t){var n=ft(r,e,t);return n>t?t:n}function Nt(){return{top:0,right:0,bottom:0,left:0}}function Rt(r){return Object.assign({},Nt(),r)}function Lt(r,e){return e.reduce(function(t,n){return t[n]=r,t},{})}var lo=function(e,t){return e=typeof e=="function"?e(Object.assign({},t.rects,{placement:t.placement})):e,Rt(typeof e!="number"?e:Lt(e,et))};function po(r){var e,t=r.state,n=r.name,o=r.options,s=t.elements.arrow,a=t.modifiersData.popperOffsets,A=ne(t.placement),c=mt(A),d=[U,Q].indexOf(A)>=0,f=d?"height":"width";if(!(!s||!a)){var b=lo(o.padding,t),k=ut(s),x=c==="y"?W:U,O=c==="y"?ee:Q,P=t.rects.reference[f]+t.rects.reference[c]-a[c]-t.rects.popper[f],j=a[c]-t.rects.reference[c],B=Ee(s),Y=B?c==="y"?B.clientHeight||0:B.clientWidth||0:0,N=P/2-j/2,T=b[x],I=Y-k[f]-b[O],C=Y/2-k[f]/2+N,R=ft(T,C,I),z=c;t.modifiersData[n]=(e={},e[z]=R,e.centerOffset=R-C,e)}}function co(r){var e=r.state,t=r.options,n=t.element,o=n===void 0?"[data-popper-arrow]":n;o!=null&&(typeof o=="string"&&(o=e.elements.popper.querySelector(o),!o)||!$t(e.elements.popper,o)||(e.elements.arrow=o))}var Ln={name:"arrow",enabled:!0,phase:"main",fn:po,effect:co,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function _e(r){return r.split("-")[1]}var Ao={top:"auto",right:"auto",bottom:"auto",left:"auto"};function uo(r,e){var t=r.x,n=r.y,o=e.devicePixelRatio||1;return{x:Ke(t*o)/o||0,y:Ke(n*o)/o||0}}function Yn(r){var e,t=r.popper,n=r.popperRect,o=r.placement,s=r.variation,a=r.offsets,A=r.position,c=r.gpuAcceleration,d=r.adaptive,f=r.roundOffsets,b=r.isFixed,k=a.x,x=k===void 0?0:k,O=a.y,P=O===void 0?0:O,j=typeof f=="function"?f({x,y:P}):{x,y:P};x=j.x,P=j.y;var B=a.hasOwnProperty("x"),Y=a.hasOwnProperty("y"),N=U,T=W,I=window;if(d){var C=Ee(t),R="clientHeight",z="clientWidth";if(C===L(t)&&(C=ie(t),Ae(C).position!=="static"&&A==="absolute"&&(R="scrollHeight",z="scrollWidth")),C=C,o===W||(o===U||o===Q)&&s===ct){T=ee;var J=b&&C===I&&I.visualViewport?I.visualViewport.height:C[R];P-=J-n.height,P*=c?1:-1}if(o===U||(o===W||o===ee)&&s===ct){N=Q;var G=b&&C===I&&I.visualViewport?I.visualViewport.width:C[z];x-=G-n.width,x*=c?1:-1}}var m=Object.assign({position:A},d&&Ao),h=f===!0?uo({x,y:P},L(t)):{x,y:P};if(x=h.x,P=h.y,c){var p;return Object.assign({},m,(p={},p[T]=Y?"0":"",p[N]=B?"0":"",p.transform=(I.devicePixelRatio||1)<=1?"translate("+x+"px, "+P+"px)":"translate3d("+x+"px, "+P+"px, 0)",p))}return Object.assign({},m,(e={},e[T]=Y?P+"px":"",e[N]=B?x+"px":"",e.transform="",e))}function mo(r){var e=r.state,t=r.options,n=t.gpuAcceleration,o=n===void 0?!0:n,s=t.adaptive,a=s===void 0?!0:s,A=t.roundOffsets,c=A===void 0?!0:A,d={placement:ne(e.placement),variation:_e(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:o,isFixed:e.options.strategy==="fixed"};e.modifiersData.popperOffsets!=null&&(e.styles.popper=Object.assign({},e.styles.popper,Yn(Object.assign({},d,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:a,roundOffsets:c})))),e.modifiersData.arrow!=null&&(e.styles.arrow=Object.assign({},e.styles.arrow,Yn(Object.assign({},d,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:c})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})}var Hn={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:mo,data:{}};var cr={passive:!0};function fo(r){var e=r.state,t=r.instance,n=r.options,o=n.scroll,s=o===void 0?!0:o,a=n.resize,A=a===void 0?!0:a,c=L(e.elements.popper),d=[].concat(e.scrollParents.reference,e.scrollParents.popper);return s&&d.forEach(function(f){f.addEventListener("scroll",t.update,cr)}),A&&c.addEventListener("resize",t.update,cr),function(){s&&d.forEach(function(f){f.removeEventListener("scroll",t.update,cr)}),A&&c.removeEventListener("resize",t.update,cr)}}var Gn={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:fo,data:{}};var go={left:"right",right:"left",bottom:"top",top:"bottom"};function Ft(r){return r.replace(/left|right|bottom|top/g,function(e){return go[e]})}var ho={start:"end",end:"start"};function Ar(r){return r.replace(/start|end/g,function(e){return ho[e]})}function dt(r){var e=L(r),t=e.pageXOffset,n=e.pageYOffset;return{scrollLeft:t,scrollTop:n}}function gt(r){return je(ie(r)).left+dt(r).scrollLeft}function Mr(r,e){var t=L(r),n=ie(r),o=t.visualViewport,s=n.clientWidth,a=n.clientHeight,A=0,c=0;if(o){s=o.width,a=o.height;var d=Dt();(d||!d&&e==="fixed")&&(A=o.offsetLeft,c=o.offsetTop)}return{width:s,height:a,x:A+gt(r),y:c}}function Pr(r){var e,t=ie(r),n=dt(r),o=(e=r.ownerDocument)==null?void 0:e.body,s=xe(t.scrollWidth,t.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),a=xe(t.scrollHeight,t.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),A=-n.scrollLeft+gt(r),c=-n.scrollTop;return Ae(o||t).direction==="rtl"&&(A+=xe(t.clientWidth,o?o.clientWidth:0)-s),{width:s,height:a,x:A,y:c}}function ht(r){var e=Ae(r),t=e.overflow,n=e.overflowX,o=e.overflowY;return/auto|scroll|overlay|hidden/.test(t+o+n)}function ur(r){return["html","body","#document"].indexOf(re(r))>=0?r.ownerDocument.body:te(r)&&ht(r)?r:ur(Ue(r))}function tt(r,e){var t;e===void 0&&(e=[]);var n=ur(r),o=n===((t=r.ownerDocument)==null?void 0:t.body),s=L(n),a=o?[s].concat(s.visualViewport||[],ht(n)?n:[]):n,A=e.concat(a);return o?A:A.concat(tt(Ue(a)))}function Mt(r){return Object.assign({},r,{left:r.x,top:r.y,right:r.x+r.width,bottom:r.y+r.height})}function jo(r,e){var t=je(r,!1,e==="fixed");return t.top=t.top+r.clientTop,t.left=t.left+r.clientLeft,t.bottom=t.top+r.clientHeight,t.right=t.left+r.clientWidth,t.width=r.clientWidth,t.height=r.clientHeight,t.x=t.left,t.y=t.top,t}function Vn(r,e,t){return e===lr?Mt(Mr(r,t)):he(e)?jo(e,t):Mt(Pr(ie(r)))}function _o(r){var e=tt(Ue(r)),t=["absolute","fixed"].indexOf(Ae(r).position)>=0,n=t&&te(r)?Ee(r):r;return he(n)?e.filter(function(o){return he(o)&&$t(o,n)&&re(o)!=="body"}):[]}function Br(r,e,t,n){var o=e==="clippingParents"?_o(r):[].concat(e),s=[].concat(o,[t]),a=s[0],A=s.reduce(function(c,d){var f=Vn(r,d,n);return c.top=xe(f.top,c.top),c.right=At(f.right,c.right),c.bottom=At(f.bottom,c.bottom),c.left=xe(f.left,c.left),c},Vn(r,a,n));return A.width=A.right-A.left,A.height=A.bottom-A.top,A.x=A.left,A.y=A.top,A}function Yt(r){var e=r.reference,t=r.element,n=r.placement,o=n?ne(n):null,s=n?_e(n):null,a=e.x+e.width/2-t.width/2,A=e.y+e.height/2-t.height/2,c;switch(o){case W:c={x:a,y:e.y-t.height};break;case ee:c={x:a,y:e.y+e.height};break;case Q:c={x:e.x+e.width,y:A};break;case U:c={x:e.x-t.width,y:A};break;default:c={x:e.x,y:e.y}}var d=o?mt(o):null;if(d!=null){var f=d==="y"?"height":"width";switch(s){case We:c[d]=c[d]-(e[f]/2-t[f]/2);break;case ct:c[d]=c[d]+(e[f]/2-t[f]/2);break;default:}}return c}function ke(r,e){e===void 0&&(e={});var t=e,n=t.placement,o=n===void 0?r.placement:n,s=t.strategy,a=s===void 0?r.strategy:s,A=t.boundary,c=A===void 0?In:A,d=t.rootBoundary,f=d===void 0?lr:d,b=t.elementContext,k=b===void 0?Et:b,x=t.altBoundary,O=x===void 0?!1:x,P=t.padding,j=P===void 0?0:P,B=Rt(typeof j!="number"?j:Lt(j,et)),Y=k===Et?Sn:Et,N=r.rects.popper,T=r.elements[O?Y:k],I=Br(he(T)?T:T.contextElement||ie(r.elements.popper),c,f,a),C=je(r.elements.reference),R=Yt({reference:C,element:N,strategy:"absolute",placement:o}),z=Mt(Object.assign({},N,R)),J=k===Et?z:C,G={top:I.top-J.top+B.top,bottom:J.bottom-I.bottom+B.bottom,left:I.left-J.left+B.left,right:J.right-I.right+B.right},m=r.modifiersData.offset;if(k===Et&&m){var h=m[o];Object.keys(G).forEach(function(p){var we=[Q,ee].indexOf(p)>=0?1:-1,me=[W,ee].indexOf(p)>=0?"y":"x";G[p]+=h[me]*we})}return G}function qr(r,e){e===void 0&&(e={});var t=e,n=t.placement,o=t.boundary,s=t.rootBoundary,a=t.padding,A=t.flipVariations,c=t.allowedAutoPlacements,d=c===void 0?pr:c,f=_e(n),b=f?A?Tr:Tr.filter(function(O){return _e(O)===f}):et,k=b.filter(function(O){return d.indexOf(O)>=0});k.length===0&&(k=b);var x=k.reduce(function(O,P){return O[P]=ke(r,{placement:P,boundary:o,rootBoundary:s,padding:a})[ne(P)],O},{});return Object.keys(x).sort(function(O,P){return x[O]-x[P]})}function vo(r){if(ne(r)===ar)return[];var e=Ft(r);return[Ar(r),e,Ar(e)]}function wo(r){var e=r.state,t=r.options,n=r.name;if(!e.modifiersData[n]._skip){for(var o=t.mainAxis,s=o===void 0?!0:o,a=t.altAxis,A=a===void 0?!0:a,c=t.fallbackPlacements,d=t.padding,f=t.boundary,b=t.rootBoundary,k=t.altBoundary,x=t.flipVariations,O=x===void 0?!0:x,P=t.allowedAutoPlacements,j=e.options.placement,B=ne(j),Y=B===j,N=c||(Y||!O?[Ft(j)]:vo(j)),T=[j].concat(N).reduce(function(y,_){return y.concat(ne(_)===ar?qr(e,{placement:_,boundary:f,rootBoundary:b,padding:d,flipVariations:O,allowedAutoPlacements:P}):_)},[]),I=e.rects.reference,C=e.rects.popper,R=new Map,z=!0,J=T[0],G=0;G<T.length;G++){var m=T[G],h=ne(m),p=_e(m)===We,we=[W,ee].indexOf(h)>=0,me=we?"width":"height",oe=ke(e,{placement:m,boundary:f,rootBoundary:b,altBoundary:k,padding:d}),se=we?p?Q:U:p?ee:W;I[me]>C[me]&&(se=Ft(se));var Pe=Ft(se),fe=[];if(s&&fe.push(oe[h]<=0),A&&fe.push(oe[se]<=0,oe[Pe]<=0),fe.every(function(y){return y})){J=m,z=!1;break}R.set(m,fe)}if(z)for(var _t=O?3:1,Be=function(_){var F=T.find(function(H){var Re=R.get(H);if(Re)return Re.slice(0,_).every(function(S){return S})});if(F)return J=F,"break"},Ne=_t;Ne>0;Ne--){var ae=Be(Ne);if(ae==="break")break}e.placement!==J&&(e.modifiersData[n]._skip=!0,e.placement=J,e.reset=!0)}}var Wn={name:"flip",enabled:!0,phase:"main",fn:wo,requiresIfExists:["offset"],data:{_skip:!1}};function Kn(r,e,t){return t===void 0&&(t={x:0,y:0}),{top:r.top-e.height-t.y,right:r.right-e.width+t.x,bottom:r.bottom-e.height+t.y,left:r.left-e.width-t.x}}function Un(r){return[W,Q,ee,U].some(function(e){return r[e]>=0})}function bo(r){var e=r.state,t=r.name,n=e.rects.reference,o=e.rects.popper,s=e.modifiersData.preventOverflow,a=ke(e,{elementContext:"reference"}),A=ke(e,{altBoundary:!0}),c=Kn(a,n),d=Kn(A,o,s),f=Un(c),b=Un(d);e.modifiersData[t]={referenceClippingOffsets:c,popperEscapeOffsets:d,isReferenceHidden:f,hasPopperEscaped:b},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":f,"data-popper-escaped":b})}var zn={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:bo};function yo(r,e,t){var n=ne(r),o=[U,W].indexOf(n)>=0?-1:1,s=typeof t=="function"?t(Object.assign({},e,{placement:r})):t,a=s[0],A=s[1];return a=a||0,A=(A||0)*o,[U,Q].indexOf(n)>=0?{x:A,y:a}:{x:a,y:A}}function xo(r){var e=r.state,t=r.options,n=r.name,o=t.offset,s=o===void 0?[0,0]:o,a=pr.reduce(function(f,b){return f[b]=yo(b,e.rects,s),f},{}),A=a[e.placement],c=A.x,d=A.y;e.modifiersData.popperOffsets!=null&&(e.modifiersData.popperOffsets.x+=c,e.modifiersData.popperOffsets.y+=d),e.modifiersData[n]=a}var Jn={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:xo};function Eo(r){var e=r.state,t=r.name;e.modifiersData[t]=Yt({reference:e.rects.reference,element:e.rects.popper,strategy:"absolute",placement:e.placement})}var Xn={name:"popperOffsets",enabled:!0,phase:"read",fn:Eo,data:{}};function Or(r){return r==="x"?"y":"x"}function ko(r){var e=r.state,t=r.options,n=r.name,o=t.mainAxis,s=o===void 0?!0:o,a=t.altAxis,A=a===void 0?!1:a,c=t.boundary,d=t.rootBoundary,f=t.altBoundary,b=t.padding,k=t.tether,x=k===void 0?!0:k,O=t.tetherOffset,P=O===void 0?0:O,j=ke(e,{boundary:c,rootBoundary:d,padding:b,altBoundary:f}),B=ne(e.placement),Y=_e(e.placement),N=!Y,T=mt(B),I=Or(T),C=e.modifiersData.popperOffsets,R=e.rects.reference,z=e.rects.popper,J=typeof P=="function"?P(Object.assign({},e.rects,{placement:e.placement})):P,G=typeof J=="number"?{mainAxis:J,altAxis:J}:Object.assign({mainAxis:0,altAxis:0},J),m=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,h={x:0,y:0};if(!!C){if(s){var p,we=T==="y"?W:U,me=T==="y"?ee:Q,oe=T==="y"?"height":"width",se=C[T],Pe=se+j[we],fe=se-j[me],_t=x?-z[oe]/2:0,Be=Y===We?R[oe]:z[oe],Ne=Y===We?-z[oe]:-R[oe],ae=e.elements.arrow,y=x&&ae?ut(ae):{width:0,height:0},_=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:Nt(),F=_[we],H=_[me],Re=ft(0,R[oe],y[oe]),S=N?R[oe]/2-_t-Re-F-G.mainAxis:Be-Re-F-G.mainAxis,le=N?-R[oe]/2+_t+Re+H+G.mainAxis:Ne+Re+H+G.mainAxis,it=e.elements.arrow&&Ee(e.elements.arrow),Ut=it?T==="y"?it.clientTop||0:it.clientLeft||0:0,Le=(p=m==null?void 0:m[T])!=null?p:0,Ye=se+S-Le-Ut,Xe=se+le-Le,ot=ft(x?At(Pe,Ye):Pe,se,x?xe(fe,Xe):fe);C[T]=ot,h[T]=ot-se}if(A){var zt,Jt=T==="x"?W:U,Xt=T==="x"?ee:Q,He=C[I],vt=I==="y"?"height":"width",Qt=He+j[Jt],Zt=He-j[Xt],Ot=[W,U].indexOf(B)!==-1,st=(zt=m==null?void 0:m[I])!=null?zt:0,er=Ot?Qt:He-R[vt]-z[vt]-st+G.altAxis,qe=Ot?He+R[vt]+z[vt]-st-G.altAxis:Zt,X=x&&Ot?Rn(er,He,qe):ft(x?er:Qt,He,x?qe:Zt);C[I]=X,h[I]=X-He}e.modifiersData[n]=h}}var Qn={name:"preventOverflow",enabled:!0,phase:"main",fn:ko,requiresIfExists:["offset"]};function Cr(r){return{scrollLeft:r.scrollLeft,scrollTop:r.scrollTop}}function Ir(r){return r===L(r)||!te(r)?dt(r):Cr(r)}function To(r){var e=r.getBoundingClientRect(),t=Ke(e.width)/r.offsetWidth||1,n=Ke(e.height)/r.offsetHeight||1;return t!==1||n!==1}function Sr(r,e,t){t===void 0&&(t=!1);var n=te(e),o=te(e)&&To(e),s=ie(e),a=je(r,o,t),A={scrollLeft:0,scrollTop:0},c={x:0,y:0};return(n||!n&&!t)&&((re(e)!=="body"||ht(s))&&(A=Ir(e)),te(e)?(c=je(e,!0),c.x+=e.clientLeft,c.y+=e.clientTop):s&&(c.x=gt(s))),{x:a.left+A.scrollLeft-c.x,y:a.top+A.scrollTop-c.y,width:a.width,height:a.height}}function Fo(r){var e=new Map,t=new Set,n=[];r.forEach(function(s){e.set(s.name,s)});function o(s){t.add(s.name);var a=[].concat(s.requires||[],s.requiresIfExists||[]);a.forEach(function(A){if(!t.has(A)){var c=e.get(A);c&&o(c)}}),n.push(s)}return r.forEach(function(s){t.has(s.name)||o(s)}),n}function Dr(r){var e=Fo(r);return Dn.reduce(function(t,n){return t.concat(e.filter(function(o){return o.phase===n}))},[])}function $r(r){var e;return function(){return e||(e=new Promise(function(t){Promise.resolve().then(function(){e=void 0,t(r())})})),e}}function Nr(r){var e=r.reduce(function(t,n){var o=t[n.name];return t[n.name]=o?Object.assign({},o,n,{options:Object.assign({},o.options,n.options),data:Object.assign({},o.data,n.data)}):n,t},{});return Object.keys(e).map(function(t){return e[t]})}var Zn={placement:"bottom",modifiers:[],strategy:"absolute"};function ei(){for(var r=arguments.length,e=new Array(r),t=0;t<r;t++)e[t]=arguments[t];return!e.some(function(n){return!(n&&typeof n.getBoundingClientRect=="function")})}function ti(r){r===void 0&&(r={});var e=r,t=e.defaultModifiers,n=t===void 0?[]:t,o=e.defaultOptions,s=o===void 0?Zn:o;return function(A,c,d){d===void 0&&(d=s);var f={placement:"bottom",orderedModifiers:[],options:Object.assign({},Zn,s),modifiersData:{},elements:{reference:A,popper:c},attributes:{},styles:{}},b=[],k=!1,x={state:f,setOptions:function(B){var Y=typeof B=="function"?B(f.options):B;P(),f.options=Object.assign({},s,f.options,Y),f.scrollParents={reference:he(A)?tt(A):A.contextElement?tt(A.contextElement):[],popper:tt(c)};var N=Dr(Nr([].concat(n,f.options.modifiers)));return f.orderedModifiers=N.filter(function(T){return T.enabled}),O(),x.update()},forceUpdate:function(){if(!k){var B=f.elements,Y=B.reference,N=B.popper;if(!!ei(Y,N)){f.rects={reference:Sr(Y,Ee(N),f.options.strategy==="fixed"),popper:ut(N)},f.reset=!1,f.placement=f.options.placement,f.orderedModifiers.forEach(function(G){return f.modifiersData[G.name]=Object.assign({},G.data)});for(var T=0;T<f.orderedModifiers.length;T++){if(f.reset===!0){f.reset=!1,T=-1;continue}var I=f.orderedModifiers[T],C=I.fn,R=I.options,z=R===void 0?{}:R,J=I.name;typeof C=="function"&&(f=C({state:f,options:z,name:J,instance:x})||f)}}}},update:$r(function(){return new Promise(function(j){x.forceUpdate(),j(f)})}),destroy:function(){P(),k=!0}};if(!ei(A,c))return x;x.setOptions(d).then(function(j){!k&&d.onFirstUpdate&&d.onFirstUpdate(j)});function O(){f.orderedModifiers.forEach(function(j){var B=j.name,Y=j.options,N=Y===void 0?{}:Y,T=j.effect;if(typeof T=="function"){var I=T({state:f,name:B,instance:x,options:N}),C=function(){};b.push(I||C)}})}function P(){b.forEach(function(j){return j()}),b=[]}return x}}var Mo=[Gn,Xn,Hn,$n,Jn,Wn,Qn,Ln,zn],Rr=ti({defaultModifiers:Mo});var Po=(r,e)=>(r%e+e)%e,ni=class{constructor(e,t,n){this.owner=e,this.containerEl=t,t.on("click",".suggestion-item",this.onSuggestionClick.bind(this)),t.on("mousemove",".suggestion-item",this.onSuggestionMouseover.bind(this)),n.register([],"ArrowUp",o=>{if(!o.isComposing)return this.setSelectedItem(this.selectedItem-1,!0),!1}),n.register([],"ArrowDown",o=>{if(!o.isComposing)return this.setSelectedItem(this.selectedItem+1,!0),!1}),n.register([],"Enter",o=>{if(!o.isComposing)return this.useSelectedItem(o),!1})}onSuggestionClick(e,t){e.preventDefault();let n=this.suggestions.indexOf(t);this.setSelectedItem(n,!1),this.useSelectedItem(e)}onSuggestionMouseover(e,t){let n=this.suggestions.indexOf(t);this.setSelectedItem(n,!1)}setSuggestions(e){this.containerEl.empty();let t=[];e.forEach(n=>{let o=this.containerEl.createDiv("suggestion-item");this.owner.renderSuggestion(n,o),t.push(o)}),this.values=e,this.suggestions=t,this.setSelectedItem(0,!1)}useSelectedItem(e){let t=this.values[this.selectedItem];t&&this.owner.selectSuggestion(t,e)}setSelectedItem(e,t){let n=Po(e,this.suggestions.length),o=this.suggestions[this.selectedItem],s=this.suggestions[n];o?.removeClass("is-selected"),s?.addClass("is-selected"),this.selectedItem=n,t&&s.scrollIntoView(!1)}},Ht=class{constructor(e,t){this.app=e,this.inputEl=t,this.scope=new ri.Scope,this.suggestEl=createDiv("suggestion-container");let n=this.suggestEl.createDiv("suggestion");this.suggest=new ni(this,n,this.scope),this.scope.register([],"Escape",this.close.bind(this)),this.inputEl.addEventListener("input",this.onInputChanged.bind(this)),this.inputEl.addEventListener("focus",this.onInputChanged.bind(this)),this.inputEl.addEventListener("blur",this.close.bind(this)),this.suggestEl.on("mousedown",".suggestion-container",o=>{o.preventDefault()})}onInputChanged(){let e=this.inputEl.value,t=this.getSuggestions(e);if(!t){this.close();return}t.length>0?(this.suggest.setSuggestions(t),this.open(this.app.dom.appContainerEl,this.inputEl)):this.close()}open(e,t){this.app.keymap.pushScope(this.scope),e.appendChild(this.suggestEl),this.popper=Rr(t,this.suggestEl,{placement:"bottom-start",modifiers:[{name:"sameWidth",enabled:!0,fn:({state:n,instance:o})=>{let s=`${n.rects.reference.width}px`;n.styles.popper.width!==s&&(n.styles.popper.width=s,o.update())},phase:"beforeWrite",requires:["computeStyles"]}]})}close(){this.app.keymap.popScope(this.scope),this.suggest.setSuggestions([]),this.popper&&this.popper.destroy(),this.suggestEl.detach()}};var Te;(function(t){t[t.TemplateFiles=0]="TemplateFiles",t[t.ScriptFiles=1]="ScriptFiles"})(Te||(Te={}));var Pt=class extends Ht{constructor(e,t,n){super(t.app,e);this.inputEl=e;this.plugin=t;this.mode=n}get_folder(e){switch(e){case 0:return this.plugin.settings.templates_folder;case 1:return this.plugin.settings.user_scripts_folder}}get_error_msg(e){switch(e){case 0:return"Templates folder doesn't exist";case 1:return"User Scripts folder doesn't exist"}}getSuggestions(e){let t=ce(()=>Ie(this.plugin.app,this.get_folder(this.mode)),this.get_error_msg(this.mode));if(!t)return[];let n=[],o=e.toLowerCase();return t.forEach(s=>{s instanceof ii.TFile&&s.extension==="md"&&s.path.toLowerCase().contains(o)&&n.push(s)}),n.slice(0,1e3)}renderSuggestion(e,t){t.setText(e.path)}selectSuggestion(e){this.inputEl.value=e.path,this.inputEl.trigger("input"),this.close()}};var oi=V(require("obsidian"));var Gt=class extends Ht{constructor(e,t){super(e,t)}getSuggestions(e){let t=this.app.vault.getAllLoadedFiles(),n=[],o=e.toLowerCase();return t.forEach(s=>{s instanceof oi.TFolder&&s.path.toLowerCase().contains(o)&&n.push(s)}),n.slice(0,1e3)}renderSuggestion(e,t){t.setText(e.path)}selectSuggestion(e){this.inputEl.value=e.path,this.inputEl.trigger("input"),this.close()}};var si={command_timeout:5,templates_folder:"",templates_pairs:[["",""]],trigger_on_file_creation:!1,auto_jump_to_cursor:!1,enable_system_commands:!1,shell_path:"",user_scripts_folder:"",enable_folder_templates:!0,folder_templates:[{folder:"",template:""}],enable_file_templates:!1,file_templates:[{regex:".*",template:""}],syntax_highlighting:!0,syntax_highlighting_mobile:!1,enabled_templates_hotkeys:[""],startup_templates:[""]},Lr=class extends M.PluginSettingTab{constructor(e){super(e.app,e);this.plugin=e}display(){this.containerEl.empty(),this.add_template_folder_setting(),this.add_internal_functions_setting(),this.add_syntax_highlighting_settings(),this.add_auto_jump_to_cursor(),this.add_trigger_on_new_file_creation_setting(),this.plugin.settings.trigger_on_file_creation&&(this.add_folder_templates_setting(),this.add_file_templates_setting()),this.add_templates_hotkeys_setting(),this.add_startup_templates_setting(),this.add_user_script_functions_setting(),this.add_user_system_command_functions_setting(),this.add_donating_setting()}add_template_folder_setting(){new M.Setting(this.containerEl).setName("Template folder location").setDesc("Files in this folder will be available as templates.").addSearch(e=>{new Gt(this.app,e.inputEl),e.setPlaceholder("Example: folder1/folder2").setValue(this.plugin.settings.templates_folder).onChange(t=>{this.plugin.settings.templates_folder=t,this.plugin.save_settings()}),e.containerEl.addClass("templater_search")})}add_internal_functions_setting(){let e=document.createDocumentFragment();e.append("Templater provides multiples predefined variables / functions that you can use.",e.createEl("br"),"Check the ",e.createEl("a",{href:"https://silentvoid13.github.io/Templater/",text:"documentation"})," to get a list of all the available internal variables / functions."),new M.Setting(this.containerEl).setName("Internal variables and functions").setDesc(e)}add_syntax_highlighting_settings(){let e=document.createDocumentFragment();e.append("Adds syntax highlighting for Templater commands in edit mode.");let t=document.createDocumentFragment();t.append("Adds syntax highlighting for Templater commands in edit mode on mobile. Use with caution: this may break live preview on mobile platforms."),new M.Setting(this.containerEl).setName("Syntax highlighting on desktop").setDesc(e).addToggle(n=>{n.setValue(this.plugin.settings.syntax_highlighting).onChange(o=>{this.plugin.settings.syntax_highlighting=o,this.plugin.save_settings(),this.plugin.event_handler.update_syntax_highlighting()})}),new M.Setting(this.containerEl).setName("Syntax highlighting on mobile").setDesc(t).addToggle(n=>{n.setValue(this.plugin.settings.syntax_highlighting_mobile).onChange(o=>{this.plugin.settings.syntax_highlighting_mobile=o,this.plugin.save_settings(),this.plugin.event_handler.update_syntax_highlighting()})})}add_auto_jump_to_cursor(){let e=document.createDocumentFragment();e.append("Automatically triggers ",e.createEl("code",{text:"tp.file.cursor"})," after inserting a template.",e.createEl("br"),"You can also set a hotkey to manually trigger ",e.createEl("code",{text:"tp.file.cursor"}),"."),new M.Setting(this.containerEl).setName("Automatic jump to cursor").setDesc(e).addToggle(t=>{t.setValue(this.plugin.settings.auto_jump_to_cursor).onChange(n=>{this.plugin.settings.auto_jump_to_cursor=n,this.plugin.save_settings()})})}add_trigger_on_new_file_creation_setting(){let e=document.createDocumentFragment();e.append("Templater will listen for the new file creation event, and, if it matches a rule you've set, replace every command it finds in the new file's content. ","This makes Templater compatible with other plugins like the Daily note core plugin, Calendar plugin, Review plugin, Note refactor plugin, etc. ",e.createEl("br"),e.createEl("br"),"Make sure to set up rules under either folder templates or file regex template below.",e.createEl("br"),e.createEl("br"),e.createEl("b",{text:"Warning: "}),"This can be dangerous if you create new files with unknown / unsafe content on creation. Make sure that every new file's content is safe on creation."),new M.Setting(this.containerEl).setName("Trigger Templater on new file creation").setDesc(e).addToggle(t=>{t.setValue(this.plugin.settings.trigger_on_file_creation).onChange(n=>{this.plugin.settings.trigger_on_file_creation=n,this.plugin.save_settings(),this.plugin.event_handler.update_trigger_file_on_creation(),this.display()})})}add_templates_hotkeys_setting(){new M.Setting(this.containerEl).setName("Template hotkeys").setHeading();let e=document.createDocumentFragment();e.append("Template hotkeys allows you to bind a template to a hotkey."),new M.Setting(this.containerEl).setDesc(e),this.plugin.settings.enabled_templates_hotkeys.forEach((t,n)=>{new M.Setting(this.containerEl).addSearch(s=>{new Pt(s.inputEl,this.plugin,Te.TemplateFiles),s.setPlaceholder("Example: folder1/template_file").setValue(t).onChange(a=>{if(a&&this.plugin.settings.enabled_templates_hotkeys.contains(a)){K(new w("This template is already bound to a hotkey"));return}this.plugin.command_handler.add_template_hotkey(this.plugin.settings.enabled_templates_hotkeys[n],a),this.plugin.settings.enabled_templates_hotkeys[n]=a,this.plugin.save_settings()}),s.containerEl.addClass("templater_search")}).addExtraButton(s=>{s.setIcon("any-key").setTooltip("Configure Hotkey").onClick(()=>{this.app.setting.openTabById("hotkeys");let a=this.app.setting.activeTab;a.searchComponent.inputEl.value=t,a.updateHotkeyVisibility()})}).addExtraButton(s=>{s.setIcon("up-chevron-glyph").setTooltip("Move up").onClick(()=>{pt(this.plugin.settings.enabled_templates_hotkeys,n,n-1),this.plugin.save_settings(),this.display()})}).addExtraButton(s=>{s.setIcon("down-chevron-glyph").setTooltip("Move down").onClick(()=>{pt(this.plugin.settings.enabled_templates_hotkeys,n,n+1),this.plugin.save_settings(),this.display()})}).addExtraButton(s=>{s.setIcon("cross").setTooltip("Delete").onClick(()=>{this.plugin.command_handler.remove_template_hotkey(this.plugin.settings.enabled_templates_hotkeys[n]),this.plugin.settings.enabled_templates_hotkeys.splice(n,1),this.plugin.save_settings(),this.display()})}).infoEl.remove()}),new M.Setting(this.containerEl).addButton(t=>{t.setButtonText("Add new hotkey for template").setCta().onClick(()=>{this.plugin.settings.enabled_templates_hotkeys.push(""),this.plugin.save_settings(),this.display()})})}add_folder_templates_setting(){new M.Setting(this.containerEl).setName("Folder templates").setHeading();let e=document.createDocumentFragment();e.append("Folder templates are triggered when a new ",e.createEl("strong",{text:"empty "}),"file is created in a given folder.",e.createEl("br"),"Templater will fill the empty file with the specified template.",e.createEl("br"),"The deepest match is used. A global default template would be defined on the root ",e.createEl("code",{text:"/"}),"."),new M.Setting(this.containerEl).setDesc(e);let t=document.createDocumentFragment();t.append("When enabled, Templater will make use of the folder templates defined below. This option is mutually exclusive with file regex templates below, so enabling one will disable the other."),new M.Setting(this.containerEl).setName("Enable folder templates").setDesc(t).addToggle(n=>{n.setValue(this.plugin.settings.enable_folder_templates).onChange(o=>{this.plugin.settings.enable_folder_templates=o,o&&(this.plugin.settings.enable_file_templates=!1),this.plugin.save_settings(),this.display()})}),!!this.plugin.settings.enable_folder_templates&&(this.plugin.settings.folder_templates.forEach((n,o)=>{new M.Setting(this.containerEl).addSearch(a=>{new Gt(this.app,a.inputEl),a.setPlaceholder("Folder").setValue(n.folder).onChange(A=>{if(A&&this.plugin.settings.folder_templates.some(c=>c.folder==A)){K(new w("This folder already has a template associated with it"));return}this.plugin.settings.folder_templates[o].folder=A,this.plugin.save_settings()}),a.containerEl.addClass("templater_search")}).addSearch(a=>{new Pt(a.inputEl,this.plugin,Te.TemplateFiles),a.setPlaceholder("Template").setValue(n.template).onChange(A=>{this.plugin.settings.folder_templates[o].template=A,this.plugin.save_settings()}),a.containerEl.addClass("templater_search")}).addExtraButton(a=>{a.setIcon("up-chevron-glyph").setTooltip("Move up").onClick(()=>{pt(this.plugin.settings.folder_templates,o,o-1),this.plugin.save_settings(),this.display()})}).addExtraButton(a=>{a.setIcon("down-chevron-glyph").setTooltip("Move down").onClick(()=>{pt(this.plugin.settings.folder_templates,o,o+1),this.plugin.save_settings(),this.display()})}).addExtraButton(a=>{a.setIcon("cross").setTooltip("Delete").onClick(()=>{this.plugin.settings.folder_templates.splice(o,1),this.plugin.save_settings(),this.display()})}).infoEl.remove()}),new M.Setting(this.containerEl).addButton(n=>{n.setButtonText("Add new folder template").setTooltip("Add additional folder template").setCta().onClick(()=>{this.plugin.settings.folder_templates.push({folder:"",template:""}),this.plugin.save_settings(),this.display()})}))}add_file_templates_setting(){new M.Setting(this.containerEl).setName("File regex templates").setHeading();let e=document.createDocumentFragment();e.append("File regex templates are triggered when a new ",e.createEl("strong",{text:"empty"})," file is created that matches one of them. Templater will fill the empty file with the specified template.",e.createEl("br"),"The first match from the top is used, so the order of the rules is important.",e.createEl("br"),"Use ",e.createEl("code",{text:".*"})," as a final catch-all, if you need it."),new M.Setting(this.containerEl).setDesc(e);let t=document.createDocumentFragment();t.append("When enabled, Templater will make use of the file regex templates defined below. This option is mutually exclusive with folder templates above, so enabling one will disable the other."),new M.Setting(this.containerEl).setName("Enable file regex templates").setDesc(t).addToggle(n=>{n.setValue(this.plugin.settings.enable_file_templates).onChange(o=>{this.plugin.settings.enable_file_templates=o,o&&(this.plugin.settings.enable_folder_templates=!1),this.plugin.save_settings(),this.display()})}),!!this.plugin.settings.enable_file_templates&&(this.plugin.settings.file_templates.forEach((n,o)=>{new M.Setting(this.containerEl).addText(a=>{a.setPlaceholder("File regex").setValue(n.regex).onChange(A=>{this.plugin.settings.file_templates[o].regex=A,this.plugin.save_settings()}),a.inputEl.addClass("templater_search")}).addSearch(a=>{new Pt(a.inputEl,this.plugin,Te.TemplateFiles),a.setPlaceholder("Template").setValue(n.template).onChange(A=>{this.plugin.settings.file_templates[o].template=A,this.plugin.save_settings()}),a.containerEl.addClass("templater_search")}).addExtraButton(a=>{a.setIcon("up-chevron-glyph").setTooltip("Move up").onClick(()=>{pt(this.plugin.settings.file_templates,o,o-1),this.plugin.save_settings(),this.display()})}).addExtraButton(a=>{a.setIcon("down-chevron-glyph").setTooltip("Move down").onClick(()=>{pt(this.plugin.settings.file_templates,o,o+1),this.plugin.save_settings(),this.display()})}).addExtraButton(a=>{a.setIcon("cross").setTooltip("Delete").onClick(()=>{this.plugin.settings.file_templates.splice(o,1),this.plugin.save_settings(),this.display()})}).infoEl.remove()}),new M.Setting(this.containerEl).addButton(n=>{n.setButtonText("Add new file regex").setTooltip("Add additional file regex").setCta().onClick(()=>{this.plugin.settings.file_templates.push({regex:"",template:""}),this.plugin.save_settings(),this.display()})}))}add_startup_templates_setting(){new M.Setting(this.containerEl).setName("Startup templates").setHeading();let e=document.createDocumentFragment();e.append("Startup templates are templates that will get executed once when Templater starts.",e.createEl("br"),"These templates won't output anything.",e.createEl("br"),"This can be useful to set up templates adding hooks to Obsidian events for example."),new M.Setting(this.containerEl).setDesc(e),this.plugin.settings.startup_templates.forEach((t,n)=>{new M.Setting(this.containerEl).addSearch(s=>{new Pt(s.inputEl,this.plugin,Te.TemplateFiles),s.setPlaceholder("Example: folder1/template_file").setValue(t).onChange(a=>{if(a&&this.plugin.settings.startup_templates.contains(a)){K(new w("This startup template already exist"));return}this.plugin.settings.startup_templates[n]=a,this.plugin.save_settings()}),s.containerEl.addClass("templater_search")}).addExtraButton(s=>{s.setIcon("cross").setTooltip("Delete").onClick(()=>{this.plugin.settings.startup_templates.splice(n,1),this.plugin.save_settings(),this.display()})}).infoEl.remove()}),new M.Setting(this.containerEl).addButton(t=>{t.setButtonText("Add new startup template").setCta().onClick(()=>{this.plugin.settings.startup_templates.push(""),this.plugin.save_settings(),this.display()})})}add_user_script_functions_setting(){new M.Setting(this.containerEl).setName("User script functions").setHeading();let e=document.createDocumentFragment();e.append("All JavaScript files in this folder will be loaded as CommonJS modules, to import custom user functions.",e.createEl("br"),"The folder needs to be accessible from the vault.",e.createEl("br"),"Check the ",e.createEl("a",{href:"https://silentvoid13.github.io/Templater/",text:"documentation"})," for more information."),new M.Setting(this.containerEl).setName("Script files folder location").setDesc(e).addSearch(n=>{new Gt(this.app,n.inputEl),n.setPlaceholder("Example: folder1/folder2").setValue(this.plugin.settings.user_scripts_folder).onChange(o=>{this.plugin.settings.user_scripts_folder=o,this.plugin.save_settings()}),n.containerEl.addClass("templater_search")}),e=document.createDocumentFragment();let t;if(!this.plugin.settings.user_scripts_folder)t="No user scripts folder set";else{let n=ce(()=>Ie(this.app,this.plugin.settings.user_scripts_folder),"User scripts folder doesn't exist");if(!n||n.length===0)t="No user scripts detected";else{let o=0;for(let s of n)s.extension==="js"&&(o++,e.append(e.createEl("li",{text:`tp.user.${s.basename}`})));t=`Detected ${o} User Script(s)`}}new M.Setting(this.containerEl).setName(t).setDesc(e).addExtraButton(n=>{n.setIcon("sync").setTooltip("Refresh").onClick(()=>{this.display()})})}add_user_system_command_functions_setting(){let e=document.createDocumentFragment();if(e.append("Allows you to create user functions linked to system commands.",e.createEl("br"),e.createEl("b",{text:"Warning: "}),"It can be dangerous to execute arbitrary system commands from untrusted sources. Only run system commands that you understand, from trusted sources."),new M.Setting(this.containerEl).setName("User system command functions").setHeading(),new M.Setting(this.containerEl).setName("Enable user system command functions").setDesc(e).addToggle(t=>{t.setValue(this.plugin.settings.enable_system_commands).onChange(n=>{this.plugin.settings.enable_system_commands=n,this.plugin.save_settings(),this.display()})}),this.plugin.settings.enable_system_commands){new M.Setting(this.containerEl).setName("Timeout").setDesc("Maximum timeout in seconds for a system command.").addText(s=>{s.setPlaceholder("Timeout").setValue(this.plugin.settings.command_timeout.toString()).onChange(a=>{let A=Number(a);if(isNaN(A)){K(new w("Timeout must be a number"));return}this.plugin.settings.command_timeout=A,this.plugin.save_settings()})}),e=document.createDocumentFragment(),e.append("Full path to the shell binary to execute the command with.",e.createEl("br"),"This setting is optional and will default to the system's default shell if not specified.",e.createEl("br"),"You can use forward slashes ('/') as path separators on all platforms if in doubt."),new M.Setting(this.containerEl).setName("Shell binary location").setDesc(e).addText(s=>{s.setPlaceholder("Example: /bin/bash, ...").setValue(this.plugin.settings.shell_path).onChange(a=>{this.plugin.settings.shell_path=a,this.plugin.save_settings()})});let t=1;this.plugin.settings.templates_pairs.forEach(s=>{let a=this.containerEl.createEl("div");a.addClass("templater_div");let A=this.containerEl.createEl("h4",{text:"User function n\xB0"+t});A.addClass("templater_title"),new M.Setting(this.containerEl).addExtraButton(d=>{d.setIcon("cross").setTooltip("Delete").onClick(()=>{let f=this.plugin.settings.templates_pairs.indexOf(s);f>-1&&(this.plugin.settings.templates_pairs.splice(f,1),this.plugin.save_settings(),this.display())})}).addText(d=>{let f=d.setPlaceholder("Function name").setValue(s[0]).onChange(b=>{let k=this.plugin.settings.templates_pairs.indexOf(s);k>-1&&(this.plugin.settings.templates_pairs[k][0]=b,this.plugin.save_settings())});return f.inputEl.addClass("templater_template"),f}).addTextArea(d=>{let f=d.setPlaceholder("System command").setValue(s[1]).onChange(b=>{let k=this.plugin.settings.templates_pairs.indexOf(s);k>-1&&(this.plugin.settings.templates_pairs[k][1]=b,this.plugin.save_settings())});return f.inputEl.setAttr("rows",2),f.inputEl.addClass("templater_cmd"),f}).infoEl.remove(),a.appendChild(A),a.appendChild(this.containerEl.lastChild),t+=1});let n=this.containerEl.createEl("div");n.addClass("templater_div2"),new M.Setting(this.containerEl).addButton(s=>{s.setButtonText("Add new user function").setCta().onClick(()=>{this.plugin.settings.templates_pairs.push(["",""]),this.plugin.save_settings(),this.display()})}).infoEl.remove(),n.appendChild(this.containerEl.lastChild)}}add_donating_setting(){let e=new M.Setting(this.containerEl).setName("Donate").setDesc("If you like this Plugin, consider donating to support continued development."),t=document.createElement("a");t.setAttribute("href","https://github.com/sponsors/silentvoid13"),t.addClass("templater_donating");let n=document.createElement("img");n.src="https://img.shields.io/static/v1?label=Sponsor&message=%E2%9D%A4&logo=GitHub&color=%23fe8e86",t.appendChild(n);let o=document.createElement("a");o.setAttribute("href","https://www.paypal.com/donate?hosted_button_id=U2SRGAFYXT32Q"),o.addClass("templater_donating");let s=document.createElement("img");s.src="https://img.shields.io/badge/paypal-silentvoid13-yellow?style=social&logo=paypal",o.appendChild(s),e.settingEl.appendChild(t),e.settingEl.appendChild(o)}};var mr=V(require("obsidian"));var Bt;(function(t){t[t.InsertTemplate=0]="InsertTemplate",t[t.CreateNoteTemplate=1]="CreateNoteTemplate"})(Bt||(Bt={}));var Yr=class extends mr.FuzzySuggestModal{constructor(e){super(e.app);this.plugin=e,this.setPlaceholder("Type name of a template...")}getItems(){if(!this.plugin.settings.templates_folder)return this.app.vault.getMarkdownFiles();let e=ce(()=>Ie(this.plugin.app,this.plugin.settings.templates_folder),`Couldn't retrieve template files from templates folder ${this.plugin.settings.templates_folder}`);return e||[]}getItemText(e){let t=e.path;return e.path.startsWith(this.plugin.settings.templates_folder)&&(0,mr.normalizePath)(this.plugin.settings.templates_folder)!="/"&&(t=e.path.slice(this.plugin.settings.templates_folder.length+1)),t.split(".").slice(0,-1).join(".")}onChooseItem(e){switch(this.open_mode){case 0:this.plugin.templater.append_template_to_active_file(e);break;case 1:this.plugin.templater.create_new_note_from_template(e,this.creation_folder);break}}start(){try{this.open()}catch(e){K(e)}}insert_template(){this.open_mode=0,this.start()}create_new_note_from_template(e){this.creation_folder=e,this.open_mode=1,this.start()}};var ai="Error_MobileUnsupportedTemplate",li='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 51.1328 28.7"><path d="M0 15.14 0 10.15 18.67 1.51 18.67 6.03 4.72 12.33 4.72 12.76 18.67 19.22 18.67 23.74 0 15.14ZM33.6928 1.84C33.6928 1.84 33.9761 2.1467 34.5428 2.76C35.1094 3.38 35.3928 4.56 35.3928 6.3C35.3928 8.0466 34.8195 9.54 33.6728 10.78C32.5261 12.02 31.0995 12.64 29.3928 12.64C27.6862 12.64 26.2661 12.0267 25.1328 10.8C23.9928 9.5733 23.4228 8.0867 23.4228 6.34C23.4228 4.6 23.9995 3.1066 25.1528 1.86C26.2994.62 27.7261 0 29.4328 0C31.1395 0 32.5594.6133 33.6928 1.84M49.8228.67 29.5328 28.38 24.4128 28.38 44.7128.67 49.8228.67M31.0328 8.38C31.0328 8.38 31.1395 8.2467 31.3528 7.98C31.5662 7.7067 31.6728 7.1733 31.6728 6.38C31.6728 5.5867 31.4461 4.92 30.9928 4.38C30.5461 3.84 29.9995 3.57 29.3528 3.57C28.7061 3.57 28.1695 3.84 27.7428 4.38C27.3228 4.92 27.1128 5.5867 27.1128 6.38C27.1128 7.1733 27.3361 7.84 27.7828 8.38C28.2361 8.9267 28.7861 9.2 29.4328 9.2C30.0795 9.2 30.6128 8.9267 31.0328 8.38M49.4328 17.9C49.4328 17.9 49.7161 18.2067 50.2828 18.82C50.8495 19.4333 51.1328 20.6133 51.1328 22.36C51.1328 24.1 50.5594 25.59 49.4128 26.83C48.2595 28.0766 46.8295 28.7 45.1228 28.7C43.4228 28.7 42.0028 28.0833 40.8628 26.85C39.7295 25.6233 39.1628 24.1366 39.1628 22.39C39.1628 20.65 39.7361 19.16 40.8828 17.92C42.0361 16.6733 43.4628 16.05 45.1628 16.05C46.8694 16.05 48.2928 16.6667 49.4328 17.9M46.8528 24.52C46.8528 24.52 46.9595 24.3833 47.1728 24.11C47.3795 23.8367 47.4828 23.3033 47.4828 22.51C47.4828 21.7167 47.2595 21.05 46.8128 20.51C46.3661 19.97 45.8162 19.7 45.1628 19.7C44.5161 19.7 43.9828 19.97 43.5628 20.51C43.1428 21.05 42.9328 21.7167 42.9328 22.51C42.9328 23.3033 43.1561 23.9733 43.6028 24.52C44.0494 25.06 44.5961 25.33 45.2428 25.33C45.8895 25.33 46.4261 25.06 46.8528 24.52Z" fill="currentColor"/></svg>';var ve=V(require("obsidian"));var ze=V(require("obsidian"));var ue=class{constructor(e){this.plugin=e;this.static_functions=new Map;this.dynamic_functions=new Map}getName(){return this.name}async init(){await this.create_static_templates(),this.static_object=Object.fromEntries(this.static_functions)}async generate_object(e){return this.config=e,await this.create_dynamic_templates(),{...this.static_object,...Object.fromEntries(this.dynamic_functions)}}};var Hr=class extends ue{constructor(){super(...arguments);this.name="date"}async create_static_templates(){this.static_functions.set("now",this.generate_now()),this.static_functions.set("tomorrow",this.generate_tomorrow()),this.static_functions.set("weekday",this.generate_weekday()),this.static_functions.set("yesterday",this.generate_yesterday())}async create_dynamic_templates(){}async teardown(){}generate_now(){return(e="YYYY-MM-DD",t,n,o)=>{if(n&&!(0,ze.moment)(n,o).isValid())throw new w("Invalid reference date format, try specifying one with the argument 'reference_format'");let s;return typeof t=="string"?s=ze.moment.duration(t):typeof t=="number"&&(s=ze.moment.duration(t,"days")),(0,ze.moment)(n,o).add(s).format(e)}}generate_tomorrow(){return(e="YYYY-MM-DD")=>(0,ze.moment)().add(1,"days").format(e)}generate_weekday(){return(e="YYYY-MM-DD",t,n,o)=>{if(n&&!(0,ze.moment)(n,o).isValid())throw new w("Invalid reference date format, try specifying one with the argument 'reference_format'");return(0,ze.moment)(n,o).weekday(t).format(e)}}generate_yesterday(){return(e="YYYY-MM-DD")=>(0,ze.moment)().add(-1,"days").format(e)}};var Z=V(require("obsidian"));var pi=10,Gr=class extends ue{constructor(){super(...arguments);this.name="file";this.include_depth=0;this.create_new_depth=0;this.linkpath_regex=new RegExp("^\\[\\[(.*)\\]\\]$")}async create_static_templates(){this.static_functions.set("creation_date",this.generate_creation_date()),this.static_functions.set("create_new",this.generate_create_new()),this.static_functions.set("cursor",this.generate_cursor()),this.static_functions.set("cursor_append",this.generate_cursor_append()),this.static_functions.set("exists",this.generate_exists()),this.static_functions.set("find_tfile",this.generate_find_tfile()),this.static_functions.set("folder",this.generate_folder()),this.static_functions.set("include",this.generate_include()),this.static_functions.set("last_modified_date",this.generate_last_modified_date()),this.static_functions.set("move",this.generate_move()),this.static_functions.set("path",this.generate_path()),this.static_functions.set("rename",this.generate_rename()),this.static_functions.set("selection",this.generate_selection())}async create_dynamic_templates(){this.dynamic_functions.set("content",await this.generate_content()),this.dynamic_functions.set("tags",this.generate_tags()),this.dynamic_functions.set("title",this.generate_title())}async teardown(){}async generate_content(){return await this.plugin.app.vault.read(this.config.target_file)}generate_create_new(){return async(e,t,n=!1,o)=>{if(this.create_new_depth+=1,this.create_new_depth>pi)throw this.create_new_depth=0,new w("Reached create_new depth limit (max = 10)");let s=await this.plugin.templater.create_new_note_from_template(e,o,t,n);return this.create_new_depth-=1,s}}generate_creation_date(){return(e="YYYY-MM-DD HH:mm")=>(0,Z.moment)(this.config.target_file.stat.ctime).format(e)}generate_cursor(){return e=>`<% tp.file.cursor(${e??""}) %>`}generate_cursor_append(){return e=>{let t=this.plugin.app.workspace.activeEditor;if(!t||!t.editor){K(new w("No active editor, can't append to cursor."));return}return t.editor.getDoc().replaceSelection(e),""}}generate_exists(){return async e=>{let t=(0,Z.normalizePath)(e);return await this.plugin.app.vault.exists(t)}}generate_find_tfile(){return e=>{let t=(0,Z.normalizePath)(e);return this.plugin.app.metadataCache.getFirstLinkpathDest(t,"")}}generate_folder(){return(e=!1)=>{let t=this.config.target_file.parent,n;return e?n=t.path:n=t.name,n}}generate_include(){return async e=>{if(this.include_depth+=1,this.include_depth>pi)throw this.include_depth-=1,new w("Reached inclusion depth limit (max = 10)");let t;if(e instanceof Z.TFile)t=await this.plugin.app.vault.read(e);else{let n;if((n=this.linkpath_regex.exec(e))===null)throw this.include_depth-=1,new w("Invalid file format, provide an obsidian link between quotes.");let{path:o,subpath:s}=(0,Z.parseLinktext)(n[1]),a=this.plugin.app.metadataCache.getFirstLinkpathDest(o,"");if(!a)throw this.include_depth-=1,new w(`File ${e} doesn't exist`);if(t=await this.plugin.app.vault.read(a),s){let A=this.plugin.app.metadataCache.getFileCache(a);if(A){let c=(0,Z.resolveSubpath)(A,s);c&&(t=t.slice(c.start.offset,c.end?.offset))}}}try{let n=await this.plugin.templater.parser.parse_commands(t,this.plugin.templater.current_functions_object);return this.include_depth-=1,n}catch(n){throw this.include_depth-=1,n}}}generate_last_modified_date(){return(e="YYYY-MM-DD HH:mm")=>(0,Z.moment)(this.config.target_file.stat.mtime).format(e)}generate_move(){return async(e,t)=>{let n=t||this.config.target_file,o=(0,Z.normalizePath)(`${e}.${n.extension}`),s=o.replace(/\\/g,"/").split("/");if(s.pop(),s.length){let a=s.join("/");this.plugin.app.vault.getAbstractFileByPath(a)||await this.plugin.app.vault.createFolder(a)}return await this.plugin.app.fileManager.renameFile(n,o),""}}generate_path(){return(e=!1)=>{let t="";if(Z.Platform.isMobile){let n=this.plugin.app.vault.adapter.fs.uri,o=this.plugin.app.vault.adapter.basePath;t=`${n}/${o}`}else if(this.plugin.app.vault.adapter instanceof Z.FileSystemAdapter)t=this.plugin.app.vault.adapter.getBasePath();else throw new w("app.vault is not a FileSystemAdapter instance");return e?this.config.target_file.path:`${t}/${this.config.target_file.path}`}}generate_rename(){return async e=>{if(e.match(/[\\/:]+/g))throw new w("File name cannot contain any of these characters: \\ / :");let t=(0,Z.normalizePath)(`${this.config.target_file.parent.path}/${e}.${this.config.target_file.extension}`);return await this.plugin.app.fileManager.renameFile(this.config.target_file,t),""}}generate_selection(){return()=>{let e=this.plugin.app.workspace.activeEditor;if(!e||!e.editor)throw new w("Active editor is null, can't read selection.");return e.editor.getSelection()}}generate_tags(){let e=this.plugin.app.metadataCache.getFileCache(this.config.target_file);return e?(0,Z.getAllTags)(e):null}generate_title(){return this.config.target_file.basename}};var ci=V(require("obsidian"));var Vr=class extends ue{constructor(){super(...arguments);this.name="web"}async create_static_templates(){this.static_functions.set("daily_quote",this.generate_daily_quote()),this.static_functions.set("request",this.generate_request()),this.static_functions.set("random_picture",this.generate_random_picture())}async create_dynamic_templates(){}async teardown(){}async getRequest(e){try{let t=await(0,ci.requestUrl)(e);if(t.status<200&&t.status>=300)throw new w("Error performing GET request");return t}catch{throw new w("Error performing GET request")}}generate_daily_quote(){return async()=>{try{let t=(await this.getRequest("https://raw.githubusercontent.com/Zachatoo/quotes-database/refs/heads/main/quotes.json")).json,n=t[Math.floor(Math.random()*t.length)],{quote:o,author:s}=n;return`> [!quote] ${o}
> \u2014 ${s}`}catch{return new w("Error generating daily quote"),"Error generating daily quote"}}}generate_random_picture(){return async(e,t,n=!1)=>{try{let o=await this.getRequest(`https://templater-unsplash-2.fly.dev/${t?"?q="+t:""}`).then(a=>a.json),s=o.full;if(e&&!n)if(e.includes("x")){let[a,A]=e.split("x");s=s.concat(`&w=${a}&h=${A}`)}else s=s.concat(`&w=${e}`);return n?`![photo by ${o.photog}(${o.photogUrl}) on Unsplash|${e}](${s})`:`![photo by ${o.photog}(${o.photogUrl}) on Unsplash](${s})`}catch{return new w("Error generating random picture"),"Error generating random picture"}}}generate_request(){return async(e,t)=>{try{let o=await(await this.getRequest(e)).json;return t&&o?t.split(".").reduce((s,a)=>{if(s&&s.hasOwnProperty(a))return s[a];throw new Error(`Path ${t} not found in the JSON response`)},o):o}catch(n){throw console.error(n),new w("Error fetching and extracting value")}}}};var Wr=class extends ue{constructor(){super(...arguments);this.name="hooks";this.event_refs=[]}async create_static_templates(){this.static_functions.set("on_all_templates_executed",this.generate_on_all_templates_executed())}async create_dynamic_templates(){}async teardown(){this.event_refs.forEach(e=>{e.e.offref(e)}),this.event_refs=[]}generate_on_all_templates_executed(){return e=>{let t=this.plugin.app.workspace.on("templater:all-templates-executed",async()=>{await sr(1),e()});t&&this.event_refs.push(t)}}};var Kr=class extends ue{constructor(){super(...arguments);this.name="frontmatter"}async create_static_templates(){}async create_dynamic_templates(){let e=this.plugin.app.metadataCache.getFileCache(this.config.target_file);this.dynamic_functions=new Map(Object.entries(e?.frontmatter||{}))}async teardown(){}};var Se=V(require("obsidian"));var Ur=class extends Se.Modal{constructor(e,t,n,o){super(e);this.prompt_text=t;this.default_value=n;this.multi_line=o;this.submitted=!1}onOpen(){this.titleEl.setText(this.prompt_text),this.createForm()}onClose(){this.contentEl.empty(),this.submitted||this.reject(new w("Cancelled prompt"))}createForm(){let e=this.contentEl.createDiv();e.addClass("templater-prompt-div");let t;if(this.multi_line){t=new Se.TextAreaComponent(e);let n=this.contentEl.createDiv();n.addClass("templater-button-div");let o=new Se.ButtonComponent(n);o.buttonEl.addClass("mod-cta"),o.setButtonText("Submit").onClick(s=>{this.resolveAndClose(s)})}else t=new Se.TextComponent(e);this.value=this.default_value??"",t.inputEl.addClass("templater-prompt-input"),t.setPlaceholder("Type text here"),t.setValue(this.value),t.onChange(n=>this.value=n),t.inputEl.focus(),t.inputEl.addEventListener("keydown",n=>this.enterCallback(n))}enterCallback(e){e.isComposing||e.keyCode===229||(this.multi_line?Se.Platform.isDesktop&&e.key==="Enter"&&!e.shiftKey&&this.resolveAndClose(e):e.key==="Enter"&&this.resolveAndClose(e))}resolveAndClose(e){this.submitted=!0,e.preventDefault(),this.resolve(this.value),this.close()}async openAndGetValue(e,t){this.resolve=e,this.reject=t,this.open()}};var Ai=V(require("obsidian")),zr=class extends Ai.FuzzySuggestModal{constructor(e,t,n,o,s){super(e);this.text_items=t;this.items=n;this.submitted=!1;this.setPlaceholder(o),s&&(this.limit=s)}getItems(){return this.items}onClose(){this.submitted||this.reject(new w("Cancelled prompt"))}selectSuggestion(e,t){this.submitted=!0,this.close(),this.onChooseSuggestion(e,t)}getItemText(e){return this.text_items instanceof Function?this.text_items(e):this.text_items[this.items.indexOf(e)]||"Undefined Text Item"}onChooseItem(e){this.resolve(e)}async openAndGetValue(e,t){this.resolve=e,this.reject=t,this.open()}};var Jr=class extends ue{constructor(){super(...arguments);this.name="system"}async create_static_templates(){this.static_functions.set("clipboard",this.generate_clipboard()),this.static_functions.set("prompt",this.generate_prompt()),this.static_functions.set("suggester",this.generate_suggester())}async create_dynamic_templates(){}async teardown(){}generate_clipboard(){return async()=>await navigator.clipboard.readText()}generate_prompt(){return async(e,t,n=!1,o=!1)=>{let s=new Ur(this.plugin.app,e,t,o),a=new Promise((A,c)=>s.openAndGetValue(A,c));try{return await a}catch(A){if(n)throw A;return null}}}generate_suggester(){return async(e,t,n=!1,o="",s)=>{let a=new zr(this.plugin.app,e,t,o,s),A=new Promise((c,d)=>a.openAndGetValue(c,d));try{return await A}catch(c){if(n)throw c;return null}}}};var Xr=class extends ue{constructor(){super(...arguments);this.name="config"}async create_static_templates(){}async create_dynamic_templates(){}async teardown(){}async generate_object(e){return e}};var Qr=class{constructor(e){this.plugin=e;this.modules_array=[];this.modules_array.push(new Hr(this.plugin)),this.modules_array.push(new Gr(this.plugin)),this.modules_array.push(new Vr(this.plugin)),this.modules_array.push(new Kr(this.plugin)),this.modules_array.push(new Wr(this.plugin)),this.modules_array.push(new Jr(this.plugin)),this.modules_array.push(new Xr(this.plugin))}async init(){for(let e of this.modules_array)await e.init()}async teardown(){for(let e of this.modules_array)await e.teardown()}async generate_object(e){let t={};for(let n of this.modules_array)t[n.getName()]=await n.generate_object(e);return t}};var Vt=V(require("obsidian"));var Zr=class{constructor(e){this.plugin=e;if(Vt.Platform.isMobile||!(this.plugin.app.vault.adapter instanceof Vt.FileSystemAdapter))this.cwd="";else{this.cwd=this.plugin.app.vault.adapter.getBasePath();let{promisify:t}=require("util"),{exec:n}=require("child_process");this.exec_promise=t(n)}}async generate_system_functions(e){let t=new Map,n=await this.plugin.templater.functions_generator.generate_object(e,De.INTERNAL);for(let o of this.plugin.settings.templates_pairs){let s=o[0],a=o[1];!s||!a||(Vt.Platform.isMobile?t.set(s,()=>new Promise(A=>A(ai))):(a=await this.plugin.templater.parser.parse_commands(a,n),t.set(s,async A=>{let c={...process.env,...A},d={timeout:this.plugin.settings.command_timeout*1e3,cwd:this.cwd,env:c,...this.plugin.settings.shell_path&&{shell:this.plugin.settings.shell_path}};try{let{stdout:f}=await this.exec_promise(a,d);return f.trimRight()}catch(f){throw new w(`Error with User Template ${s}`,f)}})))}return t}async generate_object(e){let t=await this.generate_system_functions(e);return Object.fromEntries(t)}};var en=class{constructor(e){this.plugin=e}async generate_user_script_functions(){let e=new Map,t=ce(()=>Ie(this.plugin.app,this.plugin.settings.user_scripts_folder),`Couldn't find user script folder "${this.plugin.settings.user_scripts_folder}"`);if(!t)return new Map;for(let n of t)n.extension.toLowerCase()==="js"&&await this.load_user_script_function(n,e);return e}async load_user_script_function(e,t){let n=c=>window.require&&window.require(c),o={},s={exports:o},a=await this.plugin.app.vault.read(e);try{window.eval("(function anonymous(require, module, exports){"+a+`
})`)(n,s,o)}catch(c){throw new w(`Failed to load user script at "${e.path}".`,c.message)}let A=o.default||s.exports;if(!A)throw new w(`Failed to load user script at "${e.path}". No exports detected.`);if(!(A instanceof Function))throw new w(`Failed to load user script at "${e.path}". Default export is not a function.`);t.set(`${e.basename}`,A)}async generate_object(){let e=await this.generate_user_script_functions();return Object.fromEntries(e)}};var tn=class{constructor(e){this.plugin=e;this.user_system_functions=new Zr(e),this.user_script_functions=new en(e)}async generate_object(e){let t={},n={};return this.plugin.settings.enable_system_commands&&(t=await this.user_system_functions.generate_object(e)),this.plugin.settings.user_scripts_folder&&(n=await this.user_script_functions.generate_object()),{...t,...n}}};var Bo=V(require("obsidian")),De;(function(t){t[t.INTERNAL=0]="INTERNAL",t[t.USER_INTERNAL=1]="USER_INTERNAL"})(De||(De={}));var rn=class{constructor(e){this.plugin=e;this.internal_functions=new Qr(this.plugin),this.user_functions=new tn(this.plugin)}async init(){await this.internal_functions.init()}async teardown(){await this.internal_functions.teardown()}additional_functions(){return{app:this.plugin.app,obsidian:Bo}}async generate_object(e,t=1){let n={},o=this.additional_functions(),s=await this.internal_functions.generate_object(e),a={};switch(Object.assign(n,o),t){case 0:Object.assign(n,s);break;case 1:a=await this.user_functions.generate_object(e),Object.assign(n,{...s,user:a});break}return n}};var Lo={},v,Fe=new Array(32).fill(void 0);Fe.push(void 0,null,!0,!1);function de(r){return Fe[r]}var Wt=Fe.length;function qo(r){r<36||(Fe[r]=Wt,Wt=r)}function nn(r){let e=de(r);return qo(r),e}var ui=new TextDecoder("utf-8",{ignoreBOM:!0,fatal:!0});ui.decode();var fr=new Uint8Array;function dr(){return fr.byteLength===0&&(fr=new Uint8Array(v.memory.buffer)),fr}function rt(r,e){return ui.decode(dr().subarray(r,r+e))}function nt(r){Wt===Fe.length&&Fe.push(Fe.length+1);let e=Wt;return Wt=Fe[e],Fe[e]=r,e}var Me=0,gr=new TextEncoder("utf-8"),Oo=typeof gr.encodeInto=="function"?function(r,e){return gr.encodeInto(r,e)}:function(r,e){let t=gr.encode(r);return e.set(t),{read:r.length,written:t.length}};function Je(r,e,t){if(t===void 0){let A=gr.encode(r),c=e(A.length);return dr().subarray(c,c+A.length).set(A),Me=A.length,c}let n=r.length,o=e(n),s=dr(),a=0;for(;a<n;a++){let A=r.charCodeAt(a);if(A>127)break;s[o+a]=A}if(a!==n){a!==0&&(r=r.slice(a)),o=t(o,n,n=a+r.length*3);let A=dr().subarray(o+a,o+n);a+=Oo(r,A).written}return Me=a,o}function Co(r){return r==null}var hr=new Int32Array;function ge(){return hr.byteLength===0&&(hr=new Int32Array(v.memory.buffer)),hr}function on(r){let e=typeof r;if(e=="number"||e=="boolean"||r==null)return`${r}`;if(e=="string")return`"${r}"`;if(e=="symbol"){let o=r.description;return o==null?"Symbol":`Symbol(${o})`}if(e=="function"){let o=r.name;return typeof o=="string"&&o.length>0?`Function(${o})`:"Function"}if(Array.isArray(r)){let o=r.length,s="[";o>0&&(s+=on(r[0]));for(let a=1;a<o;a++)s+=", "+on(r[a]);return s+="]",s}let t=/\[object ([^\]]+)\]/.exec(toString.call(r)),n;if(t.length>1)n=t[1];else return toString.call(r);if(n=="Object")try{return"Object("+JSON.stringify(r)+")"}catch{return"Object"}return r instanceof Error?`${r.name}: ${r.message}
${r.stack}`:n}function Io(r,e){if(!(r instanceof e))throw new Error(`expected instance of ${e.name}`);return r.ptr}var jr=32;function So(r){if(jr==1)throw new Error("out of js stack");return Fe[--jr]=r,jr}function sn(r,e){try{return r.apply(this,e)}catch(t){v.__wbindgen_exn_store(nt(t))}}var jt=class{static __wrap(e){let t=Object.create(jt.prototype);return t.ptr=e,t}__destroy_into_raw(){let e=this.ptr;return this.ptr=0,e}free(){let e=this.__destroy_into_raw();v.__wbg_parserconfig_free(e)}get interpolate(){let e=v.__wbg_get_parserconfig_interpolate(this.ptr);return String.fromCodePoint(e)}set interpolate(e){v.__wbg_set_parserconfig_interpolate(this.ptr,e.codePointAt(0))}get execution(){let e=v.__wbg_get_parserconfig_execution(this.ptr);return String.fromCodePoint(e)}set execution(e){v.__wbg_set_parserconfig_execution(this.ptr,e.codePointAt(0))}get single_whitespace(){let e=v.__wbg_get_parserconfig_single_whitespace(this.ptr);return String.fromCodePoint(e)}set single_whitespace(e){v.__wbg_set_parserconfig_single_whitespace(this.ptr,e.codePointAt(0))}get multiple_whitespace(){let e=v.__wbg_get_parserconfig_multiple_whitespace(this.ptr);return String.fromCodePoint(e)}set multiple_whitespace(e){v.__wbg_set_parserconfig_multiple_whitespace(this.ptr,e.codePointAt(0))}constructor(e,t,n,o,s,a,A){let c=Je(e,v.__wbindgen_malloc,v.__wbindgen_realloc),d=Me,f=Je(t,v.__wbindgen_malloc,v.__wbindgen_realloc),b=Me,k=Je(A,v.__wbindgen_malloc,v.__wbindgen_realloc),x=Me,O=v.parserconfig_new(c,d,f,b,n.codePointAt(0),o.codePointAt(0),s.codePointAt(0),a.codePointAt(0),k,x);return jt.__wrap(O)}get opening_tag(){try{let n=v.__wbindgen_add_to_stack_pointer(-16);v.parserconfig_opening_tag(n,this.ptr);var e=ge()[n/4+0],t=ge()[n/4+1];return rt(e,t)}finally{v.__wbindgen_add_to_stack_pointer(16),v.__wbindgen_free(e,t)}}set opening_tag(e){let t=Je(e,v.__wbindgen_malloc,v.__wbindgen_realloc),n=Me;v.parserconfig_set_opening_tag(this.ptr,t,n)}get closing_tag(){try{let n=v.__wbindgen_add_to_stack_pointer(-16);v.parserconfig_closing_tag(n,this.ptr);var e=ge()[n/4+0],t=ge()[n/4+1];return rt(e,t)}finally{v.__wbindgen_add_to_stack_pointer(16),v.__wbindgen_free(e,t)}}set closing_tag(e){let t=Je(e,v.__wbindgen_malloc,v.__wbindgen_realloc),n=Me;v.parserconfig_set_closing_tag(this.ptr,t,n)}get global_var(){try{let n=v.__wbindgen_add_to_stack_pointer(-16);v.parserconfig_global_var(n,this.ptr);var e=ge()[n/4+0],t=ge()[n/4+1];return rt(e,t)}finally{v.__wbindgen_add_to_stack_pointer(16),v.__wbindgen_free(e,t)}}set global_var(e){let t=Je(e,v.__wbindgen_malloc,v.__wbindgen_realloc),n=Me;v.parserconfig_set_global_var(this.ptr,t,n)}},qt=class{static __wrap(e){let t=Object.create(qt.prototype);return t.ptr=e,t}__destroy_into_raw(){let e=this.ptr;return this.ptr=0,e}free(){let e=this.__destroy_into_raw();v.__wbg_renderer_free(e)}constructor(e){Io(e,jt);var t=e.ptr;e.ptr=0;let n=v.renderer_new(t);return qt.__wrap(n)}render_content(e,t){try{let a=v.__wbindgen_add_to_stack_pointer(-16),A=Je(e,v.__wbindgen_malloc,v.__wbindgen_realloc),c=Me;v.renderer_render_content(a,this.ptr,A,c,So(t));var n=ge()[a/4+0],o=ge()[a/4+1],s=ge()[a/4+2];if(s)throw nn(o);return nn(n)}finally{v.__wbindgen_add_to_stack_pointer(16),Fe[jr++]=void 0}}};async function Do(r,e){if(typeof Response=="function"&&r instanceof Response){if(typeof WebAssembly.instantiateStreaming=="function")try{return await WebAssembly.instantiateStreaming(r,e)}catch(n){if(r.headers.get("Content-Type")!="application/wasm")console.warn("`WebAssembly.instantiateStreaming` failed because your server does not serve wasm with `application/wasm` MIME type. Falling back to `WebAssembly.instantiate` which is slower. Original error:\n",n);else throw n}let t=await r.arrayBuffer();return await WebAssembly.instantiate(t,e)}else{let t=await WebAssembly.instantiate(r,e);return t instanceof WebAssembly.Instance?{instance:t,module:r}:t}}function $o(){let r={};return r.wbg={},r.wbg.__wbindgen_object_drop_ref=function(e){nn(e)},r.wbg.__wbindgen_string_new=function(e,t){let n=rt(e,t);return nt(n)},r.wbg.__wbindgen_string_get=function(e,t){let n=de(t),o=typeof n=="string"?n:void 0;var s=Co(o)?0:Je(o,v.__wbindgen_malloc,v.__wbindgen_realloc),a=Me;ge()[e/4+1]=a,ge()[e/4+0]=s},r.wbg.__wbg_call_97ae9d8645dc388b=function(){return sn(function(e,t){let n=de(e).call(de(t));return nt(n)},arguments)},r.wbg.__wbg_new_8d2af00bc1e329ee=function(e,t){let n=new Error(rt(e,t));return nt(n)},r.wbg.__wbg_message_fe2af63ccc8985bc=function(e){let t=de(e).message;return nt(t)},r.wbg.__wbg_newwithargs_8fe23e3842840c8e=function(e,t,n,o){let s=new Function(rt(e,t),rt(n,o));return nt(s)},r.wbg.__wbg_call_168da88779e35f61=function(){return sn(function(e,t,n){let o=de(e).call(de(t),de(n));return nt(o)},arguments)},r.wbg.__wbg_call_3999bee59e9f7719=function(){return sn(function(e,t,n,o){let s=de(e).call(de(t),de(n),de(o));return nt(s)},arguments)},r.wbg.__wbindgen_debug_string=function(e,t){let n=on(de(t)),o=Je(n,v.__wbindgen_malloc,v.__wbindgen_realloc),s=Me;ge()[e/4+1]=s,ge()[e/4+0]=o},r.wbg.__wbindgen_throw=function(e,t){throw new Error(rt(e,t))},r}function No(r,e){}function Ro(r,e){return v=r.exports,mi.__wbindgen_wasm_module=e,hr=new Int32Array,fr=new Uint8Array,v}async function mi(r){typeof r=="undefined"&&(r=new URL("rusty_engine_bg.wasm",Lo.url));let e=$o();(typeof r=="string"||typeof Request=="function"&&r instanceof Request||typeof URL=="function"&&r instanceof URL)&&(r=fetch(r)),No(e);let{instance:t,module:n}=await Do(await r,e);return Ro(t,n)}var fi=mi;var di=Mn("AGFzbQEAAAABvwEaYAJ/fwBgAn9/AX9gAX8Bf2ADf39/AX9gA39/fwBgAX8AYAV/f39/fwBgBH9/f38AYAR/f39/AX9gAABgBX9/f39/AX9gAX8BfmAAAX9gBn9/f39/fwBgB39/f39/f38AYAV/f35/fwBgBX9/fX9/AGAFf398f38AYAR/fn9/AGAFf35/f38AYAR/fX9/AGAEf3x/fwBgBn9/f39/fwF/YAd/f39/f39/AX9gCn9/f39/f39/f38Bf2ACfn8BfwLkAgsDd2JnGl9fd2JpbmRnZW5fb2JqZWN0X2Ryb3BfcmVmAAUDd2JnFV9fd2JpbmRnZW5fc3RyaW5nX25ldwABA3diZxVfX3diaW5kZ2VuX3N0cmluZ19nZXQAAAN3YmcbX193YmdfY2FsbF85N2FlOWQ4NjQ1ZGMzODhiAAEDd2JnGl9fd2JnX25ld184ZDJhZjAwYmMxZTMyOWVlAAEDd2JnHl9fd2JnX21lc3NhZ2VfZmUyYWY2M2NjYzg5ODViYwACA3diZyJfX3diZ19uZXd3aXRoYXJnc184ZmUyM2UzODQyODQwYzhlAAgDd2JnG19fd2JnX2NhbGxfMTY4ZGE4ODc3OWUzNWY2MQADA3diZxtfX3diZ19jYWxsXzM5OTliZWU1OWU5Zjc3MTkACAN3YmcXX193YmluZGdlbl9kZWJ1Z19zdHJpbmcAAAN3YmcQX193YmluZGdlbl90aHJvdwAAA7kBtwECBwAGAgYEBAcBBQMKCAAEBgYAAwcCAAEADgETAQQXAQICAQAAAwcZAQAFAQwABgACAgAAAgAEBAAGAQAAAAAEBw0CAQUEBQYCDBgAAQAAAAQBAQEAAQABBAQEBgMDBwMJAwQIAAAABQkAAgEAAAAABwAAAgICAgAFBQMEFgoGEQ8QAAUHAwIBAgABBQEBCAACAQEBBQEAAgECAgACAQEBAgAJCQICAgIAAAAAAwMDAQECAgsLCwUEBQFwATs7BQMBABEGCQF/AUGAgMAACwfcBRkGbWVtb3J5AgAXX193YmdfcGFyc2VyY29uZmlnX2ZyZWUAUSJfX3diZ19nZXRfcGFyc2VyY29uZmlnX2ludGVycG9sYXRlAH4iX193Ymdfc2V0X3BhcnNlcmNvbmZpZ19pbnRlcnBvbGF0ZQB3IF9fd2JnX2dldF9wYXJzZXJjb25maWdfZXhlY3V0aW9uAH8gX193Ymdfc2V0X3BhcnNlcmNvbmZpZ19leGVjdXRpb24AeChfX3diZ19nZXRfcGFyc2VyY29uZmlnX3NpbmdsZV93aGl0ZXNwYWNlAIABKF9fd2JnX3NldF9wYXJzZXJjb25maWdfc2luZ2xlX3doaXRlc3BhY2UAeSpfX3diZ19nZXRfcGFyc2VyY29uZmlnX211bHRpcGxlX3doaXRlc3BhY2UAgQEqX193Ymdfc2V0X3BhcnNlcmNvbmZpZ19tdWx0aXBsZV93aGl0ZXNwYWNlAHoQcGFyc2VyY29uZmlnX25ldwBVGHBhcnNlcmNvbmZpZ19vcGVuaW5nX3RhZwBGHHBhcnNlcmNvbmZpZ19zZXRfb3BlbmluZ190YWcAYxhwYXJzZXJjb25maWdfY2xvc2luZ190YWcARxxwYXJzZXJjb25maWdfc2V0X2Nsb3NpbmdfdGFnAGQXcGFyc2VyY29uZmlnX2dsb2JhbF92YXIASBtwYXJzZXJjb25maWdfc2V0X2dsb2JhbF92YXIAZRNfX3diZ19yZW5kZXJlcl9mcmVlAE8McmVuZGVyZXJfbmV3ACAXcmVuZGVyZXJfcmVuZGVyX2NvbnRlbnQAORFfX3diaW5kZ2VuX21hbGxvYwB1El9fd2JpbmRnZW5fcmVhbGxvYwCFAR9fX3diaW5kZ2VuX2FkZF90b19zdGFja19wb2ludGVyAKsBD19fd2JpbmRnZW5fZnJlZQCaARRfX3diaW5kZ2VuX2V4bl9zdG9yZQCfAQllAQBBAQs6mAGdAaoBPzzBAZUBlgFOkgGOAWotYsEBwQFnKl3BAXaIAUyJAYgBhwGQAY8BiQGJAYwBigGLAZgBX8EBaKABXo4BvwG+AYQBOElwoQHBAWioAWCjAVclqQGcAcEBwAEK2dYCtwG8IAIPfwF+IwBBEGsiCyQAAkACQCAAQfUBTwRAQYCAfEEIQQgQlwFBFEEIEJcBakEQQQgQlwFqa0F3cUF9aiICQQBBEEEIEJcBQQJ0ayIBIAEgAksbIABNDQIgAEEEakEIEJcBIQRBrK7AACgCAEUNAUEAIARrIQMCQAJAAn9BACAEQYACSQ0AGkEfIARB////B0sNABogBEEGIARBCHZnIgBrdkEBcSAAQQF0a0E+agsiBkECdEG4sMAAaigCACIABEAgBCAGEJMBdCEHQQAhAQNAAkAgABCvASICIARJDQAgAiAEayICIANPDQAgACEBIAIiAw0AQQAhAwwDCyAAQRRqKAIAIgIgBSACIAAgB0EddkEEcWpBEGooAgAiAEcbIAUgAhshBSAHQQF0IQcgAA0ACyAFBEAgBSEADAILIAENAgtBACEBQQEgBnQQmwFBrK7AACgCAHEiAEUNAyAAEKQBaEECdEG4sMAAaigCACIARQ0DCwNAIAAgASAAEK8BIgEgBE8gASAEayIFIANJcSICGyEBIAUgAyACGyEDIAAQkQEiAA0ACyABRQ0CC0G4scAAKAIAIgAgBE9BACADIAAgBGtPGw0BIAEiACAEELoBIQYgABA1AkAgA0EQQQgQlwFPBEAgACAEEKYBIAYgAxCUASADQYACTwRAIAYgAxA0DAILIANBA3YiAUEDdEGwrsAAaiEFAn9BqK7AACgCACICQQEgAXQiAXEEQCAFKAIIDAELQaiuwAAgASACcjYCACAFCyEBIAUgBjYCCCABIAY2AgwgBiAFNgIMIAYgATYCCAwBCyAAIAMgBGoQjQELIAAQvAEiA0UNAQwCC0EQIABBBGpBEEEIEJcBQXtqIABLG0EIEJcBIQQCQAJAAkACfwJAAkBBqK7AACgCACIBIARBA3YiAHYiAkEDcUUEQCAEQbixwAAoAgBNDQcgAg0BQayuwAAoAgAiAEUNByAAEKQBaEECdEG4sMAAaigCACIBEK8BIARrIQMgARCRASIABEADQCAAEK8BIARrIgIgAyACIANJIgIbIQMgACABIAIbIQEgABCRASIADQALCyABIgAgBBC6ASEFIAAQNSADQRBBCBCXAUkNBSAAIAQQpgEgBSADEJQBQbixwAAoAgAiAUUNBCABQQN2IgFBA3RBsK7AAGohB0HAscAAKAIAIQZBqK7AACgCACICQQEgAXQiAXFFDQIgBygCCAwDCwJAIAJBf3NBAXEgAGoiA0EDdCIAQbiuwABqKAIAIgVBCGooAgAiAiAAQbCuwABqIgBHBEAgAiAANgIMIAAgAjYCCAwBC0GorsAAIAFBfiADd3E2AgALIAUgA0EDdBCNASAFELwBIQMMBwsCQEEBIABBH3EiAHQQmwEgAiAAdHEQpAFoIgJBA3QiAEG4rsAAaigCACIDQQhqKAIAIgEgAEGwrsAAaiIARwRAIAEgADYCDCAAIAE2AggMAQtBqK7AAEGorsAAKAIAQX4gAndxNgIACyADIAQQpgEgAyAEELoBIgUgAkEDdCAEayICEJQBQbixwAAoAgAiAARAIABBA3YiAEEDdEGwrsAAaiEHQcCxwAAoAgAhBgJ/QaiuwAAoAgAiAUEBIAB0IgBxBEAgBygCCAwBC0GorsAAIAAgAXI2AgAgBwshACAHIAY2AgggACAGNgIMIAYgBzYCDCAGIAA2AggLQcCxwAAgBTYCAEG4scAAIAI2AgAgAxC8ASEDDAYLQaiuwAAgASACcjYCACAHCyEBIAcgBjYCCCABIAY2AgwgBiAHNgIMIAYgATYCCAtBwLHAACAFNgIAQbixwAAgAzYCAAwBCyAAIAMgBGoQjQELIAAQvAEiAw0BCwJAAkACQAJAAkACQAJAAkBBuLHAACgCACIAIARJBEBBvLHAACgCACIAIARLDQIgC0EIQQgQlwEgBGpBFEEIEJcBakEQQQgQlwFqQYCABBCXARBxIAsoAgAiCA0BQQAhAwwJC0HAscAAKAIAIQIgACAEayIBQRBBCBCXAUkEQEHAscAAQQA2AgBBuLHAACgCACEAQbixwABBADYCACACIAAQjQEgAhC8ASEDDAkLIAIgBBC6ASEAQbixwAAgATYCAEHAscAAIAA2AgAgACABEJQBIAIgBBCmASACELwBIQMMCAsgCygCCCEMQcixwAAgCygCBCIKQcixwAAoAgBqIgE2AgBBzLHAAEHMscAAKAIAIgAgASAAIAFLGzYCAAJAAkBBxLHAACgCAARAQdCxwAAhAANAIAAQpwEgCEYNAiAAKAIIIgANAAsMAgtB5LHAACgCACIARSAIIABJcg0DDAcLIAAQsQENACAAELIBIAxHDQAgACIBKAIAIgVBxLHAACgCACICTQR/IAUgASgCBGogAksFQQALDQMLQeSxwABB5LHAACgCACIAIAggCCAASxs2AgAgCCAKaiEBQdCxwAAhAAJAAkADQCABIAAoAgBHBEAgACgCCCIADQEMAgsLIAAQsQENACAAELIBIAxGDQELQcSxwAAoAgAhCUHQscAAIQACQANAIAAoAgAgCU0EQCAAEKcBIAlLDQILIAAoAggiAA0AC0EAIQALIAkgABCnASIGQRRBCBCXASIPa0FpaiIBELwBIgBBCBCXASAAayABaiIAIABBEEEIEJcBIAlqSRsiDRC8ASEOIA0gDxC6ASEAQQhBCBCXASEDQRRBCBCXASEFQRBBCBCXASECQcSxwAAgCCAIELwBIgFBCBCXASABayIBELoBIgc2AgBBvLHAACAKQQhqIAIgAyAFamogAWprIgM2AgAgByADQQFyNgIEQQhBCBCXASEFQRRBCBCXASECQRBBCBCXASEBIAcgAxC6ASABIAIgBUEIa2pqNgIEQeCxwABBgICAATYCACANIA8QpgFB0LHAACkCACEQIA5BCGpB2LHAACkCADcCACAOIBA3AgBB3LHAACAMNgIAQdSxwAAgCjYCAEHQscAAIAg2AgBB2LHAACAONgIAA0AgAEEEELoBIQEgAEEHNgIEIAYgASIAQQRqSw0ACyAJIA1GDQcgCSANIAlrIgAgCSAAELoBEIYBIABBgAJPBEAgCSAAEDQMCAsgAEEDdiIAQQN0QbCuwABqIQICf0GorsAAKAIAIgFBASAAdCIAcQRAIAIoAggMAQtBqK7AACAAIAFyNgIAIAILIQAgAiAJNgIIIAAgCTYCDCAJIAI2AgwgCSAANgIIDAcLIAAoAgAhAyAAIAg2AgAgACAAKAIEIApqNgIEIAgQvAEiBUEIEJcBIQIgAxC8ASIBQQgQlwEhACAIIAIgBWtqIgYgBBC6ASEHIAYgBBCmASADIAAgAWtqIgAgBCAGamshBCAAQcSxwAAoAgBHBEBBwLHAACgCACAARg0EIAAoAgRBA3FBAUcNBQJAIAAQrwEiBUGAAk8EQCAAEDUMAQsgAEEMaigCACICIABBCGooAgAiAUcEQCABIAI2AgwgAiABNgIIDAELQaiuwABBqK7AACgCAEF+IAVBA3Z3cTYCAAsgBCAFaiEEIAAgBRC6ASEADAULQcSxwAAgBzYCAEG8scAAQbyxwAAoAgAgBGoiADYCACAHIABBAXI2AgQgBhC8ASEDDAcLQbyxwAAgACAEayIBNgIAQcSxwABBxLHAACgCACICIAQQugEiADYCACAAIAFBAXI2AgQgAiAEEKYBIAIQvAEhAwwGC0HkscAAIAg2AgAMAwsgACAAKAIEIApqNgIEQcSxwAAoAgBBvLHAACgCACAKahBWDAMLQcCxwAAgBzYCAEG4scAAQbixwAAoAgAgBGoiADYCACAHIAAQlAEgBhC8ASEDDAMLIAcgBCAAEIYBIARBgAJPBEAgByAEEDQgBhC8ASEDDAMLIARBA3YiAEEDdEGwrsAAaiECAn9BqK7AACgCACIBQQEgAHQiAHEEQCACKAIIDAELQaiuwAAgACABcjYCACACCyEAIAIgBzYCCCAAIAc2AgwgByACNgIMIAcgADYCCCAGELwBIQMMAgtB6LHAAEH/HzYCAEHcscAAIAw2AgBB1LHAACAKNgIAQdCxwAAgCDYCAEG8rsAAQbCuwAA2AgBBxK7AAEG4rsAANgIAQbiuwABBsK7AADYCAEHMrsAAQcCuwAA2AgBBwK7AAEG4rsAANgIAQdSuwABByK7AADYCAEHIrsAAQcCuwAA2AgBB3K7AAEHQrsAANgIAQdCuwABByK7AADYCAEHkrsAAQdiuwAA2AgBB2K7AAEHQrsAANgIAQeyuwABB4K7AADYCAEHgrsAAQdiuwAA2AgBB9K7AAEHorsAANgIAQeiuwABB4K7AADYCAEH8rsAAQfCuwAA2AgBB8K7AAEHorsAANgIAQfiuwABB8K7AADYCAEGEr8AAQfiuwAA2AgBBgK/AAEH4rsAANgIAQYyvwABBgK/AADYCAEGIr8AAQYCvwAA2AgBBlK/AAEGIr8AANgIAQZCvwABBiK/AADYCAEGcr8AAQZCvwAA2AgBBmK/AAEGQr8AANgIAQaSvwABBmK/AADYCAEGgr8AAQZivwAA2AgBBrK/AAEGgr8AANgIAQaivwABBoK/AADYCAEG0r8AAQaivwAA2AgBBsK/AAEGor8AANgIAQbyvwABBsK/AADYCAEHEr8AAQbivwAA2AgBBuK/AAEGwr8AANgIAQcyvwABBwK/AADYCAEHAr8AAQbivwAA2AgBB1K/AAEHIr8AANgIAQcivwABBwK/AADYCAEHcr8AAQdCvwAA2AgBB0K/AAEHIr8AANgIAQeSvwABB2K/AADYCAEHYr8AAQdCvwAA2AgBB7K/AAEHgr8AANgIAQeCvwABB2K/AADYCAEH0r8AAQeivwAA2AgBB6K/AAEHgr8AANgIAQfyvwABB8K/AADYCAEHwr8AAQeivwAA2AgBBhLDAAEH4r8AANgIAQfivwABB8K/AADYCAEGMsMAAQYCwwAA2AgBBgLDAAEH4r8AANgIAQZSwwABBiLDAADYCAEGIsMAAQYCwwAA2AgBBnLDAAEGQsMAANgIAQZCwwABBiLDAADYCAEGksMAAQZiwwAA2AgBBmLDAAEGQsMAANgIAQaywwABBoLDAADYCAEGgsMAAQZiwwAA2AgBBtLDAAEGosMAANgIAQaiwwABBoLDAADYCAEGwsMAAQaiwwAA2AgBBCEEIEJcBIQVBFEEIEJcBIQJBEEEIEJcBIQFBxLHAACAIIAgQvAEiAEEIEJcBIABrIgAQugEiAzYCAEG8scAAIApBCGogASACIAVqaiAAamsiBTYCACADIAVBAXI2AgRBCEEIEJcBIQJBFEEIEJcBIQFBEEEIEJcBIQAgAyAFELoBIAAgASACQQhramo2AgRB4LHAAEGAgIABNgIAC0EAIQNBvLHAACgCACIAIARNDQBBvLHAACAAIARrIgE2AgBBxLHAAEHEscAAKAIAIgIgBBC6ASIANgIAIAAgAUEBcjYCBCACIAQQpgEgAhC8ASEDCyALQRBqJAAgAwvgDwINfwp+IwBBMGsiCSQAAkAgASgCDCIKIAJqIgIgCkkEQBBrIAkoAgwhAiAJKAIIIQQMAQsCQAJAAkACfwJAIAIgASgCACIIIAhBAWoiB0EDdkEHbCAIQQhJGyILQQF2SwRAIAIgC0EBaiIEIAIgBEsbIgJBCEkNASACIAJB/////wFxRgRAQX8gAkEDdEEHbkF/amd2QQFqDAMLEGsgCSgCLCECIAkoAighBAwGCyABQQRqKAIAIQVBACECA0ACQAJAIARBAXFFBEAgAiAHTw0BDAILIAJBB2oiBCACSQ0AIAQiAiAHSQ0BCwJAAkAgB0EITwRAIAUgB2ogBSkAADcAAAwBCyAFQQhqIAUgBxAaIAdFDQELIANBCGopAwAiGELt3pHzlszct+QAhSIRIAMpAwAiFkL1ys2D16zbt/MAhXwiF0IgiSEZIBFCDYkgF4UiF0IRiSEaIBZC4eSV89bs2bzsAIUhFkEAIQIDQAJAIAUgAiIDaiIMLQAAQYABRw0AIAUgA0EDdGtBeGohDyAFIANBf3NBA3RqIQcCQANAIAggGCAPNQIAQoCAgICAgICABIQiEYVC88rRy6eM2bL0AIUiEkIQiSASIBZ8IhKFIhMgGXwiFCARhSASIBd8IhEgGoUiEnwiFSASQg2JhSISIBNCFYkgFIUiEyARQiCJQv8BhXwiEXwiFCASQhGJhSISQg2JIBIgE0IQiSARhSIRIBVCIIl8IhN8IhKFIhVCEYkgFSARQhWJIBOFIhEgFEIgiXwiE3wiFIUiFUINiSAVIBFCEIkgE4UiESASQiCJfCISfIUiEyARQhWJIBKFIhEgFEIgiXwiEnwiFCARQhCJIBKFQhWJhSATQhGJhSAUQiCIhaciDXEiBiEEIAUgBmopAABCgIGChIiQoMCAf4MiEVAEQEEIIQIgBiEEA0AgAiAEaiEEIAJBCGohAiAFIAQgCHEiBGopAABCgIGChIiQoMCAf4MiEVANAAsLIAUgEXqnQQN2IARqIAhxIgRqLAAAQX9KBEAgBSkDAEKAgYKEiJCgwIB/g3qnQQN2IQQLIAQgBmsgAyAGa3MgCHFBCE8EQCAFIARBf3NBA3RqIQIgBCAFaiIGLQAAIAYgDUEZdiIGOgAAIARBeGogCHEgBWpBCGogBjoAAEH/AUYNAiAHLQAFIQQgBy0ABCEGIAcgAi8ABDsABCACLQAHIQ0gAi0ABiEOIAIgBy8ABjsABiAHKAAAIRAgByACKAAANgAAIAIgEDYAACACIAY6AAQgByAOOgAGIAIgBDoABSAHIA06AAcMAQsLIAwgDUEZdiICOgAAIANBeGogCHEgBWpBCGogAjoAAAwBCyAMQf8BOgAAIANBeGogCHEgBWpBCGpB/wE6AAAgAiAHKQAANwAACyADQQFqIQIgAyAIRw0ACwsgASALIAprNgIIDAULIAIgBWoiBCAEKQMAIhFCB4hCf4VCgYKEiJCgwIABgyARQv/+/fv379+//wCEfDcDAEEBIQQgAkEBaiECDAALAAtBBEEIIAJBBEkbCyICQf////8BcSACRgRAIAJBA3QiBCACQQhqIgtqIgYgBE8NAQsQayAJKAIUIQIgCSgCECEEDAMLAkACQCAGQQBOBEBBCCEFAkAgBkUNACAGQQgQngEiBQ0AIAZBCBCzAQALIAQgBWogCxBFIQYgAkF/aiIFIAJBA3ZBB2wgBUEISRsgCmshCyABQQRqIgIoAgAhCiAHDQEgASALNgIIIAEgBTYCACACIAY2AgAMAgsQayAJKAIcIQIgCSgCGCEEDAQLIANBCGopAwAiGELt3pHzlszct+QAhSIRIAMpAwAiFkL1ys2D16zbt/MAhXwiF0IgiSEZIBFCDYkgF4UiF0IRiSEaIBZC4eSV89bs2bzsAIUhFkEAIQMDQCADIApqLAAAQQBOBEAgBiAFIBggCiADQQN0a0F4ajUCAEKAgICAgICAgASEIhGFQvPK0cunjNmy9ACFIhJCEIkgEiAWfCIShSITIBl8IhQgEYUgEiAXfCIRIBqFIhJ8IhUgEkINiYUiEiATQhWJIBSFIhMgEUIgiUL/AYV8IhF8IhQgEkIRiYUiEkINiSASIBNCEIkgEYUiESAVQiCJfCITfCIShSIVQhGJIBUgEUIViSAThSIRIBRCIIl8IhN8IhSFIhVCDYkgFSARQhCJIBOFIhEgEkIgiXwiEnyFIhMgEUIViSAShSIRIBRCIIl8IhJ8IhQgEUIQiSAShUIViYUgE0IRiYUgFEIgiIWnIgxxIgRqKQAAQoCBgoSIkKDAgH+DIhFQBEBBCCECA0AgAiAEaiEEIAJBCGohAiAGIAQgBXEiBGopAABCgIGChIiQoMCAf4MiEVANAAsLIAYgEXqnQQN2IARqIAVxIgJqLAAAQX9KBEAgBikDAEKAgYKEiJCgwIB/g3qnQQN2IQILIAIgBmogDEEZdiIEOgAAIAJBeGogBXEgBmpBCGogBDoAACAGIAJBf3NBA3RqIAogA0F/c0EDdGopAAA3AwALIAMgCEYgA0EBaiEDRQ0ACyABIAs2AgggASAFNgIAIAFBBGogBjYCACAIRQ0BC0GBgICAeCECIAggB0EDdCIEakEJakUNASAKIARrEBUMAQtBgYCAgHghAgsLIAAgAjYCBCAAIAQ2AgAgCUEwaiQAC8YNAhV/AX4jAEHQAGsiAiQAIAJBADYCECACQgQ3AwggAkEYaiABKAIAIg0gAUEEaigCACIOIAFBCGooAgAiChAfAkACQAJAIAIoAhgiAUUEQCAOIQUgDSEGDAELIApBDGohFCACQTBqIREgAkEoakEFciESIApBCGohFSAKQRRqIRYCQANAIBUoAgAgE2ohCCACKAIkIQcgAigCICEDIAIoAhwiBQRAIAIoAhAiBCACKAIMRgRAIAJBCGogBBA9IAIoAhAhBAsgAigCCCAEQQR0aiIGIAE2AgRBACEEIAZBADYCACAGQQhqIAU2AgAgAiACKAIQQQFqNgIQIAVBA3EhCSAFQX9qQQNPBEAgBUF8cSEMA0AgBCABLQAAQQpGaiABQQFqLQAAQQpGaiABQQJqLQAAQQpGaiABQQNqLQAAQQpGaiEEIAFBBGohASAMQXxqIgwNAAsLIAkEQANAIAQgAS0AAEEKRmohBCABQQFqIQEgCUF/aiIJDQALCyAEIAtqIQsgBSAIaiEICwJAAkACQAJAIAcEQAJAIAMsAAAiAUF/SgRAIAFB/wFxIQQMAQsgAy0AAUE/cSEGIAFBH3EhBSABQV9NBEAgBUEGdCAGciEEDAELIAMtAAJBP3EgBkEGdHIhBiABQXBJBEAgBiAFQQx0ciEEDAELIAVBEnRBgIDwAHEgAy0AA0E/cSAGQQZ0cnIiBEGAgMQARg0CC0EBIRAgCigCJCAERwRAQQAhECAEIAooAiBHDQILIAdBAU0EQCAIQQFqIQgMBQsgAywAASIBQb9/Sg0CDAkLIABBCGogDSAOIAsgCBAcIABCgYCAgDA3AgAMBQtBAiEQDAELIANBAWohAyAIQQFqIQggB0F/aiEHCwJAIAFBf0wEQCADLQABQT9xIQYgAUEfcSEFIAFBX00EQCAFQQZ0IAZyIQEMAgsgAy0AAkE/cSAGQQZ0ciEGIAFBcEkEQCAGIAVBDHRyIQEMAgsgBUESdEGAgPAAcSADLQADQT9xIAZBBnRyciIBQYCAxABGDQIMAQsgAUH/AXEhAQsCQAJAAkACQCAKKAIcIgUgAUcEQCABIAooAhgiBkYNASAGDQJBACEPDAQLQQEhDyAHQQJJDQIgAywAAUG/f0wNCQwCC0EAIQ8gB0ECSQ0BIAMsAAFBv39KDQEMCAtBASEPIAUNAgwBCyAIQQFqIQggA0EBaiEDIAdBf2ohBwsgAkFAayADIAcgFBAfAkACQAJAAkACQCACKAJAIgcEQCACKAJMIQUgAigCSCEGIBYoAgACQCACKAJEIgNBf2oiAUUEQCAHLQAAIQkMAQsgA0UNBCABIAdqLAAAIglBv39MDQQLIAhqIQRBASEIIAlB/wFxIgkgCigCJEYNAUEAIQggCigCICAJRg0BIAMgBGohE0ECIQgMAgsgESANIA4gCyAIEBwgAikDMCEXIABBEGogAigCODYCACAAQQhqIBc3AgAgAEKBgICAMDcCAAwHCyADIARqIRMgAUUNAiABIQMLIANBA3EhCQJAIANBf2pBA0kEQEEAIQQgByEBDAELIANBfHEhDEEAIQQgByEBA0AgBCABLQAAQQpGaiABQQFqLQAAQQpGaiABQQJqLQAAQQpGaiABQQNqLQAAQQpGaiEEIAFBBGohASAMQXxqIgwNAAsLIAlFDQIDQCAEIAEtAABBCkZqIQQgAUEBaiEBIAlBf2oiCQ0ACwwCCyAHIAMgASADEHsAC0EAIQNBACEECyACKAIQIgEgAigCDEYEQCACQQhqIAEQPSACKAIQIQELIAQgC2ohCyACKAIIIAFBBHRqIgEgCDoADiABIBA6AA0gASAHNgIEIAFBATYCACABQQxqIA86AAAgAUEIaiADNgIAIAIgAigCEEEBajYCECACQRhqIAYgBSAKEB8gAigCGCIBRQ0DDAELCyARIA0gDiALIAgQHCACQQI2AiwgAkHCAGogEkECai0AACIBOgAAIAIgEi8AACIHOwFAIAJBOGooAgAhAyACKQMwIRcgAEECOgAEIAAgBzsABSAAQQdqIAE6AAAgAEEQaiADNgIAIABBCGogFzcCACAAQQE2AgALIAIoAgxFDQEgAigCCBAVDAELIAUEQCACKAIQIgEgAigCDEYEQCACQQhqIAEQPSACKAIQIQELIAIoAgggAUEEdGoiASAGNgIEIAFBADYCACABQQhqIAU2AgAgAiACKAIQQQFqNgIQCyAAIAIpAwg3AgQgAEEANgIAIABBDGogAkEQaigCADYCAAsgAkHQAGokAA8LIAMgB0EBIAcQewALqwsCCn8BfgJ/AkAgBARAQQEhDQJAIARBAUYEQEEBIQgMAQtBASEGQQEhBwNAIAchCwJAAkAgBSAKaiIIIARJBEAgAyAGai0AACIHIAMgCGotAAAiBk8EQCAGIAdGDQJBASENIAtBAWohB0EAIQUgCyEKDAMLIAUgC2pBAWoiByAKayENQQAhBQwCCyAIIARB+JfAABBbAAtBACAFQQFqIgcgByANRiIGGyEFIAdBACAGGyALaiEHCyAFIAdqIgYgBEkNAAtBASEGQQEhB0EAIQVBASEIA0AgByELAkACQCAFIAlqIgwgBEkEQCADIAZqLQAAIgcgAyAMai0AACIGTQRAIAYgB0YNAkEBIQggC0EBaiEHQQAhBSALIQkMAwsgBSALakEBaiIHIAlrIQhBACEFDAILIAwgBEH4l8AAEFsAC0EAIAVBAWoiByAHIAhGIgYbIQUgB0EAIAYbIAtqIQcLIAUgB2oiBiAESQ0ACyAKIQULIAUgCSAFIAlLIgUbIgsgBE0EQCANIAggBRsiByALaiIFIAdPBEAgBSAETQRAIAMgAyAHaiALELgBBEAgCyAEIAtrIgZLIQogBEEDcSEHIARBf2pBA0kEQCADIQUMBgsgBEF8cSEIIAMhBQNAQgEgBTEAAIYgD4RCASAFQQFqMQAAhoRCASAFQQJqMQAAhoRCASAFQQNqMQAAhoQhDyAFQQRqIQUgCEF8aiIIDQALDAULQQEhCUEAIQVBASEGQQAhDQNAIAYiCiAFaiIMIARJBEACQAJAAkAgBCAFayAKQX9zaiIIIARJBEAgBUF/cyAEaiANayIGIARPDQEgAyAIai0AACIIIAMgBmotAAAiBk8EQCAGIAhGDQMgCkEBaiEGQQAhBUEBIQkgCiENDAQLIAxBAWoiBiANayEJQQAhBQwDCyAIIARBiJjAABBbAAsgBiAEQZiYwAAQWwALQQAgBUEBaiIIIAggCUYiBhshBSAIQQAgBhsgCmohBgsgByAJRw0BCwtBASEJQQAhBUEBIQZBACEIA0AgBiIKIAVqIg4gBEkEQAJAAkACQCAEIAVrIApBf3NqIgwgBEkEQCAFQX9zIARqIAhrIgYgBE8NASADIAxqLQAAIgwgAyAGai0AACIGTQRAIAYgDEYNAyAKQQFqIQZBACEFQQEhCSAKIQgMBAsgDkEBaiIGIAhrIQlBACEFDAMLIAwgBEGImMAAEFsACyAGIARBmJjAABBbAAtBACAFQQFqIgwgCSAMRiIGGyEFIAxBACAGGyAKaiEGCyAHIAlHDQELCyAHIARNBEAgBCANIAggDSAISxtrIQpBACEJAkAgB0UEQEEAIQcMAQsgB0EDcSEIAkAgB0F/akEDSQRAIAMhBQwBCyAHQXxxIQYgAyEFA0BCASAFMQAAhiAPhEIBIAVBAWoxAACGhEIBIAVBAmoxAACGhEIBIAVBA2oxAACGhCEPIAVBBGohBSAGQXxqIgYNAAsLIAhFDQADQEIBIAUxAACGIA+EIQ8gBUEBaiEFIAhBf2oiCA0ACwsgBAwGCyAHIAQQtQEACyAFIAQQtQEACyAHIAUQtgEACyALIAQQtQEACyAAIAM2AjggACABNgIwIABBADoADiAAQgA3AwAgAEE8akEANgIAIABBNGogAjYCACAAQQxqQYECOwEAIABBCGogAjYCAA8LIAcEQANAQgEgBTEAAIYgD4QhDyAFQQFqIQUgB0F/aiIHDQALCyALIAYgChtBAWohB0F/IQkgCyEKQX8LIQUgACADNgI4IAAgATYCMCAAQQE2AgAgAEE8aiAENgIAIABBNGogAjYCACAAQShqIAU2AgAgAEEkaiAJNgIAIABBIGogAjYCACAAQRxqQQA2AgAgAEEYaiAHNgIAIABBFGogCjYCACAAQRBqIAs2AgAgAEEIaiAPNwIAC+AJAQ9/IwBB0ABrIgEkACABQcgAaiAAQShqKAIAIgY2AgAgAUFAayILIABBIGopAgA3AwAgAUE4aiAAQRhqKQIANwMAIAFBMGogAEEQaikCADcDACABQShqIABBCGopAgA3AwAgASAAKQIANwMgAkAgBkUEQAwBCyABKAIoIQcgASgCJCEIIAEtAEQhCiABQTRqKAIAIgUgAUEsaigCACIMSwRAIApFIAggASgCICIARnEEQAwCCyAHRQRADAILIAggAGshBCABLQBFRSEAA0AgAEEBcUUNAiADIARqQQFqIQNBACEAIAZBf2oiBg0ACwwBCyABQTxqKAIAIgkgC2pBf2ohDSAJQQRNBEAgAS0ARSECA0AgAkH/AXENAgJ/AkAgBSABKAIwIgJJDQADQCACIAdqIQ4gDS0AACEPAkACfyAFIAJrIgRBCE8EQCABQRhqIA8gDiAEEDEgASgCHCEAIAEoAhgMAQtBACEAQQAgBEUNABoDQEEBIA8gACAOai0AAEYNARogBCAAQQFqIgBHDQALIAQhAEEAC0EBRgRAIAEgACACakEBaiICNgIwIAIgCUkgAiAMS3INASAHIAIgCWsiAGogCyAJELgBDQEgASgCICEEIAEgAjYCICAAIARrIQBBAAwECyABIAU2AjAMAgsgBSACTw0ACwsgCkVBACABKAIgIgAgCEYbDQMgAUEBOgBFIAggAGshAEEBCyECIAdFBEBBACEDDAMLIAAgA2pBAWohAyAGQX9qIgYNAAsMAQsgAS0ARSEAAkACQCAKRUEAIAEoAiAiBCAIRhtFBEAgB0UNASAIIARrIQsgAEUhAANAIABBAXFFDQQCQCAFIAEoAjAiAkkNAANAIAIgB2ohCCANLQAAIQoCfyAFIAJrIgRBCE8EQCABQQhqIAogCCAEEDEgASgCDCEAIAEoAggMAQtBACEAQQAgBEUNABoDQEEBIAogACAIai0AAEYNARogBCAAQQFqIgBHDQALIAQhAEEAC0EBRgRAIAEgACACakEBaiICNgIwIAIgCU9BACACIAxNGw0GIAUgAkkNAgwBCwsgASAFNgIwCyABQQE6AEUgAyALakEBaiEDQQAhACAGQX9qIgYNAAsMAwsgAARADAMLIAUgASgCMCICSQRADAMLA0AgAiAHaiEDIA0tAAAhBgJ/IAUgAmsiBEEITwRAIAFBEGogBiADIAQQMSABKAIUIQAgASgCEAwBC0EAIQBBACAERQ0AGgNAQQEgBiAAIANqLQAARg0BGiAEIABBAWoiAEcNAAsgBCEAQQALQQFHBEBBACEDDAQLIAEgACACakEBaiICNgIwIAIgCU9BACACIAxNGw0CIAUgAk8NAAtBACEDDAILIAAEQAwCCyAFIAEoAjAiAkkEQAwCCyAFIAdqIQcCQANAIA0tAAAhAwJ/IAUgAmsiBEEITwRAIAEgAyACIAQQMSABKAIEIQAgASgCAAwBC0EAIQBBACAERQ0AGgNAQQEgAyAAIAJqLQAARg0BGiACIABBAWoiAGogB0cNAAsgBCEAQQALQQFHDQEgASAAIAJqQQFqIgI2AjAgAiAJT0EAIAIgDE0bDQIgBSACTw0AC0EAIQMMAgsgASAFNgIwQQAhAwwBCyAJQQQQtQEACyABQdAAaiQAIAMLzAkBBX8jAEEQayIGJAACQCADRQ0AAkACQAJAAkACQAJAAkACQCADLQAARQRAIAYgATYCACAGIAEgAmoiAzYCBCAGIAM2AgwgBiABNgIIIAYgBkEIaiAEG0EEQQUgBBsRAgBBdmoOBAIBAQMBCyAEDQcgAkUEQEEAIQIMCQsgASACaiEDAkADQAJAIAMiAkF/aiIDLQAAIgRBGHRBGHUiBUF/Sg0AIAVBP3ECfyACQX5qIgMtAAAiBEEYdEEYdSIHQUBOBEAgBEEfcQwBCyAHQT9xAn8gAkF9aiIDLQAAIgRBGHRBGHUiCEFATgRAIARBD3EMAQsgCEE/cSACQXxqIgMtAABBB3FBBnRyC0EGdHILQQZ0ciIEQYCAxABHDQBBACECDAsLIARBIEYgBEF3akEFSXJFBEAgBEGAAUkNAiAEECxFDQILIAEgA0cNAAtBACECDAkLIAIgAWshAgwIC0EAIQMgBEUNAgwEC0EBIQUgBA0CIAYoAgwiAyAGKAIIRgRAQX8hAwwCCyAGIANBf2oiBDYCDCAELQAAIgRBGHRBGHUiBUF/TARAIAYgA0F+aiIENgIMAn8gBC0AACIEQRh0QRh1IgdBQE4EQCAEQR9xDAELIAYgA0F9aiIENgIMIAdBP3ECfyAELQAAIgRBGHRBGHUiCEFATgRAIARBD3EMAQsgBiADQXxqIgM2AgwgCEE/cSADLQAAQQdxQQZ0cgtBBnRyCyEEQX8hAyAFQT9xIARBBnRyIgRBgIDEAEYNAgtBfkF/IARBDUYbIQMMAQtBfyEDIARFDQAgBigCACIDIAYoAgRGBEBBASEFDAILIAYgA0EBajYCAAJAIAMtAAAiBEEYdEEYdUF/Sg0AIAYgA0ECajYCACADLQABQT9xIQUgBEEfcSEHIARB3wFNBEAgB0EGdCAFciEEDAELIAYgA0EDajYCACADLQACQT9xIAVBBnRyIQggBEHwAUkEQCAIIAdBDHRyIQQMAQsgBiADQQRqNgIAQQEhBSAHQRJ0QYCA8ABxIAMtAANBP3EgCEEGdHJyIgRBgIDEAEYNAgtBAkEBIARBCkYbIQUMAQsgAiADaiIERQRAQQAhAgwFCwJAIAQgAk8EQCADDQEgBCECDAYLIAEgBGosAABBv39MDQAgBCECDAULIAEgAkEAIAQQewALIAUgAk8EQCAFIAIiA0YNAQwCCyABIAVqLAAAQb9/TA0BIAUhAwsgASADaiEBIAIgA2shAgwCCyABIAIgBSACEHsACwJAIAJFBEAMAQsgASACaiEJIAEhAwNAAkACfyADIgQsAAAiBUF/SgRAIAVB/wFxIQUgBEEBagwBCyAELQABQT9xIQggBUEfcSEDIAVBX00EQCADQQZ0IAhyIQUgBEECagwBCyAELQACQT9xIAhBBnRyIQggBUFwSQRAIAggA0EMdHIhBSAEQQNqDAELIANBEnRBgIDwAHEgBC0AA0E/cSAIQQZ0cnIiBUGAgMQARg0BIARBBGoLIQMgBUEgRiAFQXdqQQVJckUEQCAFQYABSQ0DIAUQLEUNAwsgByAEayADaiEHIAMgCUcNAQsLIAIhBwsgASAHaiEBIAIgB2shAgsgACACNgIEIAAgATYCACAGQRBqJAALyAsBCH8jAEHgAGsiAyQAIABCATcCACAAQQhqIgRBADYCACAAQQBBEBBBIAQoAgAiBSAAKAIAaiIGQdSDwAApAAA3AAAgBCAFQRBqNgIAIAZBCGpB3IPAACkAADcAACADQQE2AiwgAyABKAIIQShqIgU2AiggAyAANgIYIANB3ABqQQE2AgAgA0ICNwJMIANB8IPAADYCSCADIANBKGo2AlgCQAJAAkACQAJAAkAgA0EYakGYisAAIANByABqEB5FBEAgAigCACEIAkAgAigCCCIBRQ0AIAFBBHQhCkGQhMAAIQZBACEBQQAhBANAAn8gASAIaiIHQQRqIgkgBygCAEUNABoCQCAERQ0AIANBEGogBCgCACAEKAIEQQAgBiAGLQAAQQJGG0EBEBAgA0EIaiADKAIQIAMoAhRBACAHQQ1qIgQgBC0AAEECRhtBABAQIANBGGogAygCCCADKAIMEBIgA0EBNgI0IANBATYCLCADIAU2AiggAyADQRhqNgIwIAMgADYCRCADQQI2AlwgA0IDNwJMIANBmITAADYCSCADIANBKGo2AlggA0HEAGpBmIrAACADQcgAahAeDQUgAygCHEUNACADKAIYEBULIAdBDmohBgJAIAdBDGotAABFBEAgA0ECNgIsIAMgCTYCKCADIAA2AhggA0EBNgJcIANCAjcCTCADQfSEwAA2AkggAyADQShqNgJYIANBGGpBmIrAACADQcgAahAeDQcgA0ECNgI0IANBoIXAADYCMCADQQE2AiwgAyAFNgIoIAMgADYCGCADQQI2AlwgA0IDNwJMIANBmITAADYCSCADIANBKGo2AlggA0EYakGYisAAIANByABqEB5FDQFBq4HAAEErIANByABqQdiBwABBqIXAABBSAAsgA0ECNgIsIAMgCTYCKCADIAA2AhggA0EBNgJcIANCAjcCTCADQcSEwAA2AkggAyADQShqNgJYIANBGGpBmIrAACADQcgAahAeDQcLQQALIQQgCiABQRBqIgFHDQALIARFDQAgAyAEKAIAIAQoAgRBACAGIAYtAABBAkYbQQEQECADQRhqIAMoAgAgAygCBBASIANBNGpBATYCACADQQE2AiwgAyAFNgIoIAMgA0EYajYCMCADIAA2AkQgA0HcAGpBAjYCACADQgM3AkwgA0GYhMAANgJIIAMgA0EoajYCWCADQcQAakGYisAAIANByABqEB4NBSADKAIcRQ0AIAMoAhgQFQsgAEEEaigCACAAQQhqIgQoAgAiAWtBJ00EQCAAIAFBKBBBIAQoAgAhAQsgBCABQShqNgIAIAAoAgAgAWoiAUHIhcAAKQAANwAAIAFBCGpB0IXAACkAADcAACABQRBqQdiFwAApAAA3AAAgAUEYakHghcAAKQAANwAAIAFBIGpB6IXAACkAADcAACADQTxqQQI2AgAgA0E0akEBNgIAIANBoIXAADYCOCADIAU2AjAgA0EBNgIsIAMgBTYCKCADIAA2AhggA0HcAGoiAUEDNgIAIANCBDcCTCADQZiGwAA2AkggAyADQShqNgJYIANBGGpBmIrAACADQcgAahAeDQUgA0EBNgIsIAMgBTYCKCADIAA2AhggAUEBNgIAIANCAjcCTCADQdCGwAA2AkggAyADQShqNgJYIANBGGpBmIrAACADQcgAahAeDQYgAkEEaigCAARAIAgQFQsgA0HgAGokAA8LQauBwABBKyADQcgAakHYgcAAQYCEwAAQUgALQauBwABBKyADQcgAakHYgcAAQbCEwAAQUgALQauBwABBKyADQcgAakHYgcAAQYSFwAAQUgALQauBwABBKyADQcgAakHYgcAAQdSEwAAQUgALQauBwABBKyADQcgAakHYgcAAQbiFwAAQUgALQauBwABBKyADQcgAakHYgcAAQbiGwAAQUgALQauBwABBKyADQcgAakHYgcAAQeCGwAAQUgAL7QkCCH8GfiMAQdAAayIDJAACQAJAAkAQVCIEBEAgA0EgakIANwMAIANBHGpBkIrAADYCACAEIAQpAwAiC0IBfDcDACADQQA2AhggAyALNwMIIAMgBEEIaikDADcDECADQqeAgIDwBDcDSCADQo2AgICgDjcDQCADQoqAgIDgDTcDOCADQtyAgIDACzcDMCADQQhqIANBMGoQGSADQQA2AjggA0IENwMwIAJFBEAgAEEANgIIIABCATcCAEEEIQRBBCEBDAQLIAEgAmohCEEAIQIDQAJ/IAEsAAAiBEF/SgRAIARB/wFxIQQgAUEBagwBCyABLQABQT9xIQUgBEEfcSEGIARBX00EQCAGQQZ0IAVyIQQgAUECagwBCyABLQACQT9xIAVBBnRyIQUgBEFwSQRAIAUgBkEMdHIhBCABQQNqDAELIAZBEnRBgIDwAHEgAS0AA0E/cSAFQQZ0cnIiBEGAgMQARg0EIAFBBGoLIQEgAyAENgIsAkAgA0EIaiADQSxqECJFBEAgAygCLCECIAMoAjgiBCADKAI0RgRAIANBMGogBBA+IAMoAjghBAsgAygCMCAEQQJ0aiACNgIADAELIAMoAjgiBCADKAI0RgRAIANBMGogBBA+IAMoAjghBAsgAygCMCAEQQJ0akHcADYCACADIAMoAjhBAWoiAjYCOCADKAIkRQ0DIAMoAhgiBiADKQMQIgsgAygCLCIJrUKAgICAgICAgASEIgyFQvPK0cunjNmy9ACFIg1CEIkgDSADKQMIIg5C4eSV89bs2bzsAIV8Ig2FIg8gC0Lt3pHzlszct+QAhSILIA5C9crNg9es27fzAIV8Ig5CIIl8IhAgDIUgDSALQg2JIA6FIgt8IgwgC0IRiYUiC3wiDSALQg2JhSILIA9CFYkgEIUiDiAMQiCJQv8BhXwiDHwiDyALQhGJhSILQg2JIAsgDkIQiSAMhSIMIA1CIIl8Ig18IguFIg5CEYkgDiAMQhWJIA2FIgwgD0IgiXwiDXwiDoUiD0INiSAPIAxCEIkgDYUiDCALQiCJfCILfIUiDSAMQhWJIAuFIgsgDkIgiXwiDHwiDiALQhCJIAyFQhWJhSANQhGJhSAOQiCIhSILp3EhBCALQhmIQv8Ag0KBgoSIkKDAgAF+IQ1BACEFIAMoAhwhBwNAIAQgB2opAAAiDCANhSILQn+FIAtC//379+/fv/9+fINCgIGChIiQoMCAf4MhCwNAIAtQBEAgDCAMQgGGg0KAgYKEiJCgwIB/g1BFDQYgBCAFQQhqIgVqIAZxIQQMAgsgC3ohDiALQn98IAuDIQsgByAOp0EDdiAEaiAGcUEDdGsiCkF4aigCACAJRw0ACwsgCkF8aigCACEEIAMoAjQgAkYEQCADQTBqIAIQPiADKAI4IQILIAMoAjAgAkECdGogBDYCAAsgAyADKAI4QQFqIgI2AjggASAIRw0ACwwCC0GwisAAQcYAIANBMGpB2IvAAEHIi8AAEFIAC0GAgcAAQZSDwAAQbwALIABBADYCCCAAQgE3AgAgAygCMCIBIAJBAnRqIQQgAkUNACAAQQAgAhBBCyABIAQgABAoIAMoAjQEQCADKAIwEBULAkAgAygCGCIARQ0AIAAgAEEDdEEIaiIBakEJakUNACADKAIcIAFrEBULIANB0ABqJAALmAkBBX8jAEHwAGsiBCQAIAQgAzYCDCAEIAI2AggCQAJAAkACQAJAIAQCfwJAIAFBgQJPBEACf0GAAiAALACAAkG/f0oNABpB/wEgACwA/wFBv39KDQAaQf4BIAAsAP4BQb9/Sg0AGkH9AQsiBSABSQ0BIAEgBUcNAwsgBCABNgIUIAQgADYCEEGAk8AAIQZBAAwBCyAEIAU2AhQgBCAANgIQQcOYwAAhBkEFCzYCHCAEIAY2AhggAiABSyIFIAMgAUtyDQEgAiADTQRAAkACQCACRQ0AIAIgAU8EQCABIAJGDQEMAgsgACACaiwAAEFASA0BCyADIQILIAQgAjYCICACIAEiA0kEQCACQQFqIgVBACACQX1qIgMgAyACSxsiA0kNBAJAIAMgBUYNACAAIAVqIAAgA2oiB2shBSAAIAJqIggsAABBv39KBEAgBUF/aiEGDAELIAIgA0YNACAIQX9qIgIsAABBv39KBEAgBUF+aiEGDAELIAIgB0YNACAIQX5qIgIsAABBv39KBEAgBUF9aiEGDAELIAIgB0YNACAIQX1qIgIsAABBv39KBEAgBUF8aiEGDAELIAIgB0YNACAFQXtqIQYLIAMgBmohAwsCQCADRQ0AIAMgAU8EQCABIANGDQEMBwsgACADaiwAAEG/f0wNBgsgASADRg0EAn8CQAJAIAAgA2oiASwAACIAQX9MBEAgAS0AAUE/cSEFIABBH3EhAiAAQV9LDQEgAkEGdCAFciECDAILIAQgAEH/AXE2AiRBAQwCCyABLQACQT9xIAVBBnRyIQUgAEFwSQRAIAUgAkEMdHIhAgwBCyACQRJ0QYCA8ABxIAEtAANBP3EgBUEGdHJyIgJBgIDEAEYNBgsgBCACNgIkQQEgAkGAAUkNABpBAiACQYAQSQ0AGkEDQQQgAkGAgARJGwshASAEIAM2AiggBCABIANqNgIsIARBxABqQQU2AgAgBEHsAGpBNDYCACAEQeQAakE0NgIAIARB3ABqQTU2AgAgBEHUAGpBNjYCACAEQgU3AjQgBEGsmsAANgIwIARBAzYCTCAEIARByABqNgJAIAQgBEEYajYCaCAEIARBEGo2AmAgBCAEQShqNgJYIAQgBEEkajYCUCAEIARBIGo2AkggBEEwakHUmsAAEHQACyAEQeQAakE0NgIAIARB3ABqQTQ2AgAgBEHUAGpBAzYCACAEQcQAakEENgIAIARCBDcCNCAEQbiZwAA2AjAgBEEDNgJMIAQgBEHIAGo2AkAgBCAEQRhqNgJgIAQgBEEQajYCWCAEIARBDGo2AlAgBCAEQQhqNgJIIARBMGpB2JnAABB0AAsgACABQQAgBRB7AAsgBCACIAMgBRs2AiggBEHEAGpBAzYCACAEQdwAakE0NgIAIARB1ABqQTQ2AgAgBEIDNwI0IARB7JjAADYCMCAEQQM2AkwgBCAEQcgAajYCQCAEIARBGGo2AlggBCAEQRBqNgJQIAQgBEEoajYCSCAEQTBqQYSZwAAQdAALIAMgBRC2AQALQdCTwABB6JnAABBvAAsgACABIAMgARB7AAv/BwEIfwJAAkAgAEEDakF8cSICIABrIgMgAUsgA0EES3INACABIANrIgZBBEkNACAGQQNxIQdBACEBAkAgA0UNACADQQNxIQgCQCACIABBf3NqQQNJBEAgACECDAELIANBfHEhBCAAIQIDQCABIAIsAABBv39KaiACQQFqLAAAQb9/SmogAkECaiwAAEG/f0pqIAJBA2osAABBv39KaiEBIAJBBGohAiAEQXxqIgQNAAsLIAhFDQADQCABIAIsAABBv39KaiEBIAJBAWohAiAIQX9qIggNAAsLIAAgA2ohAAJAIAdFDQAgACAGQXxxaiICLAAAQb9/SiEFIAdBAUYNACAFIAIsAAFBv39KaiEFIAdBAkYNACAFIAIsAAJBv39KaiEFCyAGQQJ2IQMgASAFaiEEA0AgACEBIANFDQIgA0HAASADQcABSRsiBUEDcSEGIAVBAnQhBwJAIAVB/AFxIghBAnQiAEUEQEEAIQIMAQsgACABaiEJQQAhAiABIQADQCACIAAoAgAiAkF/c0EHdiACQQZ2ckGBgoQIcWogAEEEaigCACICQX9zQQd2IAJBBnZyQYGChAhxaiAAQQhqKAIAIgJBf3NBB3YgAkEGdnJBgYKECHFqIABBDGooAgAiAkF/c0EHdiACQQZ2ckGBgoQIcWohAiAAQRBqIgAgCUcNAAsLIAEgB2ohACADIAVrIQMgAkEIdkH/gfwHcSACQf+B/AdxakGBgARsQRB2IARqIQQgBkUNAAsgASAIQQJ0aiEAIAZB/////wNqIgNB/////wNxIgFBAWoiAkEDcQJAIAFBA0kEQEEAIQIMAQsgAkH8////B3EhAUEAIQIDQCACIAAoAgAiAkF/c0EHdiACQQZ2ckGBgoQIcWogAEEEaigCACICQX9zQQd2IAJBBnZyQYGChAhxaiAAQQhqKAIAIgJBf3NBB3YgAkEGdnJBgYKECHFqIABBDGooAgAiAkF/c0EHdiACQQZ2ckGBgoQIcWohAiAAQRBqIQAgAUF8aiIBDQALCwRAIANBgYCAgHxqIQEDQCACIAAoAgAiAkF/c0EHdiACQQZ2ckGBgoQIcWohAiAAQQRqIQAgAUF/aiIBDQALCyACQQh2Qf+B/AdxIAJB/4H8B3FqQYGABGxBEHYgBGoPCyABRQRAQQAPCyABQQNxIQICQCABQX9qQQNJBEAMAQsgAUF8cSEBA0AgBCAALAAAQb9/SmogAEEBaiwAAEG/f0pqIABBAmosAABBv39KaiAAQQNqLAAAQb9/SmohBCAAQQRqIQAgAUF8aiIBDQALCyACRQ0AA0AgBCAALAAAQb9/SmohBCAAQQFqIQAgAkF/aiICDQALCyAEC4cHAQV/IAAQvQEiACAAEK8BIgIQugEhAQJAAkACQCAAELABDQAgACgCACEDAkAgABClAUUEQCACIANqIQIgACADELsBIgBBwLHAACgCAEcNASABKAIEQQNxQQNHDQJBuLHAACACNgIAIAAgAiABEIYBDwsgAiADakEQaiEADAILIANBgAJPBEAgABA1DAELIABBDGooAgAiBCAAQQhqKAIAIgVHBEAgBSAENgIMIAQgBTYCCAwBC0GorsAAQaiuwAAoAgBBfiADQQN2d3E2AgALAkAgARCiAQRAIAAgAiABEIYBDAELAkACQAJAQcSxwAAoAgAgAUcEQCABQcCxwAAoAgBHDQFBwLHAACAANgIAQbixwABBuLHAACgCACACaiIBNgIAIAAgARCUAQ8LQcSxwAAgADYCAEG8scAAQbyxwAAoAgAgAmoiATYCACAAIAFBAXI2AgQgAEHAscAAKAIARg0BDAILIAEQrwEiAyACaiECAkAgA0GAAk8EQCABEDUMAQsgAUEMaigCACIEIAFBCGooAgAiAUcEQCABIAQ2AgwgBCABNgIIDAELQaiuwABBqK7AACgCAEF+IANBA3Z3cTYCAAsgACACEJQBIABBwLHAACgCAEcNAkG4scAAIAI2AgAMAwtBuLHAAEEANgIAQcCxwABBADYCAAtB4LHAACgCACABTw0BQYCAfEEIQQgQlwFBFEEIEJcBakEQQQgQlwFqa0F3cUF9aiIAQQBBEEEIEJcBQQJ0ayIBIAEgAEsbRQ0BQcSxwAAoAgBFDQFBCEEIEJcBIQBBFEEIEJcBIQFBEEEIEJcBIQJBAAJAQbyxwAAoAgAiBCACIAEgAEEIa2pqIgJNDQBBxLHAACgCACEBQdCxwAAhAAJAA0AgACgCACABTQRAIAAQpwEgAUsNAgsgACgCCCIADQALQQAhAAsgABCxAQ0AIABBDGooAgAaDAALQQAQN2tHDQFBvLHAACgCAEHgscAAKAIATQ0BQeCxwABBfzYCAA8LIAJBgAJJDQEgACACEDRB6LHAAEHoscAAKAIAQX9qIgA2AgAgAA0AEDcaDwsPCyACQQN2IgNBA3RBsK7AAGohAQJ/QaiuwAAoAgAiAkEBIAN0IgNxBEAgASgCCAwBC0GorsAAIAIgA3I2AgAgAQshAyABIAA2AgggAyAANgIMIAAgATYCDCAAIAM2AggL8gYBBn8CQAJAAkACQAJAIAAoAggiCEEBR0EAIAAoAhAiBEEBRxtFBEAgBEEBRw0DIAEgAmohByAAQRRqKAIAIgYNASABIQQMAgsgACgCGCABIAIgAEEcaigCACgCDBEDACEDDAMLIAEhBANAIAQiAyAHRg0CAn8gA0EBaiADLAAAIgRBf0oNABogA0ECaiAEQWBJDQAaIANBA2ogBEFwSQ0AGiAEQf8BcUESdEGAgPAAcSADLQADQT9xIAMtAAJBP3FBBnQgAy0AAUE/cUEMdHJyckGAgMQARg0DIANBBGoLIgQgBSADa2ohBSAGQX9qIgYNAAsLIAQgB0YNACAELAAAIgNBf0ogA0FgSXIgA0FwSXJFBEAgA0H/AXFBEnRBgIDwAHEgBC0AA0E/cSAELQACQT9xQQZ0IAQtAAFBP3FBDHRycnJBgIDEAEYNAQsCQAJAIAVFBEBBACEEDAELIAUgAk8EQEEAIQMgBSACIgRGDQEMAgtBACEDIAUiBCABaiwAAEFASA0BCyAEIQUgASEDCyAFIAIgAxshAiADIAEgAxshAQsgCEUNASAAQQxqKAIAIQcCQCACQRBPBEAgASACEBQhBAwBCyACRQRAQQAhBAwBCyACQQNxIQUCQCACQX9qQQNJBEBBACEEIAEhAwwBCyACQXxxIQZBACEEIAEhAwNAIAQgAywAAEG/f0pqIANBAWosAABBv39KaiADQQJqLAAAQb9/SmogA0EDaiwAAEG/f0pqIQQgA0EEaiEDIAZBfGoiBg0ACwsgBUUNAANAIAQgAywAAEG/f0pqIQQgA0EBaiEDIAVBf2oiBQ0ACwsgByAESwRAQQAhAyAHIARrIgQhBgJAAkACQEEAIAAtACAiBSAFQQNGG0EDcUEBaw4CAAECC0EAIQYgBCEDDAELIARBAXYhAyAEQQFqQQF2IQYLIANBAWohAyAAQRxqKAIAIQQgACgCBCEFIAAoAhghAAJAA0AgA0F/aiIDRQ0BIAAgBSAEKAIQEQEARQ0AC0EBDwtBASEDIAVBgIDEAEYNASAAIAEgAiAEKAIMEQMADQFBACEDA0AgAyAGRgRAQQAPCyADQQFqIQMgACAFIAQoAhARAQBFDQALIANBf2ogBkkPCwwBCyADDwsgACgCGCABIAIgAEEcaigCACgCDBEDAAv+BgEGf0ErQYCAxAAgACgCACIFQQFxIgYbIQogBCAGaiEHAkAgBUEEcUUEQEEAIQEMAQsCQCACQRBPBEAgASACEBQhCAwBCyACRQ0AIAJBA3EhBgJAIAJBf2pBA0kEQCABIQUMAQsgAkF8cSEJIAEhBQNAIAggBSwAAEG/f0pqIAVBAWosAABBv39KaiAFQQJqLAAAQb9/SmogBUEDaiwAAEG/f0pqIQggBUEEaiEFIAlBfGoiCQ0ACwsgBkUNAANAIAggBSwAAEG/f0pqIQggBUEBaiEFIAZBf2oiBg0ACwsgByAIaiEHCwJAAkAgACgCCEUEQEEBIQUgACAKIAEgAhBuDQEMAgsCQAJAAkACQCAAQQxqKAIAIgYgB0sEQCAALQAAQQhxDQRBACEFIAYgB2siBiEHQQEgAC0AICIIIAhBA0YbQQNxQQFrDgIBAgMLQQEhBSAAIAogASACEG4NBAwFC0EAIQcgBiEFDAELIAZBAXYhBSAGQQFqQQF2IQcLIAVBAWohBSAAQRxqKAIAIQggACgCBCEGIAAoAhghCQJAA0AgBUF/aiIFRQ0BIAkgBiAIKAIQEQEARQ0AC0EBDwtBASEFIAZBgIDEAEYNASAAIAogASACEG4NASAAKAIYIAMgBCAAKAIcKAIMEQMADQEgACgCHCEBIAAoAhghAEEAIQUCfwNAIAcgBSAHRg0BGiAFQQFqIQUgACAGIAEoAhARAQBFDQALIAVBf2oLIAdJIQUMAQsgACgCBCEIIABBMDYCBCAALQAgIQlBASEFIABBAToAICAAIAogASACEG4NAEEAIQUgBiAHayIBIQICQAJAAkBBASAALQAgIgYgBkEDRhtBA3FBAWsOAgABAgtBACECIAEhBQwBCyABQQF2IQUgAUEBakEBdiECCyAFQQFqIQUgAEEcaigCACEGIAAoAgQhASAAKAIYIQcCQANAIAVBf2oiBUUNASAHIAEgBigCEBEBAEUNAAtBAQ8LQQEhBSABQYCAxABGDQAgACgCGCADIAQgACgCHCgCDBEDAA0AIAAoAhwhAyAAKAIYIQRBACEGAkADQCACIAZGDQEgBkEBaiEGIAQgASADKAIQEQEARQ0ACyAGQX9qIAJJDQELIAAgCToAICAAIAg2AgRBAA8LIAUPCyAAKAIYIAMgBCAAQRxqKAIAKAIMEQMAC4MHAQZ/AkACQAJAIAJBCU8EQCADIAIQJyICDQFBAA8LQQAhAkGAgHxBCEEIEJcBQRRBCBCXAWpBEEEIEJcBamtBd3FBfWoiAUEAQRBBCBCXAUECdGsiBSAFIAFLGyADTQ0BQRAgA0EEakEQQQgQlwFBe2ogA0sbQQgQlwEhBSAAEL0BIgEgARCvASIGELoBIQQCQAJAAkACQAJAAkACQCABEKUBRQRAIAYgBU8NASAEQcSxwAAoAgBGDQIgBEHAscAAKAIARg0DIAQQogENByAEEK8BIgcgBmoiCCAFSQ0HIAggBWshBiAHQYACSQ0EIAQQNQwFCyABEK8BIQQgBUGAAkkNBiAEIAVBBGpPQQAgBCAFa0GBgAhJGw0FIAEoAgAiBiAEakEQaiEHIAVBH2pBgIAEEJcBIQRBACIFRQ0GIAUgBmoiASAEIAZrIgBBcGoiAjYCBCABIAIQugFBBzYCBCABIABBdGoQugFBADYCBEHIscAAQcixwAAoAgAgBCAHa2oiADYCAEHkscAAQeSxwAAoAgAiAiAFIAUgAksbNgIAQcyxwABBzLHAACgCACICIAAgAiAASxs2AgAMCQsgBiAFayIEQRBBCBCXAUkNBCABIAUQugEhBiABIAUQggEgBiAEEIIBIAYgBBAhDAQLQbyxwAAoAgAgBmoiBiAFTQ0EIAEgBRC6ASEEIAEgBRCCASAEIAYgBWsiBUEBcjYCBEG8scAAIAU2AgBBxLHAACAENgIADAMLQbixwAAoAgAgBmoiBiAFSQ0DAkAgBiAFayIEQRBBCBCXAUkEQCABIAYQggFBACEEQQAhBgwBCyABIAUQugEiBiAEELoBIQcgASAFEIIBIAYgBBCUASAHIAcoAgRBfnE2AgQLQcCxwAAgBjYCAEG4scAAIAQ2AgAMAgsgBEEMaigCACIJIARBCGooAgAiBEcEQCAEIAk2AgwgCSAENgIIDAELQaiuwABBqK7AACgCAEF+IAdBA3Z3cTYCAAsgBkEQQQgQlwFPBEAgASAFELoBIQQgASAFEIIBIAQgBhCCASAEIAYQIQwBCyABIAgQggELIAENAwsgAxALIgVFDQEgBSAAIAMgARCvAUF4QXwgARClARtqIgEgASADSxsQuQEgABAVDwsgAiAAIAMgASABIANLGxC5ARogABAVCyACDwsgARClARogARC8AQvbBQIKfwd+IwBBMGsiAiQAIABBGGooAgBBAkEEIABBHGooAgAbIgNJBEAgAiAAQRBqIAMgABAMCyACQSBqIAFBGGopAgA3AwAgAkEYaiABQRBqKQIANwMAIAJBEGogAUEIaikCADcDACACQoCAgIDAADcDKCACIAEpAgA3AwggAEEQaiEJQQAhAyAAQRRqIQoDQCAAKAIQIgQgAkEIaiADQQN0aikCACIQQv////8PgyIMIABBCGopAwAiDYVC88rRy6eM2bLwAIUiDkIQiSAOIAApAwAiD0Lh5JXz1uzZvOwAhXwiDoUiESANQu3ekfOWzNy35ACFIg0gD0L1ys2D16zbt/MAhXwiD0IgiXwiEiAMQoCAgICAgICABISFIA4gDUINiSAPhSIMfCINIAxCEYmFIgx8Ig4gDEINiYUiDCARQhWJIBKFIg8gDUIgiUL/AYV8Ig18IhEgDEIRiYUiDEINiSAMIA9CEIkgDYUiDSAOQiCJfCIOfCIMhSIPQhGJIA8gDUIViSAOhSINIBFCIIl8Ig58Ig+FIhFCDYkgESANQhCJIA6FIg0gDEIgiXwiDHyFIg4gDUIViSAMhSIMIA9CIIl8Ig18Ig8gDEIQiSANhUIViYUgDkIRiYUgD0IgiYUiDKdxIQEgDEIZiEL/AINCgYKEiJCgwIABfiEOIANBAWohAyAKKAIAIQUgEKchBiAQQiCIpyEHQQAhCAJAAkADQCABIAVqKQAAIg0gDoUiEEJ/hSAQQv/9+/fv37//fnyDQoCBgoSIkKDAgH+DIRADQCAQUARAIA0gDUIBhoNCgIGChIiQoMCAf4NQRQ0DIAEgCEEIaiIIaiAEcSEBDAILIBB6IQ8gEEJ/fCAQgyEQIAUgD6dBA3YgAWogBHFBA3RrIgtBeGooAgAgBkcNAAsLIAtBfGogBzYCAAwBCyAJIAwgBiAHIAAQJgsgA0EERw0ACyACQTBqJAALmAUBB38CQAJ/AkAgACABayACSQRAIAEgAmohBSAAIAJqIQMgACACQQ9NDQIaIANBfHEhAEEAIANBA3EiBmshByAGBEAgASACakF/aiEEA0AgA0F/aiIDIAQtAAA6AAAgBEF/aiEEIAAgA0kNAAsLIAAgAiAGayIGQXxxIgJrIQNBACACayECIAUgB2oiBUEDcQRAIAJBf0oNAiAFQQN0IgRBGHEhByAFQXxxIghBfGohAUEAIARrQRhxIQkgCCgCACEEA0AgAEF8aiIAIAQgCXQgASgCACIEIAd2cjYCACABQXxqIQEgACADSw0ACwwCCyACQX9KDQEgASAGakF8aiEBA0AgAEF8aiIAIAEoAgA2AgAgAUF8aiEBIAAgA0sNAAsMAQsCQCACQQ9NBEAgACEDDAELIABBACAAa0EDcSIFaiEEIAUEQCAAIQMgASEAA0AgAyAALQAAOgAAIABBAWohACADQQFqIgMgBEkNAAsLIAQgAiAFayICQXxxIgZqIQMCQCABIAVqIgVBA3EEQCAGQQFIDQEgBUEDdCIAQRhxIQcgBUF8cSIIQQRqIQFBACAAa0EYcSEJIAgoAgAhAANAIAQgACAHdiABKAIAIgAgCXRyNgIAIAFBBGohASAEQQRqIgQgA0kNAAsMAQsgBkEBSA0AIAUhAQNAIAQgASgCADYCACABQQRqIQEgBEEEaiIEIANJDQALCyACQQNxIQIgBSAGaiEBCyACRQ0CIAIgA2ohAANAIAMgAS0AADoAACABQQFqIQEgA0EBaiIDIABJDQALDAILIAZBA3EiAEUNASACIAVqIQUgAyAAawshACAFQX9qIQEDQCADQX9qIgMgAS0AADoAACABQX9qIQEgACADSQ0ACwsLwwUCAX8CfiMAQfAAayIFJAAgBSADNgIkIAUgAjYCICAFIAFBBGo2AiggBUHQAGogBUEgahANIAVB0ABqQQRyIQICQAJAAkAgBSgCUEUEQCAFQThqIAJBCGooAgAiAzYCACAFIAIpAgAiBjcDMCAFQdgAaiADNgIAIAUgBjcDUCAFQUBrIAVBIGogBUHQAGoQESAFQSE2AmQgBUGwh8AAQQIQATYCaCAFIAUoAkAiAiAFKAJIEAE2AmwgBUEYaiABIAVB5ABqIAVB6ABqIAVB7ABqEGYgBSgCHCEBAkAgBSgCGEUEQCAFKAJsIgNBJE8EQCADEAALIAUoAmgiA0EkTwRAIAMQAAsgBSgCZCIDQSRPBEAgAxAACyAFIAE2AmwgBUEhNgJQIAVBCGogBUHsAGogBUHQAGogBBBpIAUoAgwhASAFKAIIRQ0DIABCgYCAgBA3AgAgAUEkTwRAIAEQAAsgBSgCUCIAQSRPBEAgABAACyAFKAJsIgBBJEkNASAAEAAMAQsgBSABNgJQIAVBEGogBUHQAGooAgAQBSIBEAIgBSgCECIERQ0DIAUoAhQhAyABQSNLBEAgARAACyAAQgE3AgAgAEEQaiADNgIAIABBDGogAzYCACAAQQhqIAQ2AgAgBSgCUCIAQSRPBEAgABAACyAFKAJsIgBBJE8EQCAAEAALIAUoAmgiAEEkTwRAIAAQAAsgBSgCZCIAQSRJDQAgABAACyAFKAJERQ0DIAIQFQwDCyAFQcgAaiACQQhqKQIAIgY3AwAgBSACKQIAIgc3A0AgAEEMaiAGNwIAIAAgBzcCBCAAQQE2AgAMAgsgBSgCUCIDQSRPBEAgAxAACyAAQQA2AgAgACABNgIEIAUoAmwiAEEkTwRAIAAQAAsgBSgCREUNASACEBUMAQtBgIHAAEG0h8AAEG8ACyAFQfAAaiQAC6wFAQN/IwBBgAFrIgUkACAFQfAAakEKNgIAIAVB6ABqQoqAgIAQNwMAIAVB5ABqIAI2AgAgBUHgAGpBADYCACAFQdwAaiACNgIAIAUgAzYCeCAFQQA7AXQgBSABNgJYIAUgAjYCVCAFQQA2AlACQCADBEAgBUEANgJ4IANBf2oiBgRAA0AgBUEQaiAFQdAAahAdIAUoAhBFDQMgBkF/aiIGDQALCyAFQQhqIAVB0ABqEB0gBSgCCEUNAQsgBSAFQdAAahAdIAUoAgAiBkUNACAFKAIEIQcgBSAGNgIYIAUgBzYCHCAFQfAAakEKNgIAIAVB6ABqQoqAgIAQNwMAIAVB5ABqIAI2AgBBACEHIAVB4ABqQQA2AgAgBUHcAGogAjYCACAFIAM2AnggBUEBOwF0IAUgATYCWCAFIAI2AlQgBUEANgJQIAUgBCAFQdAAahAPayIBNgIkIAVBADYCMCAFQgE3AygCQCABQX9qIgIEQCAFQShqQQAgAhBBIAUoAjAhBgNAIAUoAiwgBkYEfyAFQShqIAYQQCAFKAIwBSAGCyAFKAIoakEgOgAAIAUgBSgCMEEBaiIGNgIwIAJBf2oiAg0ACyAFKAIsIgcgBkcNAQsgBUEoaiAHQQEQQSAFKAIwIQYLIAUoAiggBmpB3gA6AAAgBSAGQQFqNgIwIAVB7ABqQQE2AgAgBUHkAGpBAjYCACAFQdwAakEDNgIAIAVBAzYCVCAFIANBAWo2AjQgBSAFQShqNgJoIAUgBUEYajYCYCAFIAVBJGo2AlggBSAFQTRqNgJQIAVBzABqQQQ2AgAgBUIENwI8IAVBxILAADYCOCAFIAVB0ABqNgJIIAAgBUE4ahAjIAUoAiwEQCAFKAIoEBULIAVBgAFqJAAPC0GAgcAAQaSCwAAQbwALwAQBDX8jAEEQayIFJAACQCABLQAlDQAgASgCCCEIAn8CQCABQRRqKAIAIgYgAUEQaigCACIDSQ0AIAYgAUEMaigCACIMSw0AIAFBHGooAgAiByABQSBqIg5qQX9qIQ0CQCAHQQRNBEADQCADIAhqIQkgDS0AACEKAn8gBiADayIEQQhPBEAgBUEIaiAKIAkgBBAxIAUoAgwhAiAFKAIIDAELQQAhAkEAIARFDQAaA0BBASAKIAIgCWotAABGDQEaIAQgAkEBaiICRw0ACyAEIQJBAAtBAUcNAiABIAIgA2pBAWoiAzYCEAJAIAMgB0kgAyAMS3INACAIIAMgB2siBGogDiAHELgBDQAgASgCACECIAEgAzYCACAEIAJrDAULIAYgA08NAAwDCwALA0AgAyAIaiEJIA0tAAAhCgJ/IAYgA2siBEEITwRAIAUgCiAJIAQQMSAFKAIEIQIgBSgCAAwBC0EAIQJBACAERQ0AGgNAQQEgCiACIAlqLQAARg0BGiAEIAJBAWoiAkcNAAsgBCECQQALQQFHDQEgASACIANqQQFqIgM2AhAgAyAHT0EAIAMgDE0bRQRAIAYgA08NAQwDCwsgB0EEELUBAAsgASAGNgIQCyABLQAkIAEoAgAiAiABKAIEIgRHckUNASABQQE6ACUgBCACawshAyAIRQ0AIAIgCGohCyADRQRAQQAhAgwBCyADQX9qIgEgAyABIAtqLQAAQQ1GGyECCyAAIAI2AgQgACALNgIAIAVBEGokAAv+BAEKfyMAQTBrIgMkACADQSRqIAE2AgAgA0EDOgAoIANCgICAgIAENwMIIAMgADYCICADQQA2AhggA0EANgIQAkACQAJAIAIoAggiCkUEQCACQRRqKAIAIgRFDQEgAigCACEBIAIoAhAhACAEQX9qQf////8BcUEBaiIHIQQDQCABQQRqKAIAIgUEQCADKAIgIAEoAgAgBSADKAIkKAIMEQMADQQLIAAoAgAgA0EIaiAAQQRqKAIAEQEADQMgAEEIaiEAIAFBCGohASAEQX9qIgQNAAsMAQsgAkEMaigCACIARQ0AIABBBXQhCyAAQX9qQf///z9xQQFqIQcgAigCACEBA0AgAUEEaigCACIABEAgAygCICABKAIAIAAgAygCJCgCDBEDAA0DCyADIAQgCmoiBUEcai0AADoAKCADIAVBBGopAgBCIIk3AwggBUEYaigCACEGIAIoAhAhCEEAIQlBACEAAkACQAJAIAVBFGooAgBBAWsOAgACAQsgBkEDdCAIaiIMKAIEQTdHDQEgDCgCACgCACEGC0EBIQALIAMgBjYCFCADIAA2AhAgBUEQaigCACEAAkACQAJAIAVBDGooAgBBAWsOAgACAQsgAEEDdCAIaiIGKAIEQTdHDQEgBigCACgCACEAC0EBIQkLIAMgADYCHCADIAk2AhggCCAFKAIAQQN0aiIAKAIAIANBCGogACgCBBEBAA0CIAFBCGohASALIARBIGoiBEcNAAsLQQAhACAHIAIoAgRJIgFFDQEgAygCICACKAIAIAdBA3RqQQAgARsiASgCACABKAIEIAMoAiQoAgwRAwBFDQELQQEhAAsgA0EwaiQAIAALwgQBCH8jAEHQAGsiBCQAIARBEGogASACIAMoAgAgA0EIaigCABAOAkACQAJAAkACQAJAIAQoAhBFBEAgBEEeai0AAA0EIARBxABqKAIAIQYgBCgCQCEHIARBHGotAABFIQggBCgCFCEDA0ACQCADRQ0AIAYgA00EQCADIAZGDQEMCQsgAyAHaiwAAEFASA0ICyADIAZGDQICfyADIAdqIgksAAAiBUF/TARAIAktAAFBP3EiCiAFQR9xIgtBBnRyIAVBYEkNARogCS0AAkE/cSAKQQZ0ciIKIAtBDHRyIAVBcEkNARogC0ESdEGAgPAAcSAJLQADQT9xIApBBnRycgwBCyAFQf8BcQshBSAIRQRAIAMhBgwECyAFQYCAxABGDQQCf0EBIAVBgAFJDQAaQQIgBUGAEEkNABpBA0EEIAVBgIAESRsLIANqIQNBACEIDAALAAsgBEEYaiEDIARBzABqKAIAIQYgBEHEAGooAgAhBSAEKAJIIQcgBCgCQCEIIARBNGooAgBBf0cEQCAEIAMgCCAFIAcgBkEAECQMBQsgBCADIAggBSAHIAZBARAkDAQLIAgNAQsgBEEIaiAGNgIAIAQgBjYCBCAEQQE2AgAMAgsgBEEBOgAeCyAEQQA2AgALAkAgBCgCAARAIAQoAgQhAyAAQQxqIAIgBEEIaigCACICazYCACAAQQhqIAEgAmo2AgAgACADNgIEIAAgATYCAAwBCyAAQQA2AgALIARB0ABqJAAPCyAHIAYgAyAGEHsAC5QEAQ1/IwBBsAFrIgEkAAJAAkAgAARAIAAoAgANASAAQQA2AgAgAUGIAWoiAiAAQRBqKQIANwMAIAFBgAFqIgMgAEEIaikCADcDACABQZABaiIEIABBGGopAgA3AwAgAUGYAWoiBSAAQSBqKQIANwMAIAFBoAFqIgYgAEEoaikCADcDACABQagBaiIHIABBMGopAgA3AwAgAUEQaiIIIAFBhAFqKQIANwMAIAFBGGoiCSABQYwBaikCADcDACABQSBqIgogAUGUAWopAgA3AwAgAUEoaiILIAFBnAFqKQIANwMAIAFBMGoiDCABQaQBaikCADcDACABQThqIg0gAUGsAWooAgA2AgAgASAAKQIANwN4IAEgASkCfDcDCCAAEBUgAUHwAGogDSgCADYCACABQegAaiAMKQMANwMAIAFB4ABqIAspAwA3AwAgAUHYAGogCikDADcDACABQdAAaiAJKQMANwMAIAFByABqIAgpAwA3AwAgASABKQMINwNAIAFB+ABqIAFBQGsQOkE8QQQQngEiAEUNAiAAQQA2AgAgACABKQN4NwIEIABBDGogAykDADcCACAAQRRqIAIpAwA3AgAgAEEcaiAEKQMANwIAIABBJGogBSkDADcCACAAQSxqIAYpAwA3AgAgAEE0aiAHKQMANwIAIAFBsAFqJAAgAA8LEK0BAAsQrgEAC0E8QQQQswEAC9cEAQR/IAAgARC6ASECAkACQAJAIAAQsAENACAAKAIAIQMCQCAAEKUBRQRAIAEgA2ohASAAIAMQuwEiAEHAscAAKAIARw0BIAIoAgRBA3FBA0cNAkG4scAAIAE2AgAgACABIAIQhgEPCyABIANqQRBqIQAMAgsgA0GAAk8EQCAAEDUMAQsgAEEMaigCACIEIABBCGooAgAiBUcEQCAFIAQ2AgwgBCAFNgIIDAELQaiuwABBqK7AACgCAEF+IANBA3Z3cTYCAAsgAhCiAQRAIAAgASACEIYBDAILAkBBxLHAACgCACACRwRAIAJBwLHAACgCAEcNAUHAscAAIAA2AgBBuLHAAEG4scAAKAIAIAFqIgE2AgAgACABEJQBDwtBxLHAACAANgIAQbyxwABBvLHAACgCACABaiIBNgIAIAAgAUEBcjYCBCAAQcCxwAAoAgBHDQFBuLHAAEEANgIAQcCxwABBADYCAA8LIAIQrwEiAyABaiEBAkAgA0GAAk8EQCACEDUMAQsgAkEMaigCACIEIAJBCGooAgAiAkcEQCACIAQ2AgwgBCACNgIIDAELQaiuwABBqK7AACgCAEF+IANBA3Z3cTYCAAsgACABEJQBIABBwLHAACgCAEcNAUG4scAAIAE2AgALDwsgAUGAAk8EQCAAIAEQNA8LIAFBA3YiAkEDdEGwrsAAaiEBAn9BqK7AACgCACIDQQEgAnQiAnEEQCABKAIIDAELQaiuwAAgAiADcjYCACABCyECIAEgADYCCCACIAA2AgwgACABNgIMIAAgAjYCCAuYBAIDfwZ+IABBHGooAgBFBEBBAA8LIABBEGooAgAiAiAAQQhqKQMAIgUgASgCACIErUKAgICAgICAgASEIgaFQvPK0cunjNmy9ACFIgdCEIkgByAAKQMAIghC4eSV89bs2bzsAIV8IgeFIgkgBULt3pHzlszct+QAhSIFIAhC9crNg9es27fzAIV8IghCIIl8IgogBoUgByAFQg2JIAiFIgV8IgYgBUIRiYUiBXwiByAFQg2JhSIFIAlCFYkgCoUiCCAGQiCJQv8BhXwiBnwiCSAFQhGJhSIFQg2JIAUgCEIQiSAGhSIGIAdCIIl8Igd8IgWFIghCEYkgCCAGQhWJIAeFIgYgCUIgiXwiB3wiCIUiCUINiSAJIAZCEIkgB4UiBiAFQiCJfCIFfIUiByAGQhWJIAWFIgUgCEIgiXwiBnwiCCAFQhCJIAaFQhWJhSAHQhGJhSAIQiCIhSIFp3EhASAFQhmIQv8Ag0KBgoSIkKDAgAF+IQcgAEEUaigCACEAA0AgACABaikAACIGIAeFIgVCf4UgBUL//fv379+//358g0KAgYKEiJCgwIB/gyEFAkADQCAFUARAIAYgBkIBhoNCgIGChIiQoMCAf4NQDQJBAA8LIAV6IQggBUJ/fCAFgyEFIAAgCKdBA3YgAWogAnFBA3RrQXhqKAIAIARHDQALQQEPCyABIANBCGoiA2ogAnEhAQwACwAL4QMBCH8jAEEgayIEJAAgAUEUaigCACEJIAEoAgAhBQJAIAFBBGooAgAiB0EDdEUEQAwBCyAHQX9qQf////8BcSICQQFqIgNBB3EhBgJ/IAJBB0kEQEEAIQMgBQwBCyAFQTxqIQIgA0H4////A3EhCEEAIQMDQCACKAIAIAJBeGooAgAgAkFwaigCACACQWhqKAIAIAJBYGooAgAgAkFYaigCACACQVBqKAIAIAJBSGooAgAgA2pqampqampqIQMgAkFAayECIAhBeGoiCA0ACyACQURqCyAGRQ0AQQRqIQIDQCACKAIAIANqIQMgAkEIaiECIAZBf2oiBg0ACwsCQAJAAkAgCUUEQCADIQIMAQsCQCAHRQ0AIAUoAgQNACADQRBJDQILIAMgA2oiAiADSQ0BCyACRQ0AAkAgAkF/SgRAIAJBARCeASIDRQ0BDAMLEHMACyACQQEQswEAC0EBIQNBACECCyAAQQA2AgggACACNgIEIAAgAzYCACAEIAA2AgQgBEEYaiABQRBqKQIANwMAIARBEGogAUEIaikCADcDACAEIAEpAgA3AwggBEEEakG0kcAAIARBCGoQHkUEQCAEQSBqJAAPC0GkksAAQTMgBEEIakHMkcAAQfCSwAAQUgALzwMCDX8BfgJAIAVBf2oiDSABKAIUIghqIgcgA0kEQEEAIAEoAggiCmshDiAFIAEoAhAiD2shECABKAIcIQsgASkDACEUA0ACQAJAAkAgFCACIAdqMQAAiEIBg1BFBEAgCiAKIAsgCiALSxsgBhsiCSAFIAkgBUsbIQwgAiAIaiERIAkhBwJAA0AgByAMRgRAQQAgCyAGGyEMIAohBwJAAkACQANAIAwgB08EQCABIAUgCGoiAjYCFCAGRQ0CDA4LIAdBf2oiByAFTw0CIAcgCGoiCSADTw0DIAQgB2otAAAgAiAJai0AAEYNAAsgASAIIA9qIgg2AhQgECEHIAZFDQgMCQsgAUEANgIcDAsLIAcgBUHggMAAEFsACyAJIANB8IDAABBbAAsgByAIaiADTw0BIAcgEWohEiAEIAdqIAdBAWohBy0AACASLQAARg0ACyAIIA5qIAdqIQgMAgsgAyAIIAlqIgAgAyAASxsgA0HQgMAAEFsACyABIAUgCGoiCDYCFAtBACEHIAYNAQsgASAHNgIcIAchCwsgCCANaiIHIANJDQALCyABIAM2AhQgAEEANgIADwsgACAINgIEIABBCGogAjYCACAAQQE2AgALqwQCBX8BfkEBIQMCQCABKAIYIgRBJyABQRxqKAIAKAIQIgURAQANAEECIQFBMCECAkACfgJAAkACQAJAAkACQAJAIAAoAgAiAA4oCAEBAQEBAQEBAgQBAQMBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBBQALIABB3ABGDQQLIAAQK0UNBCAAQQFyZ0ECdkEHc61CgICAgNAAhAwFC0H0ACECDAULQfIAIQIMBAtB7gAhAgwDCyAAIQIMAgsgABA7BEBBASEBIAAhAgwCCyAAQQFyZ0ECdkEHc61CgICAgNAAhAshB0EDIQEgACECCwNAIAEhBkEAIQEgAiEAAkACQAJAAkACQCAGQQFrDgMEAgABCwJAAkACQAJAAkAgB0IgiKdB/wFxQQFrDgUABAECAwULIAdC/////49ggyEHQf0AIQBBAyEBDAcLIAdC/////49gg0KAgICAIIQhB0H7ACEAQQMhAQwGCyAHQv////+PYINCgICAgDCEIQdB9QAhAEEDIQEMBQsgB0L/////j2CDQoCAgIDAAIQhB0HcACEAQQMhAQwEC0EwQdcAIAIgB6ciAUECdHZBD3EiAEEKSRsgAGohACABRQ0CIAdCf3xC/////w+DIAdCgICAgHCDhCEHQQMhAQwDCyAEQScgBREBACEDDAQLQdwAIQBBASEBDAELIAdC/////49gg0KAgICAEIQhB0EDIQELIAQgACAFEQEARQ0ACwsgAwu7AwEGfyMAQRBrIgkkACAAQQRqKAIAIgYgACgCACIIIAGnIgpxIgdqKQAAQoCBgoSIkKDAgH+DIgFQBEBBCCEFA0AgBSAHaiEHIAVBCGohBSAGIAcgCHEiB2opAABCgIGChIiQoMCAf4MiAVANAAsLAkAgACgCCCAGIAF6p0EDdiAHaiAIcSIFaiwAACIHQX9KBH8gBiAGKQMAQoCBgoSIkKDAgH+DeqdBA3YiBWotAAAFIAcLQQFxIgdFcg0AIAlBCGogAEEBIAQQDCAAQQRqKAIAIgYgACgCACIIIApxIgRqKQAAQoCBgoSIkKDAgH+DIgFQBEBBCCEFA0AgBCAFaiEEIAVBCGohBSAGIAQgCHEiBGopAABCgIGChIiQoMCAf4MiAVANAAsLIAYgAXqnQQN2IARqIAhxIgVqLAAAQX9MDQAgBikDAEKAgYKEiJCgwIB/g3qnQQN2IQULIAUgBmogCkEZdiIEOgAAIAVBeGogCHEgBmpBCGogBDoAACAAIAAoAgggB2s2AgggACAAKAIMQQFqNgIMIAYgBUEDdGsiAEF4aiACNgIAIABBfGogAzYCACAJQRBqJAALgwMBA38CQAJAAkACQCABQQlPBEBBEEEIEJcBIAFLDQEMAgsgABALIQMMAgtBEEEIEJcBIQELQYCAfEEIQQgQlwFBFEEIEJcBakEQQQgQlwFqa0F3cUF9aiIEQQBBEEEIEJcBQQJ0ayICIAIgBEsbIAFrIABNDQAgAUEQIABBBGpBEEEIEJcBQXtqIABLG0EIEJcBIgRqQRBBCBCXAWpBfGoQCyICRQ0AIAIQvQEhAAJAIAFBf2oiAyACcUUEQCAAIQEMAQsgAiADakEAIAFrcRC9ASECQRBBCBCXASEDIAAQrwEgAkEAIAEgAiAAayADSxtqIgEgAGsiAmshAyAAEKUBRQRAIAEgAxCCASAAIAIQggEgACACECEMAQsgACgCACEAIAEgAzYCBCABIAAgAmo2AgALIAEQpQENASABEK8BIgJBEEEIEJcBIARqTQ0BIAEgBBC6ASEAIAEgBBCCASAAIAIgBGsiBBCCASAAIAQQIQwBCyADDwsgARC8ASABEKUBGgv3AgEEfyMAQRBrIgMkACAAIAFHBEAgAkEIaiEEA0AgAEEEagJAAn8CQAJAIAAoAgAiAEGAAU8EQCADQQA2AgwgAEGAEEkNASAAQYCABE8NAiADIABBP3FBgAFyOgAOIAMgAEEMdkHgAXI6AAwgAyAAQQZ2QT9xQYABcjoADUEDDAMLIAQoAgAiBSACQQRqKAIARgR/IAIgBRBAIAQoAgAFIAULIAIoAgBqIAA6AAAgBCAEKAIAQQFqNgIADAMLIAMgAEE/cUGAAXI6AA0gAyAAQQZ2QcABcjoADEECDAELIAMgAEE/cUGAAXI6AA8gAyAAQQZ2QT9xQYABcjoADiADIABBDHZBP3FBgAFyOgANIAMgAEESdkEHcUHwAXI6AAxBBAshACACQQRqKAIAIAQoAgAiBWsgAEkEQCACIAUgABBBIAQoAgAhBQsgAigCACAFaiADQQxqIAAQuQEaIAQgACAFajYCAAsiACABRw0ACwsgA0EQaiQAC9QCAQd/QQEhCQJAAkAgAkUNACABIAJBAXRqIQogAEGA/gNxQQh2IQsgAEH/AXEhDQJAA0AgAUECaiEMIAcgAS0AASICaiEIIAsgAS0AACIBRwRAIAEgC0sNAyAIIQcgDCIBIApHDQEMAwsgCCAHTwRAIAggBEsNAiADIAdqIQECQANAIAJFDQEgAkF/aiECIAEtAAAgAUEBaiEBIA1HDQALQQAhCQwFCyAIIQcgDCIBIApHDQEMAwsLIAcgCBC2AQALIAggBBC1AQALIAZFDQAgBSAGaiEDIABB//8DcSEBA0ACQCAFQQFqIQACfyAAIAUtAAAiAkEYdEEYdSIEQQBODQAaIAAgA0YNASAFLQABIARB/wBxQQh0ciECIAVBAmoLIQUgASACayIBQQBIDQIgCUEBcyEJIAMgBUcNAQwCCwtB0JPAAEGMm8AAEG8ACyAJQQFxC+ICAQN/IwBBEGsiAiQAIAAoAgAhAAJAAn8CQAJAIAFBgAFPBEAgAkEANgIMIAFBgBBJDQEgAUGAgARPDQIgAiABQT9xQYABcjoADiACIAFBDHZB4AFyOgAMIAIgAUEGdkE/cUGAAXI6AA1BAwwDCyAAKAIIIgMgAEEEaigCAEYEfyAAIAMQQCAAKAIIBSADCyAAKAIAaiABOgAAIAAgACgCCEEBajYCCAwDCyACIAFBP3FBgAFyOgANIAIgAUEGdkHAAXI6AAxBAgwBCyACIAFBP3FBgAFyOgAPIAIgAUEGdkE/cUGAAXI6AA4gAiABQQx2QT9xQYABcjoADSACIAFBEnZBB3FB8AFyOgAMQQQLIQEgAEEEaigCACAAQQhqIgQoAgAiA2sgAUkEQCAAIAMgARBBIAQoAgAhAwsgACgCACADaiACQQxqIAEQuQEaIAQgASADajYCAAsgAkEQaiQAQQAL4QIBBX8gAEELdCEEQSAhAkEgIQMCQANAAkACQCACQQF2IAFqIgJBAnRB6KbAAGooAgBBC3QiBSAETwRAIAQgBUYNAiACIQMMAQsgAkEBaiEBCyADIAFrIQIgAyABSw0BDAILCyACQQFqIQELAkACQCABQR9NBEAgAUECdCEEQcMFIQMgAUEfRwRAIARB7KbAAGooAgBBFXYhAwtBACEFIAFBf2oiAiABTQRAIAJBIE8NAiACQQJ0QeimwABqKAIAQf///wBxIQULAkAgAyAEQeimwABqKAIAQRV2IgFBf3NqRQ0AIAAgBWshBCABQcMFIAFBwwVLGyECIANBf2ohAEEAIQMDQCABIAJGDQQgAyABQeinwABqLQAAaiIDIARLDQEgACABQQFqIgFHDQALIAAhAQsgAUEBcQ8LIAFBIEGwpsAAEFsACyACQSBB0KbAABBbAAsgAkHDBUHApsAAEFsAC90CAQV/IABBC3QhBEEEIQJBBCEDAkADQAJAAkAgAkEBdiABaiICQQJ0QaytwABqKAIAQQt0IgUgBE8EQCAEIAVGDQIgAiEDDAELIAJBAWohAQsgAyABayECIAMgAUsNAQwCCwsgAkEBaiEBCwJAAkAgAUEDTQRAIAFBAnQhBEEVIQMgAUEDRwRAIARBsK3AAGooAgBBFXYhAwtBACEFIAFBf2oiAiABTQRAIAJBBE8NAiACQQJ0QaytwABqKAIAQf///wBxIQULAkAgAyAEQaytwABqKAIAQRV2IgFBf3NqRQ0AIAAgBWshBCABQRUgAUEVSxshAiADQX9qIQBBACEDA0AgASACRg0EIAMgAUG8rcAAai0AAGoiAyAESw0BIAAgAUEBaiIBRw0ACyAAIQELIAFBAXEPCyABQQRBsKbAABBbAAsgAkEEQdCmwAAQWwALIAJBFUHApsAAEFsAC9sCAQN/IwBBEGsiAiQAAkACfwJAAkAgAUGAAU8EQCACQQA2AgwgAUGAEEkNASABQYCABE8NAiACIAFBP3FBgAFyOgAOIAIgAUEMdkHgAXI6AAwgAiABQQZ2QT9xQYABcjoADUEDDAMLIAAoAggiAyAAQQRqKAIARgR/IAAgAxBAIAAoAggFIAMLIAAoAgBqIAE6AAAgACAAKAIIQQFqNgIIDAMLIAIgAUE/cUGAAXI6AA0gAiABQQZ2QcABcjoADEECDAELIAIgAUE/cUGAAXI6AA8gAiABQQZ2QT9xQYABcjoADiACIAFBDHZBP3FBgAFyOgANIAIgAUESdkEHcUHwAXI6AAxBBAshASAAQQRqKAIAIABBCGoiBCgCACIDayABSQRAIAAgAyABEEEgBCgCACEDCyAAKAIAIANqIAJBDGogARC5ARogBCABIANqNgIACyACQRBqJABBAAvVAgEDfyMAQRBrIgIkAAJAAn8CQCABQYABTwRAIAJBADYCDCABQYAQTw0BIAIgAUE/cUGAAXI6AA0gAiABQQZ2QcABcjoADEECDAILIAAoAggiAyAAQQRqKAIARgRAIAAgAxBDIAAoAgghAwsgACADQQFqNgIIIAAoAgAgA2ogAToAAAwCCyABQYCABE8EQCACIAFBP3FBgAFyOgAPIAIgAUEGdkE/cUGAAXI6AA4gAiABQQx2QT9xQYABcjoADSACIAFBEnZBB3FB8AFyOgAMQQQMAQsgAiABQT9xQYABcjoADiACIAFBDHZB4AFyOgAMIAIgAUEGdkE/cUGAAXI6AA1BAwshASAAQQRqKAIAIABBCGoiBCgCACIDayABSQRAIAAgAyABEEIgBCgCACEDCyAAKAIAIANqIAJBDGogARC5ARogBCABIANqNgIACyACQRBqJAAL1wIBA38jAEEQayICJAACQAJ/AkACQCABQYABTwRAIAJBADYCDCABQYAQSQ0BIAFBgIAETw0CIAIgAUE/cUGAAXI6AA4gAiABQQx2QeABcjoADCACIAFBBnZBP3FBgAFyOgANQQMMAwsgACgCCCIDIABBBGooAgBGBEAgACADEEMgACgCCCEDCyAAIANBAWo2AgggACgCACADaiABOgAADAMLIAIgAUE/cUGAAXI6AA0gAiABQQZ2QcABcjoADEECDAELIAIgAUE/cUGAAXI6AA8gAiABQQZ2QT9xQYABcjoADiACIAFBDHZBP3FBgAFyOgANIAIgAUESdkEHcUHwAXI6AAxBBAshASAAQQRqKAIAIABBCGoiBCgCACIDayABSQRAIAAgAyABEEIgBCgCACEDCyAAKAIAIANqIAJBDGogARC5ARogBCABIANqNgIACyACQRBqJAALtgIBB38CQCACQQ9NBEAgACEDDAELIABBACAAa0EDcSIEaiEFIAQEQCAAIQMgASEGA0AgAyAGLQAAOgAAIAZBAWohBiADQQFqIgMgBUkNAAsLIAUgAiAEayIIQXxxIgdqIQMCQCABIARqIgRBA3EEQCAHQQFIDQEgBEEDdCICQRhxIQkgBEF8cSIGQQRqIQFBACACa0EYcSECIAYoAgAhBgNAIAUgBiAJdiABKAIAIgYgAnRyNgIAIAFBBGohASAFQQRqIgUgA0kNAAsMAQsgB0EBSA0AIAQhAQNAIAUgASgCADYCACABQQRqIQEgBUEEaiIFIANJDQALCyAIQQNxIQIgBCAHaiEBCyACBEAgAiADaiECA0AgAyABLQAAOgAAIAFBAWohASADQQFqIgMgAkkNAAsLIAALvgIBBX8CQAJAAkACQCACQQNqQXxxIAJrIgRFDQAgAyAEIAQgA0sbIgRFDQAgAUH/AXEhB0EBIQYDQCACIAVqLQAAIAdGDQQgBCAFQQFqIgVHDQALIAQgA0F4aiIGSw0CDAELIANBeGohBkEAIQQLIAFB/wFxQYGChAhsIQUDQCACIARqIgcoAgAgBXMiCEF/cyAIQf/9+3dqcSAHQQRqKAIAIAVzIgdBf3MgB0H//ft3anFyQYCBgoR4cUUEQCAEQQhqIgQgBk0NAQsLIAQgA00NACAEIAMQtAEACwJAIAMgBEYNACAEIANrIQMgAiAEaiECQQAhBSABQf8BcSEBA0AgASACIAVqLQAARwRAIAMgBUEBaiIFag0BDAILCyAEIAVqIQVBASEGDAELQQAhBgsgACAFNgIEIAAgBjYCAAu+AgIFfwF+IwBBMGsiBCQAQSchAgJAIABCkM4AVARAIAAhBwwBCwNAIARBCWogAmoiA0F8aiAAIABCkM4AgCIHQpDOAH59pyIFQf//A3FB5ABuIgZBAXRBpZTAAGovAAA7AAAgA0F+aiAFIAZB5ABsa0H//wNxQQF0QaWUwABqLwAAOwAAIAJBfGohAiAAQv/B1y9WIAchAA0ACwsgB6ciA0HjAEsEQCACQX5qIgIgBEEJamogB6ciAyADQf//A3FB5ABuIgNB5ABsa0H//wNxQQF0QaWUwABqLwAAOwAACwJAIANBCk8EQCACQX5qIgIgBEEJamogA0EBdEGllMAAai8AADsAAAwBCyACQX9qIgIgBEEJamogA0EwajoAAAsgAUGAk8AAQQAgBEEJaiACakEnIAJrEBcgBEEwaiQAC7ECAQN/IwBBgAFrIgQkAAJAAkACQAJAIAEoAgAiAkEQcUUEQCACQSBxDQEgADUCACABEDIhAAwECyAAKAIAIQBBACECA0AgAiAEakH/AGpBMEHXACAAQQ9xIgNBCkkbIANqOgAAIAJBf2ohAiAAQQ9LIABBBHYhAA0ACyACQYABaiIAQYEBTw0BIAFBo5TAAEECIAIgBGpBgAFqQQAgAmsQFyEADAMLIAAoAgAhAEEAIQIDQCACIARqQf8AakEwQTcgAEEPcSIDQQpJGyADajoAACACQX9qIQIgAEEPSyAAQQR2IQANAAsgAkGAAWoiAEGBAU8NASABQaOUwABBAiACIARqQYABakEAIAJrEBchAAwCCyAAQYABELQBAAsgAEGAARC0AQALIARBgAFqJAAgAAunAgEFfyAAQgA3AhAgAAJ/QQAgAUGAAkkNABpBHyABQf///wdLDQAaIAFBBiABQQh2ZyICa3ZBAXEgAkEBdGtBPmoLIgI2AhwgAkECdEG4sMAAaiEDIAAhBAJAAkACQAJAQayuwAAoAgAiBUEBIAJ0IgZxBEAgAygCACEDIAIQkwEhAiADEK8BIAFHDQEgAyECDAILQayuwAAgBSAGcjYCACADIAA2AgAMAwsgASACdCEFA0AgAyAFQR12QQRxakEQaiIGKAIAIgJFDQIgBUEBdCEFIAIiAxCvASABRw0ACwsgAigCCCIBIAQ2AgwgAiAENgIIIAQgAjYCDCAEIAE2AgggAEEANgIYDwsgBiAANgIACyAAIAM2AhggBCAENgIIIAQgBDYCDAu2AgEFfyAAKAIYIQQCQAJAIAAgACgCDEYEQCAAQRRBECAAQRRqIgEoAgAiAxtqKAIAIgINAUEAIQEMAgsgACgCCCICIAAoAgwiATYCDCABIAI2AggMAQsgASAAQRBqIAMbIQMDQCADIQUgAiIBQRRqIgMoAgAiAkUEQCABQRBqIQMgASgCECECCyACDQALIAVBADYCAAsCQCAERQ0AAkAgACAAKAIcQQJ0QbiwwABqIgIoAgBHBEAgBEEQQRQgBCgCECAARhtqIAE2AgAgAQ0BDAILIAIgATYCACABDQBBrK7AAEGsrsAAKAIAQX4gACgCHHdxNgIADwsgASAENgIYIAAoAhAiAgRAIAEgAjYCECACIAE2AhgLIABBFGooAgAiAEUNACABQRRqIAA2AgAgACABNgIYCwvAAgEBfyMAQTBrIgIkAAJ/AkACQAJAAkAgACgCAEEBaw4DAQIDAAsgAkEcakEBNgIAIAJCATcCDCACQYSKwAA2AgggAkEKNgIkIAIgAEEEajYCLCACIAJBIGo2AhggAiACQSxqNgIgIAEgAkEIahBcDAMLIAJBHGpBADYCACACQfCIwAA2AhggAkIBNwIMIAJB5InAADYCCCABIAJBCGoQXAwCCyACQRxqQQE2AgAgAkIBNwIMIAJBwInAADYCCCACQQo2AiQgAiAAQQRqNgIsIAIgAkEgajYCGCACIAJBLGo2AiAgASACQQhqEFwMAQsgAkEcakEBNgIAIAJCATcCDCACQaCJwAA2AgggAkEKNgIkIAIgAEEEajYCLCACIAJBIGo2AhggAiACQSxqNgIgIAEgAkEIahBcCyACQTBqJAALbwEMf0HYscAAKAIAIgJFBEBB6LHAAEH/HzYCAEEADwtB0LHAACEGA0AgAiIBKAIIIQIgASgCBCEDIAEoAgAhBCABQQxqKAIAGiABIQYgBUEBaiEFIAINAAtB6LHAACAFQf8fIAVB/x9LGzYCACAIC4sCAgR/AX4jAEEwayICJAAgAUEEaiEEIAEoAgRFBEAgASgCACEDIAJBEGoiBUEANgIAIAJCATcDCCACIAJBCGo2AhQgAkEoaiADQRBqKQIANwMAIAJBIGogA0EIaikCADcDACACIAMpAgA3AxggAkEUakGAjsAAIAJBGGoQHhogBEEIaiAFKAIANgIAIAQgAikDCDcCAAsgAkEgaiIDIARBCGooAgA2AgAgAUEMakEANgIAIAQpAgAhBiABQgE3AgQgAiAGNwMYQQxBBBCeASIBRQRAQQxBBBCzAQALIAEgAikDGDcCACABQQhqIAMoAgA2AgAgAEHoj8AANgIEIAAgATYCACACQTBqJAAL7AEBAn8jAEEwayIFJAACQCABBEAgASgCACIGQX9GDQEgASAGQQFqNgIAIAUgBDYCFCAFQRhqIAFBBGogAiADIAVBFGoQGyAFQRBqIAVBKGooAgA2AgAgBSAFQSBqKQMANwMIIAUoAhwhBCAFKAIYIQYgAwRAIAIQFQsgASABKAIAQX9qNgIAAn8gBkUEQEEAIQNBAAwBCyAFQSRqIAVBEGooAgA2AgAgBSAENgIYIAUgBSkDCDcCHEEBIQMgBUEYahBNCyEBIAAgAzYCCCAAIAE2AgQgACAENgIAIAVBMGokAA8LEK0BAAsQrgEAC4UCAQN/IwBBIGsiAiQAIAJB8IbAAEEGQfaGwABBJxAGNgIUIAJBITYCGCACQQhqIAJBFGogAkEYahBtIAIoAgwhAyACKAIIRQRAIAIoAhgiBEEkTwRAIAQQAAsgACADNgIAIAAgASkCADcCBCAAQTRqIAFBMGooAgA2AgAgAEEsaiABQShqKQIANwIAIABBJGogAUEgaikCADcCACAAQRxqIAFBGGopAgA3AgAgAEEUaiABQRBqKQIANwIAIABBDGogAUEIaikCADcCACACKAIUIgBBJE8EQCAAEAALIAJBIGokAA8LIAIgAzYCHEGrgcAAQSsgAkEcakHogcAAQaCHwAAQUgAL1gEAAkAgAEEgSQ0AAkACf0EBIABB/wBJDQAaIABBgIAESQ0BAkAgAEGAgAhPBEAgAEG12XNqQbXbK0kgAEHii3RqQeILSXINBCAAQZ+odGpBnxhJIABB3uJ0akEOSXINBCAAQX5xQZ7wCkYNBCAAQWBxQeDNCkcNAQwECyAAQbugwABBKkGPocAAQcABQc+iwABBtgMQKQ8LQQAgAEHHkXVqQQdJDQAaIABBgIC8f2pB8IN0SQsPCyAAQZybwABBKEHsm8AAQaACQYyewABBrwIQKQ8LQQALwwEBA38gACgCBCIDIAAoAgBGBEBBgIDEAA8LIAAgA0F/aiIBNgIEIAEtAAAiAUEYdEEYdSICQX9MBH8gACADQX5qIgE2AgQgAkE/cQJ/IAEtAAAiAUEYdEEYdSICQUBOBEAgAUEfcQwBCyAAIANBfWoiATYCBCACQT9xAn8gAS0AACIBQRh0QRh1IgJBQE4EQCABQQ9xDAELIAAgA0F8aiIANgIEIAJBP3EgAC0AAEEHcUEGdHILQQZ0cgtBBnRyBSABCwvTAQEFfyMAQSBrIgIkAAJAIAFBAWoiAyABSQ0AQQQhBCAAQQRqKAIAIgVBAXQiASADIAEgA0sbIgFBBCABQQRLGyIBQf////8AcSABRkECdCEDIAFBBHQhBgJAIAVFBEBBACEEDAELIAIgBUEEdDYCFCACIAAoAgA2AhALIAIgBDYCGCACIAYgAyACQRBqEEsgAigCAARAIAJBCGooAgAiAEUNASACKAIEIAAQswEACyACKAIEIQMgAEEEaiABNgIAIAAgAzYCACACQSBqJAAPCxBzAAvTAQEFfyMAQSBrIgIkAAJAIAFBAWoiAyABSQ0AQQQhBCAAQQRqKAIAIgVBAXQiASADIAEgA0sbIgFBBCABQQRLGyIBQf////8DcSABRkECdCEDIAFBAnQhBgJAIAVFBEBBACEEDAELIAIgBUECdDYCFCACIAAoAgA2AhALIAIgBDYCGCACIAYgAyACQRBqEEsgAigCAARAIAJBCGooAgAiAEUNASACKAIEIAAQswEACyACKAIEIQMgAEEEaiABNgIAIAAgAzYCACACQSBqJAAPCxBzAAu3AQEEfyAAKAIAIgEgACgCBEYEQEGAgMQADwsgACABQQFqNgIAIAEtAAAiA0EYdEEYdUF/TAR/IAAgAUECajYCACABLQABQT9xIQIgA0EfcSEEIANB3wFNBEAgBEEGdCACcg8LIAAgAUEDajYCACABLQACQT9xIAJBBnRyIQIgA0HwAUkEQCACIARBDHRyDwsgACABQQRqNgIAIARBEnRBgIDwAHEgAS0AA0E/cSACQQZ0cnIFIAMLC68BAQN/IwBBIGsiAiQAAkAgAUEBaiIDIAFJDQAgAEEEaigCACIBQQF0IgQgAyAEIANLGyIDQQggA0EISxshAyACIAEEfyACIAE2AhQgAiAAKAIANgIQQQEFQQALNgIYIAIgA0EBIAJBEGoQSyACKAIABEAgAkEIaigCACIARQ0BIAIoAgQgABCzAQALIAIoAgQhASAAQQRqIAM2AgAgACABNgIAIAJBIGokAA8LEHMAC68BAQJ/IwBBIGsiAyQAAkAgASACaiICIAFJDQAgAEEEaigCACIBQQF0IgQgAiAEIAJLGyICQQggAkEISxshBCADIAEEfyADIAE2AhQgAyAAKAIANgIQQQEFQQALNgIYIAMgBEEBIANBEGoQSyADKAIABEAgA0EIaigCACIARQ0BIAMoAgQgABCzAQALIAMoAgQhASAAQQRqIAQ2AgAgACABNgIAIANBIGokAA8LEHMAC60BAQJ/IwBBIGsiAyQAAkAgASACaiICIAFJDQAgAEEEaigCACIBQQF0IgQgAiAEIAJLGyICQQggAkEISxshBCADIAEEfyADIAE2AhQgAyAAKAIANgIQQQEFQQALNgIYIAMgBCADQRBqEEogAygCAARAIANBCGooAgAiAEUNASADKAIEIAAQswEACyADKAIEIQEgAEEEaiAENgIAIAAgATYCACADQSBqJAAPCxBzAAutAQEDfyMAQSBrIgIkAAJAIAFBAWoiAyABSQ0AIABBBGooAgAiAUEBdCIEIAMgBCADSxsiA0EIIANBCEsbIQMgAiABBH8gAiABNgIUIAIgACgCADYCEEEBBUEACzYCGCACIAMgAkEQahBKIAIoAgAEQCACQQhqKAIAIgBFDQEgAigCBCAAELMBAAsgAigCBCEBIABBBGogAzYCACAAIAE2AgAgAkEgaiQADwsQcwAL7wEBA38jAEEgayIFJABBjK7AAEGMrsAAKAIAIgdBAWo2AgBB7LHAAEHsscAAKAIAQQFqIgY2AgACQAJAIAdBAEggBkECS3INACAFIAQ6ABggBSADNgIUIAUgAjYCEEGArsAAKAIAIgJBf0wNAEGArsAAIAJBAWoiAjYCAEGArsAAQYiuwAAoAgAiAwR/QYSuwAAoAgAgBSAAIAEoAhARAAAgBSAFKQMANwMIIAVBCGogAygCFBEAAEGArsAAKAIABSACC0F/ajYCACAGQQFLDQAgBA0BCwALIwBBEGsiAiQAIAIgATYCDCACIAA2AggAC58BAQN/AkAgAUEPTQRAIAAhAgwBCyAAQQAgAGtBA3EiBGohAyAEBEAgACECA0AgAkH/AToAACACQQFqIgIgA0kNAAsLIAMgASAEayIBQXxxIgRqIQIgBEEBTgRAA0AgA0F/NgIAIANBBGoiAyACSQ0ACwsgAUEDcSEBCyABBEAgASACaiEBA0AgAkH/AToAACACQQFqIgIgAUkNAAsLIAALrAEBA38jAEEQayIDJAACQAJAIAEEQCABKAIAIgJBf0YNASABIAJBAWo2AgAgAyABQQRqEGEgASABKAIAQX9qNgIAIAMoAgAhAQJAIAMoAgQiAiADKAIIIgRNBEAgASECDAELIARFBEBBASECIAEQFQwBCyABIAJBASAEEJkBIgJFDQMLIAAgBDYCBCAAIAI2AgAgA0EQaiQADwsQrQEACxCuAQALIARBARCzAQALrAEBA38jAEEQayIDJAACQAJAIAEEQCABKAIAIgJBf0YNASABIAJBAWo2AgAgAyABQRBqEGEgASABKAIAQX9qNgIAIAMoAgAhAQJAIAMoAgQiAiADKAIIIgRNBEAgASECDAELIARFBEBBASECIAEQFQwBCyABIAJBASAEEJkBIgJFDQMLIAAgBDYCBCAAIAI2AgAgA0EQaiQADwsQrQEACxCuAQALIARBARCzAQALrAEBA38jAEEQayIDJAACQAJAIAEEQCABKAIAIgJBf0YNASABIAJBAWo2AgAgAyABQSxqEGEgASABKAIAQX9qNgIAIAMoAgAhAQJAIAMoAgQiAiADKAIIIgRNBEAgASECDAELIARFBEBBASECIAEQFQwBCyABIAJBASAEEJkBIgJFDQMLIAAgBDYCBCAAIAI2AgAgA0EQaiQADwsQrQEACxCuAQALIARBARCzAQALrAEBA38jAEEwayICJAAgAUEEaiEDIAEoAgRFBEAgASgCACEBIAJBEGoiBEEANgIAIAJCATcDCCACIAJBCGo2AhQgAkEoaiABQRBqKQIANwMAIAJBIGogAUEIaikCADcDACACIAEpAgA3AxggAkEUakGAjsAAIAJBGGoQHhogA0EIaiAEKAIANgIAIAMgAikDCDcCAAsgAEHoj8AANgIEIAAgAzYCACACQTBqJAALkAEBAn8CQAJ/AkACQAJAAn9BASIDIAFBAEgNABogAigCCEUNAiACKAIEIgQNASABDQNBAQwECyEDQQAhAQwECyACKAIAIARBASABEJkBDAILIAENAEEBDAELIAFBARCeAQsiAgRAIAAgAjYCBEEAIQMMAQsgACABNgIEQQEhAQsgACADNgIAIABBCGogATYCAAunAQECfwJAAkACQAJAAkACQAJAAn8gAgRAQQEiBCABQQBIDQEaIAMoAghFDQMgAygCBCIFDQIgAQ0EDAYLIAAgATYCBEEBCyEEQQAhAQwGCyADKAIAIAUgAiABEJkBIgNFDQIMBAsgAUUNAgsgASACEJ4BIgMNAgsgACABNgIEIAIhAQwCCyACIQMLIAAgAzYCBEEAIQQLIAAgBDYCACAAQQhqIAE2AgALlwEBAX8jAEEQayIGJAAgAQRAIAYgASADIAQgBSACKAIQEQYAIAYoAgAhAQJAIAYoAgQiAyAGKAIIIgJNBEAgASEDDAELIANBAnQhAyACQQJ0IgQEQCABIANBBCAEEJkBIgMNASAEQQQQswEAC0EEIQMgARAVCyAAIAI2AgQgACADNgIAIAZBEGokAA8LQciMwABBMBCsAQALjAEBAn8jAEFAaiIBJAAgAUEANgIIIAFCATcDACABQRBqIAEQfCAAIAFBEGoQNkUEQCABKAIAIAEoAggQBCABKAIEBEAgASgCABAVCwJAIAAoAgBBAUYNACAAQQhqKAIARQ0AIAAoAgQQFQsgAUFAayQADwtB3IfAAEE3IAFBOGpB8IjAAEHgiMAAEFIAC5YBAQF/IwBBQGoiAiQAIAAoAgAhACACQgA3AzggAkE4aiAAEAkgAkEcakEBNgIAIAIgAigCPCIANgIwIAIgADYCLCACIAIoAjg2AiggAkEiNgIkIAJCAjcCDCACQYSNwAA2AgggAiACQShqNgIgIAIgAkEgajYCGCABIAJBCGoQXCACKAIsBEAgAigCKBAVCyACQUBrJAALewEHfwJAIAAEQCAAKAIADQEgAEEANgIAIAAoAgghAiAAKAIMIAAoAhQhBCAAKAIYIQUgACgCMCEGIAAoAjQhByAAKAIEIQEgABAVIAFBJE8EQCABEAALBEAgAhAVCyAFBEAgBBAVCyAHBEAgBhAVCw8LEK0BAAsQrgEAC54BAQJ/IwBBEGsiAyQAIABBFGooAgAhBAJAAn8CQAJAIABBBGooAgAOAgABAwsgBA0CQQAhAEGYjsAADAELIAQNASAAKAIAIgQoAgQhACAEKAIACyEEIAMgADYCBCADIAQ2AgAgA0GckMAAIAEoAgggAiABLQAQEEQACyADQQA2AgQgAyAANgIAIANBiJDAACABKAIIIAIgAS0AEBBEAAtoAQZ/AkAgAARAIAAoAgANASAAQQA2AgAgACgCBCEBIAAoAgggACgCECEDIAAoAhQhBCAAKAIsIQUgACgCMCEGIAAQFQRAIAEQFQsgBARAIAMQFQsgBgRAIAUQFQsPCxCtAQALEK4BAAt9AQF/IwBBQGoiBSQAIAUgATYCDCAFIAA2AgggBSADNgIUIAUgAjYCECAFQSxqQQI2AgAgBUE8akE4NgIAIAVCAjcCHCAFQZCUwAA2AhggBUE0NgI0IAUgBUEwajYCKCAFIAVBEGo2AjggBSAFQQhqNgIwIAVBGGogBBB0AAt8AQF/IAAtAAQhASAALQAFBEAgAUH/AXEhASAAAn9BASABDQAaIAAoAgAiAS0AAEEEcUUEQCABKAIYQaGUwABBAiABQRxqKAIAKAIMEQMADAELIAEoAhhBoJTAAEEBIAFBHGooAgAoAgwRAwALIgE6AAQLIAFB/wFxQQBHC10CAX8BfiMAQRBrIgAkAEGQrsAAKQMAUARAIABCAjcDCCAAQgE3AwAgACkDACEBQaCuwAAgACkDCDcDAEGYrsAAIAE3AwBBkK7AAEIBNwMACyAAQRBqJABBmK7AAAt9AQF/QThBBBCeASIKRQRAQThBBBCzAQALIAogCTYCNCAKIAk2AjAgCiAINgIsIAogBzYCKCAKIAY2AiQgCiAFNgIgIAogBDYCHCAKIAM2AhggCiADNgIUIAogAjYCECAKIAE2AgwgCiABNgIIIAogADYCBCAKQQA2AgAgCgt8AQN/IAAgABC8ASIAQQgQlwEgAGsiAhC6ASEAQbyxwAAgASACayIBNgIAQcSxwAAgADYCACAAIAFBAXI2AgRBCEEIEJcBIQJBFEEIEJcBIQNBEEEIEJcBIQQgACABELoBIAQgAyACQQhramo2AgRB4LHAAEGAgIABNgIAC28BBH8jAEEgayICJABBASEDAkAgACABEDMNACABQRxqKAIAIQQgASgCGCACQRxqQQA2AgAgAkGAk8AANgIYIAJCATcCDCACQYSTwAA2AgggBCACQQhqEB4NACAAQQRqIAEQMyEDCyACQSBqJAAgAwtvAQF/IwBBMGsiAiQAIAIgATYCBCACIAA2AgAgAkEcakECNgIAIAJBLGpBAzYCACACQgI3AgwgAkGklsAANgIIIAJBAzYCJCACIAJBIGo2AhggAiACQQRqNgIoIAIgAjYCICACQQhqQdSWwAAQdAALbwEBfyMAQTBrIgIkACACIAE2AgQgAiAANgIAIAJBHGpBAjYCACACQSxqQQM2AgAgAkICNwIMIAJBuJfAADYCCCACQQM2AiQgAiACQSBqNgIYIAIgAkEEajYCKCACIAI2AiAgAkEIakHIl8AAEHQAC28BAX8jAEEwayICJAAgAiABNgIEIAIgADYCACACQRxqQQI2AgAgAkEsakEDNgIAIAJCAjcCDCACQfSWwAA2AgggAkEDNgIkIAIgAkEgajYCGCACIAJBBGo2AiggAiACNgIgIAJBCGpBhJfAABB0AAtsAQF/IwBBMGsiAyQAIAMgATYCBCADIAA2AgAgA0EcakECNgIAIANBLGpBAzYCACADQgI3AgwgA0HAk8AANgIIIANBAzYCJCADIANBIGo2AhggAyADNgIoIAMgA0EEajYCICADQQhqIAIQdAALVgECfyMAQSBrIgIkACAAQRxqKAIAIQMgACgCGCACQRhqIAFBEGopAgA3AwAgAkEQaiABQQhqKQIANwMAIAIgASkCADcDCCADIAJBCGoQHiACQSBqJAALWQEBfyMAQSBrIgIkACACIAAoAgA2AgQgAkEYaiABQRBqKQIANwMAIAJBEGogAUEIaikCADcDACACIAEpAgA3AwggAkEEakGYisAAIAJBCGoQHiACQSBqJAALWQEBfyMAQSBrIgIkACACIAAoAgA2AgQgAkEYaiABQRBqKQIANwMAIAJBEGogAUEIaikCADcDACACIAEpAgA3AwggAkEEakGAjsAAIAJBCGoQHiACQSBqJAALZwAjAEEwayIBJABB2K3AAC0AAARAIAFBHGpBATYCACABQgI3AgwgAUH0jsAANgIIIAFBAzYCJCABIAA2AiwgASABQSBqNgIYIAEgAUEsajYCICABQQhqQZyPwAAQdAALIAFBMGokAAtZAQF/IwBBIGsiAiQAIAIgACgCADYCBCACQRhqIAFBEGopAgA3AwAgAkEQaiABQQhqKQIANwMAIAIgASkCADcDCCACQQRqQbSRwAAgAkEIahAeIAJBIGokAAtnAQJ/IAEoAgAhAwJAAkACQCABQQhqKAIAIgFFBEBBASECDAELIAFBf0wNASABQQEQngEiAkUNAgsgAiADIAEQuQEhAiAAIAE2AgggACABNgIEIAAgAjYCAA8LEHMACyABQQEQswEAC1YBAX8jAEEgayICJAAgAiAANgIEIAJBGGogAUEQaikCADcDACACQRBqIAFBCGopAgA3AwAgAiABKQIANwMIIAJBBGpBmIrAACACQQhqEB4gAkEgaiQAC1YBAX8CQCAABEAgACgCAA0BIABBfzYCACAAQQhqIgMoAgAEQCAAKAIEEBULIAAgATYCBCAAQQA2AgAgAEEMaiACNgIAIAMgAjYCAA8LEK0BAAsQrgEAC1YBAX8CQCAABEAgACgCAA0BIABBfzYCACAAQRRqIgMoAgAEQCAAKAIQEBULIAAgATYCECAAQQA2AgAgAEEYaiACNgIAIAMgAjYCAA8LEK0BAAsQrgEAC1YBAX8CQCAABEAgACgCAA0BIABBfzYCACAAQTBqIgMoAgAEQCAAKAIsEBULIAAgATYCLCAAQQA2AgAgAEE0aiACNgIAIAMgAjYCAA8LEK0BAAsQrgEAC1YBAX8jAEEQayIFJAAgASgCACACKAIAIAMoAgAgBCgCABAIIQEgBUEIahCDASAFKAIMIQIgACAFKAIIIgNBAEc2AgAgACACIAEgAxs2AgQgBUEQaiQAC08BAn8gACgCACIDQQRqKAIAIANBCGoiBCgCACIAayACSQRAIAMgACACEEEgBCgCACEACyADKAIAIABqIAEgAhC5ARogBCAAIAJqNgIAQQALTwECfyAAKAIAIgNBBGooAgAgA0EIaiIEKAIAIgBrIAJJBEAgAyAAIAIQQiAEKAIAIQALIAMoAgAgAGogASACELkBGiAEIAAgAmo2AgBBAAtRAQF/IwBBEGsiBCQAIAEoAgAgAigCACADKAIAEAchASAEQQhqEIMBIAQoAgwhAiAAIAQoAggiA0EARzYCACAAIAIgASADGzYCBCAEQRBqJAALSgECfyAAQQRqKAIAIABBCGoiBCgCACIDayACSQRAIAAgAyACEEEgBCgCACEDCyAAKAIAIANqIAEgAhC5ARogBCACIANqNgIAQQALPwEBfyMAQSBrIgAkACAAQRxqQQA2AgAgAEGwkMAANgIYIABCATcCDCAAQcyQwAA2AgggAEEIakGkkcAAEHQAC0MBA38CQCACRQ0AA0AgAC0AACIEIAEtAAAiBUYEQCAAQQFqIQAgAUEBaiEBIAJBf2oiAg0BDAILCyAEIAVrIQMLIAMLTAECfyMAQRBrIgMkACABKAIAIAIoAgAQAyEBIANBCGoQgwEgAygCDCECIAAgAygCCCIEQQBHNgIAIAAgAiABIAQbNgIEIANBEGokAAtLAAJAAn8gAUGAgMQARwRAQQEgACgCGCABIABBHGooAgAoAhARAQANARoLIAINAUEACw8LIAAoAhggAiADIABBHGooAgAoAgwRAwALRwEBfyMAQSBrIgIkACACQRRqQQA2AgAgAkGAk8AANgIQIAJCATcCBCACQSs2AhwgAiAANgIYIAIgAkEYajYCACACIAEQdAALRgECfyABKAIEIQIgASgCACEDQQhBBBCeASIBRQRAQQhBBBCzAQALIAEgAjYCBCABIAM2AgAgAEH4j8AANgIEIAAgATYCAAs5AQF/IAFBEHZAACECIABBADYCCCAAQQAgAUGAgHxxIAJBf0YiARs2AgQgAEEAIAJBEHQgARs2AgALZAEDfyMAQRBrIgEkACAAKAIMIgJFBEBBmI7AAEHIj8AAEG8ACyAAKAIIIgNFBEBBmI7AAEHYj8AAEG8ACyABIAI2AgggASAANgIEIAEgAzYCACABKAIAIAEoAgQgASgCCBBQAAs/AQF/IwBBIGsiACQAIABBHGpBADYCACAAQcyRwAA2AhggAEIBNwIMIABBjJLAADYCCCAAQQhqQZSSwAAQdAALPgEBfyMAQSBrIgIkACACQQE6ABggAiABNgIUIAIgADYCECACQfyTwAA2AgwgAkGAk8AANgIIIAJBCGoQcgALKwACQCAAQXxLDQAgAEUEQEEEDwsgACAAQX1JQQJ0EJ4BIgBFDQAgAA8LAAsiACMAQRBrIgAkACAAQQhqIAEQfSAAQQhqEFMgAEEQaiQACysAAkAgAARAIAAoAgANASAAQQA2AgAgAEEcaiABNgIADwsQrQEACxCuAQALKwACQCAABEAgACgCAA0BIABBADYCACAAQSBqIAE2AgAPCxCtAQALEK4BAAsrAAJAIAAEQCAAKAIADQEgAEEANgIAIABBJGogATYCAA8LEK0BAAsQrgEACysAAkAgAARAIAAoAgANASAAQQA2AgAgAEEoaiABNgIADwsQrQEACxCuAQALQAEBfyMAQRBrIgQkACAEIAM2AgwgBCACNgIIIAQgATYCBCAEIAA2AgAgBCgCACAEKAIEIAQoAgggBCgCDBATAAs3ACAAQQM6ACAgAEKAgICAgAQ3AgAgACABNgIYIABBADYCECAAQQA2AgggAEEcakHEh8AANgIACzUBAX8gASgCGEHDjsAAQQsgAUEcaigCACgCDBEDACECIABBADoABSAAIAI6AAQgACABNgIACyUAAkAgAARAIAAoAgBBf0YNASAAQRxqKAIADwsQrQEACxCuAQALJQACQCAABEAgACgCAEF/Rg0BIABBIGooAgAPCxCtAQALEK4BAAslAAJAIAAEQCAAKAIAQX9GDQEgAEEkaigCAA8LEK0BAAsQrgEACyUAAkAgAARAIAAoAgBBf0YNASAAQShqKAIADwsQrQEACxCuAQALJwAgACAAKAIEQQFxIAFyQQJyNgIEIAAgAWoiACAAKAIEQQFyNgIECzoBAn9B3K3AAC0AACEBQdytwABBADoAAEHgrcAAKAIAIQJB4K3AAEEANgIAIAAgAjYCBCAAIAE2AgALIAEBfwJAIAAoAgQiAUUNACAAQQhqKAIARQ0AIAEQFQsLHwACQCABQXxNBEAgACABQQQgAhCZASIADQELAAsgAAsjACACIAIoAgRBfnE2AgQgACABQQFyNgIEIAAgAWogATYCAAslACAARQRAQciMwABBMBCsAQALIAAgAiADIAQgBSABKAIQEQoACyMAIABFBEBByIzAAEEwEKwBAAsgACACIAMgBCABKAIQEQgACyMAIABFBEBByIzAAEEwEKwBAAsgACACIAMgBCABKAIQEQcACyMAIABFBEBByIzAAEEwEKwBAAsgACACIAMgBCABKAIQERUACyMAIABFBEBByIzAAEEwEKwBAAsgACACIAMgBCABKAIQERIACyMAIABFBEBByIzAAEEwEKwBAAsgACACIAMgBCABKAIQERQACx4AIAAgAUEDcjYCBCAAIAFqIgAgACgCBEEBcjYCBAsUACAAQQRqKAIABEAgACgCABAVCwshACAARQRAQciMwABBMBCsAQALIAAgAiADIAEoAhARBAALHwAgAEUEQEHIjMAAQTAQrAEACyAAIAIgASgCEBEBAAsZAQF/IAAoAhAiAQR/IAEFIABBFGooAgALCxkAIAAoAgAiACgCACAAQQhqKAIAIAEQtwELEgBBAEEZIABBAXZrIABBH0YbCxYAIAAgAUEBcjYCBCAAIAFqIAE2AgALHAAgASgCGEHgpsAAQQUgAUEcaigCACgCDBEDAAsTACAAKAIAIgBBJE8EQCAAEAALCxAAIAAgAWpBf2pBACABa3ELFAAgACgCACAAQQhqKAIAIAEQtwELDAAgACABIAIgAxAYCwsAIAEEQCAAEBULCw8AIABBAXQiAEEAIABrcgsUACAAKAIAIAEgACgCBCgCDBEBAAsRACAAKAIAIAAoAgQgARC3AQsIACAAIAEQJwsWAEHgrcAAIAA2AgBB3K3AAEEBOgAACw0AIAAoAgAgARAuQQALEwAgAEH4j8AANgIEIAAgATYCAAsNACAALQAEQQJxQQF2CxAAIAEgACgCACAAKAIEEBYLCgBBACAAayAAcQsLACAALQAEQQNxRQsMACAAIAFBA3I2AgQLDQAgACgCACAAKAIEagsNACAAKAIAIAEQL0EACw4AIAAoAgAaA0AMAAsACwsAIAA1AgAgARAyCwsAIAAjAGokACMACwkAIAAgARAKAAsNAEGUjcAAQRsQrAEACw4AQa+NwABBzwAQrAEACwoAIAAoAgRBeHELCgAgACgCBEEBcQsKACAAKAIMQQFxCwoAIAAoAgxBAXYLGQAgACABQfytwAAoAgAiAEEjIAAbEQAAAAsJACAAIAEQWAALCQAgACABEFoACwkAIAAgARBZAAsKACACIAAgARAWCwoAIAAgASACEGwLCgAgACABIAIQMAsHACAAIAFqCwcAIAAgAWsLBwAgAEEIagsHACAAQXhqCw0AQovk55XyuI/XuH8LDQBC/LTd9YySl9W1fwsNAEKksbTUvr71pMMACwMAAQsL2i0BAEGAgMAAC9AtL3J1c3RjL2E1NWRkNzFkNWZiMGVjNWE2YTNhOWU4YzI3YjIxMjdiYTQ5MWNlNTIvbGlicmFyeS9jb3JlL3NyYy9zdHIvcGF0dGVybi5ycwAAABAATwAAAIwFAAAhAAAAAAAQAE8AAACYBQAAFAAAAAAAEABPAAAAmAUAACEAAABjYWxsZWQgYE9wdGlvbjo6dW53cmFwKClgIG9uIGEgYE5vbmVgIHZhbHVlY2FsbGVkIGBSZXN1bHQ6OnVud3JhcCgpYCBvbiBhbiBgRXJyYCB2YWx1ZQAABgAAAAAAAAABAAAABwAAAAgAAAAEAAAABAAAAAkAAAAAABAATwAAABwEAAAXAAAAAAAQAE8AAAC3AQAAJgAAAHNyYy9saWIucnMAABgBEAAKAAAAfAAAAEYAAABsaW5lICBjb2wgOgoKCgAANAEQAAUAAAA5ARAABQAAAD4BEAADAAAAQQEQAAEAAAAYARAACgAAAJQAAAAWAAAAGAEQAAoAAACYAAAAFgAAABgBEAAKAAAAvAAAABYAAAAYARAACgAAANEAAAAwAAAAGAEQAAoAAAAAAQAAFgAAABgBEAAKAAAAAgEAABYAAAAYARAACgAAACkBAAAnAAAAbGV0IF9fcHJzID0gW107CmxldCAgPSAnJzsKAOQBEAAEAAAA6AEQAAcAAAAYARAACgAAAFABAAA9AAAAAis9Jyc7CgAAABAAAAAAABECEAADAAAAFAIQAAMAAAAYARAACgAAAF4BAABQAAAAOwoAAAAAEAAAAAAAQAIQAAIAAAAYARAACgAAAGkBAABRAAAAX19wcnMucHVzaCgpOwoAAGQCEAALAAAAbwIQAAMAAAAYARAACgAAAGUBAABHAAAAckoyS3FYenhRZwAAlAIQAAoAAAAYARAACgAAAGcBAAAiAAAAGAEQAAoAAABxAQAARAAAAGNvbnN0IF9fcnN0ID0gYXdhaXQgUHJvbWlzZS5hbGwoX19wcnMpOwogPSAucmVwbGFjZSgvL2csICgpID0+IF9fcnN0LnNoaWZ0KCkpOwoAAAAQAAAAAADwAhAAAwAAAPMCEAAKAAAA/QIQABoAAAAYARAACgAAAHoBAAAKAAAAcmV0dXJuIABIAxAABwAAAEACEAACAAAAGAEQAAoAAAB7AQAAOwAAAGJvZHksIHJldHVybiAoYXN5bmMgZnVuY3Rpb24oKXt9KS5jb25zdHJ1Y3RvcgAAABgBEAAKAAAAjAEAAEkAAAB0cAAAGAEQAAoAAACgAQAANQAAAAsAAAAMAAAABAAAAAwAAAANAAAADgAAAGEgRGlzcGxheSBpbXBsZW1lbnRhdGlvbiByZXR1cm5lZCBhbiBlcnJvciB1bmV4cGVjdGVkbHkvcnVzdGMvYTU1ZGQ3MWQ1ZmIwZWM1YTZhM2E5ZThjMjdiMjEyN2JhNDkxY2U1Mi9saWJyYXJ5L2FsbG9jL3NyYy9zdHJpbmcucnMAABMEEABLAAAAugkAAA4AAAAPAAAAAAAAAAEAAAAHAAAATWlzc2luZyBjbG9zaW5nIGNvbW1hbmQgdGFnIGF0IACABBAAHwAAAE1pc3NpbmcgY29tbWFuZCB0eXBlIGF0IKgEEAAYAAAAVGVtcGxhdGUgZnVuY3Rpb24gY2FsbCBlcnJvcsgEEAAcAAAAVGVtcGxhdGUgc3ludGF4IGVycm9yOiAA7AQQABcAAAAAAAAA//////////8QAAAABAAAAAQAAAARAAAAEgAAABMAAABjYW5ub3QgYWNjZXNzIGEgVGhyZWFkIExvY2FsIFN0b3JhZ2UgdmFsdWUgZHVyaW5nIG9yIGFmdGVyIGRlc3RydWN0aW9uL3J1c3RjL2E1NWRkNzFkNWZiMGVjNWE2YTNhOWU4YzI3YjIxMjdiYTQ5MWNlNTIvbGlicmFyeS9zdGQvc3JjL3RocmVhZC9sb2NhbC5ycwAAAHYFEABPAAAApQEAABoAAAAUAAAAAAAAAAEAAAAVAAAAL3J1c3RjL2E1NWRkNzFkNWZiMGVjNWE2YTNhOWU4YzI3YjIxMjdiYTQ5MWNlNTIvbGlicmFyeS9jb3JlL3NyYy9zdHIvcGF0dGVybi5ycwDoBRAATwAAALcBAAAmAAAAY2xvc3VyZSBpbnZva2VkIHJlY3Vyc2l2ZWx5IG9yIGRlc3Ryb3llZCBhbHJlYWR5SnNWYWx1ZSgpAAAAeAYQAAgAAACABhAAAQAAAG51bGwgcG9pbnRlciBwYXNzZWQgdG8gcnVzdHJlY3Vyc2l2ZSB1c2Ugb2YgYW4gb2JqZWN0IGRldGVjdGVkIHdoaWNoIHdvdWxkIGxlYWQgdG8gdW5zYWZlIGFsaWFzaW5nIGluIHJ1c3QAACQAAAAEAAAABAAAACUAAAAmAAAAJwAAAGNhbGxlZCBgT3B0aW9uOjp1bndyYXAoKWAgb24gYSBgTm9uZWAgdmFsdWVBY2Nlc3NFcnJvcm1lbW9yeSBhbGxvY2F0aW9uIG9mICBieXRlcyBmYWlsZWQKAAAATgcQABUAAABjBxAADgAAAGxpYnJhcnkvc3RkL3NyYy9hbGxvYy5yc4QHEAAYAAAAUgEAAAkAAABsaWJyYXJ5L3N0ZC9zcmMvcGFuaWNraW5nLnJzrAcQABwAAABGAgAAHwAAAKwHEAAcAAAARwIAAB4AAAAoAAAADAAAAAQAAAApAAAAJAAAAAgAAAAEAAAAKgAAACsAAAAQAAAABAAAACwAAAAtAAAAJAAAAAgAAAAEAAAALgAAAC8AAABIYXNoIHRhYmxlIGNhcGFjaXR5IG92ZXJmbG93MAgQABwAAAAvY2FyZ28vcmVnaXN0cnkvc3JjL2dpdGh1Yi5jb20tMWVjYzYyOTlkYjllYzgyMy9oYXNoYnJvd24tMC4xMi4zL3NyYy9yYXcvbW9kLnJzAFQIEABPAAAAWgAAACgAAAAwAAAABAAAAAQAAAAxAAAAMgAAADMAAAAwAAAAAAAAAAEAAAAHAAAAbGlicmFyeS9hbGxvYy9zcmMvcmF3X3ZlYy5yc2NhcGFjaXR5IG92ZXJmbG93AAAA+AgQABEAAADcCBAAHAAAAAYCAAAFAAAAYSBmb3JtYXR0aW5nIHRyYWl0IGltcGxlbWVudGF0aW9uIHJldHVybmVkIGFuIGVycm9ybGlicmFyeS9hbGxvYy9zcmMvZm10LnJzAFcJEAAYAAAAZAIAACAAAAAuLgAAgAkQAAIAAABpbmRleCBvdXQgb2YgYm91bmRzOiB0aGUgbGVuIGlzICBidXQgdGhlIGluZGV4IGlzIAAAjAkQACAAAACsCRAAEgAAAGNhbGxlZCBgT3B0aW9uOjp1bndyYXAoKWAgb24gYSBgTm9uZWAgdmFsdWUAOQAAAAAAAAABAAAAOgAAAGA6IACACRAAAAAAAA0KEAACAAAAfSB9MHgwMDAxMDIwMzA0MDUwNjA3MDgwOTEwMTExMjEzMTQxNTE2MTcxODE5MjAyMTIyMjMyNDI1MjYyNzI4MjkzMDMxMzIzMzM0MzUzNjM3MzgzOTQwNDE0MjQzNDQ0NTQ2NDc0ODQ5NTA1MTUyNTM1NDU1NTY1NzU4NTk2MDYxNjI2MzY0NjU2NjY3Njg2OTcwNzE3MjczNzQ3NTc2Nzc3ODc5ODA4MTgyODM4NDg1ODY4Nzg4ODk5MDkxOTI5Mzk0OTU5Njk3OTg5OXJhbmdlIHN0YXJ0IGluZGV4ICBvdXQgb2YgcmFuZ2UgZm9yIHNsaWNlIG9mIGxlbmd0aCAAAADtChAAEgAAAP8KEAAiAAAAbGlicmFyeS9jb3JlL3NyYy9zbGljZS9pbmRleC5ycwA0CxAAHwAAADQAAAAFAAAAcmFuZ2UgZW5kIGluZGV4IGQLEAAQAAAA/woQACIAAAA0CxAAHwAAAEkAAAAFAAAAc2xpY2UgaW5kZXggc3RhcnRzIGF0ICBidXQgZW5kcyBhdCAAlAsQABYAAACqCxAADQAAADQLEAAfAAAAXAAAAAUAAABsaWJyYXJ5L2NvcmUvc3JjL3N0ci9wYXR0ZXJuLnJzANgLEAAfAAAAGgYAABUAAADYCxAAHwAAAEgGAAAVAAAA2AsQAB8AAABJBgAAFQAAAGxpYnJhcnkvY29yZS9zcmMvc3RyL21vZC5yc1suLi5dYnl0ZSBpbmRleCAgaXMgb3V0IG9mIGJvdW5kcyBvZiBgAAAASAwQAAsAAABTDBAAFgAAAAwKEAABAAAAKAwQABsAAABrAAAACQAAAGJlZ2luIDw9IGVuZCAoIDw9ICkgd2hlbiBzbGljaW5nIGAAAJQMEAAOAAAAogwQAAQAAACmDBAAEAAAAAwKEAABAAAAKAwQABsAAABvAAAABQAAACgMEAAbAAAAfQAAAC0AAAAgaXMgbm90IGEgY2hhciBib3VuZGFyeTsgaXQgaXMgaW5zaWRlICAoYnl0ZXMgKSBvZiBgSAwQAAsAAAD4DBAAJgAAAB4NEAAIAAAAJg0QAAYAAAAMChAAAQAAACgMEAAbAAAAfwAAAAUAAABsaWJyYXJ5L2NvcmUvc3JjL3VuaWNvZGUvcHJpbnRhYmxlLnJzAAAAZA0QACUAAAAaAAAANgAAAAABAwUFBgYCBwYIBwkRChwLGQwaDRAODQ8EEAMSEhMJFgEXBBgBGQMaBxsBHAIfFiADKwMtCy4BMAMxAjIBpwKpAqoEqwj6AvsF/QL+A/8JrXh5i42iMFdYi4yQHN0OD0tM+/wuLz9cXV/ihI2OkZKpsbq7xcbJyt7k5f8ABBESKTE0Nzo7PUlKXYSOkqmxtLq7xsrOz+TlAAQNDhESKTE0OjtFRklKXmRlhJGbncnOzw0RKTo7RUlXW1xeX2RljZGptLq7xcnf5OXwDRFFSWRlgISyvL6/1dfw8YOFi6Smvr/Fx87P2ttImL3Nxs7PSU5PV1leX4mOj7G2t7/BxsfXERYXW1z29/7/gG1x3t8OH25vHB1ffX6ur3+7vBYXHh9GR05PWFpcXn5/tcXU1dzw8fVyc490dZYmLi+nr7e/x8/X35pAl5gwjx/S1M7/Tk9aWwcIDxAnL+7vbm83PT9CRZCRU2d1yMnQ0djZ5/7/ACBfIoLfBIJECBsEBhGBrA6AqwUfCYEbAxkIAQQvBDQEBwMBBwYHEQpQDxIHVQcDBBwKCQMIAwcDAgMDAwwEBQMLBgEOFQVOBxsHVwcCBhYNUARDAy0DAQQRBg8MOgQdJV8gbQRqJYDIBYKwAxoGgv0DWQcWCRgJFAwUDGoGCgYaBlkHKwVGCiwEDAQBAzELLAQaBgsDgKwGCgYvMU0DgKQIPAMPAzwHOAgrBYL/ERgILxEtAyEPIQ+AjASClxkLFYiUBS8FOwcCDhgJgL4idAyA1hoMBYD/BYDfDPKdAzcJgVwUgLgIgMsFChg7AwoGOAhGCAwGdAseA1oEWQmAgxgcChYJTASAigarpAwXBDGhBIHaJgcMBQWAphCB9QcBICoGTASAjQSAvgMbAw8NAAYBAQMBBAIFBwcCCAgJAgoFCwIOBBABEQISBRMRFAEVAhcCGQ0cBR0IJAFqBGsCrwO8As8C0QLUDNUJ1gLXAtoB4AXhAucE6ALuIPAE+AL6AvsBDCc7Pk5Pj56en3uLk5aisrqGsQYHCTY9Plbz0NEEFBg2N1ZXf6qur7014BKHiY6eBA0OERIpMTQ6RUZJSk5PZGVctrcbHAcICgsUFzY5Oqip2NkJN5CRqAcKOz5maY+Sb1+/7u9aYvT8/5qbLi8nKFWdoKGjpKeorbq8xAYLDBUdOj9FUaanzM2gBxkaIiU+P+fs7//FxgQgIyUmKDM4OkhKTFBTVVZYWlxeYGNlZmtzeH1/iqSqr7DA0K6vbm+TXiJ7BQMELQNmAwEvLoCCHQMxDxwEJAkeBSsFRAQOKoCqBiQEJAQoCDQLTkOBNwkWCggYO0U5A2MICTAWBSEDGwUBQDgESwUvBAoHCQdAICcEDAk2AzoFGgcEDAdQSTczDTMHLggKgSZSTigIKhYaJhwUFwlOBCQJRA0ZBwoGSAgnCXULP0EqBjsFCgZRBgEFEAMFgItiHkgICoCmXiJFCwoGDRM6Bgo2LAQXgLk8ZFMMSAkKRkUbSAhTDUmBB0YKHQNHSTcDDggKBjkHCoE2GYC3AQ8yDYObZnULgMSKTGMNhC+P0YJHobmCOQcqBFwGJgpGCigFE4KwW2VLBDkHEUAFCwIOl/gIhNYqCaLngTMtAxEECIGMiQRrBQ0DCQcQkmBHCXQ8gPYKcwhwFUaAmhQMVwkZgIeBRwOFQg8VhFAfgOErgNUtAxoEAoFAHxE6BQGE4ID3KUwECgQCgxFETD2AwjwGAQRVBRs0AoEOLARkDFYKgK44HQ0sBAkHAg4GgJqD2AUQAw0DdAxZBwwEAQ8MBDgICgYoCCJOgVQMFQMFAwcJHQMLBQYKCgYICAcJgMslCoQGbGlicmFyeS9jb3JlL3NyYy91bmljb2RlL3VuaWNvZGVfZGF0YS5ycwAAAAUTEAAoAAAASwAAACgAAAAFExAAKAAAAFcAAAAWAAAABRMQACgAAABSAAAAPgAAAEVycm9yAAAAAAMAAIMEIACRBWAAXROgABIXIB8MIGAf7yygKyowICxvpuAsAqhgLR77YC4A/iA2nv9gNv0B4TYBCiE3JA3hN6sOYTkvGKE5MBzhR/MeIUzwauFPT28hUJ28oVAAz2FRZdGhUQDaIVIA4OFTMOFhVa7ioVbQ6OFWIABuV/AB/1cAcAAHAC0BAQECAQIBAUgLMBUQAWUHAgYCAgEEIwEeG1sLOgkJARgEAQkBAwEFKwM8CCoYASA3AQEBBAgEAQMHCgIdAToBAQECBAgBCQEKAhoBAgI5AQQCBAICAwMBHgIDAQsCOQEEBQECBAEUAhYGAQE6AQECAQQIAQcDCgIeATsBAQEMAQkBKAEDATcBAQMFAwEEBwILAh0BOgECAQIBAwEFAgcCCwIcAjkCAQECBAgBCQEKAh0BSAEEAQIDAQEIAVEBAgcMCGIBAgkLBkoCGwEBAQEBNw4BBQECBQsBJAkBZgQBBgECAgIZAgQDEAQNAQICBgEPAQADAAMdAh4CHgJAAgEHCAECCwkBLQMBAXUCIgF2AwQCCQEGA9sCAgE6AQEHAQEBAQIIBgoCATAfMQQwBwEBBQEoCQwCIAQCAgEDOAEBAgMBAQM6CAICmAMBDQEHBAEGAQMCxkAAAcMhAAONAWAgAAZpAgAEAQogAlACAAEDAQQBGQIFAZcCGhINASYIGQsuAzABAgQCAicBQwYCAgICDAEIAS8BMwEBAwICBQIBASoCCAHuAQIBBAEAAQAQEBAAAgAB4gGVBQADAQIFBCgDBAGlAgAEAAKZCzEEewE2DykBAgIKAzEEAgIHAT0DJAUBCD4BDAI0CQoEAgFfAwIBAQIGAaABAwgVAjkCAQEBARYBDgcDBcMIAgMBARcBUQECBgEBAgEBAgEC6wECBAYCAQIbAlUIAgEBAmoBAQECBgEBZQMCBAEFAAkBAvUBCgIBAQQBkAQCAgQBIAooBgIECAEJBgIDLg0BAgAHAQYBAVIWAgcBAgECegYDAQECAQcBAUgCAwEBAQACAAU7BwABPwRRAQACAC4CFwABAQMEBQgIAgceBJQDADcEMggBDgEWBQEPAAcBEQIHAQIBBQAHAAE9BAAHbQcAYIDwAACAFgAAACAgAQAwYAEBMHECCQUSAWQBGgEAAQALHQIFAS8BAAEAewlwcm9kdWNlcnMCCGxhbmd1YWdlAQRSdXN0AAxwcm9jZXNzZWQtYnkDBXJ1c3RjHTEuNjQuMCAoYTU1ZGQ3MWQ1IDIwMjItMDktMTkpBndhbHJ1cwYwLjE5LjAMd2FzbS1iaW5kZ2VuEjAuMi44MyAoZWJhNjkxZjM4KQ==");var an=class{async init(){await fi(di);let e=new jt("<%","%>","\0","*","-","_","tR");this.renderer=new qt(e)}async parse_commands(e,t){return this.renderer.render_content(e,t)}};var $e;(function(a){a[a.CreateNewFromTemplate=0]="CreateNewFromTemplate",a[a.AppendActiveFile=1]="AppendActiveFile",a[a.OverwriteFile=2]="OverwriteFile",a[a.OverwriteActiveFile=3]="OverwriteActiveFile",a[a.DynamicProcessor=4]="DynamicProcessor",a[a.StartupTemplate=5]="StartupTemplate"})($e||($e={}));var Kt=class{constructor(e){this.plugin=e;this.functions_generator=new rn(this.plugin),this.parser=new an}async setup(){this.files_with_pending_templates=new Set,await this.parser.init(),await this.functions_generator.init(),this.plugin.registerMarkdownPostProcessor((e,t)=>this.process_dynamic_templates(e,t))}create_running_config(e,t,n){let o=xt(this.plugin.app);return{template_file:e,target_file:t,run_mode:n,active_file:o}}async read_and_parse_template(e){let t=await this.plugin.app.vault.read(e.template_file);return this.parse_template(e,t)}async parse_template(e,t){let n=await this.functions_generator.generate_object(e,De.USER_INTERNAL);return this.current_functions_object=n,await this.parser.parse_commands(t,n)}start_templater_task(e){this.files_with_pending_templates.add(e)}async end_templater_task(e){this.files_with_pending_templates.delete(e),this.files_with_pending_templates.size===0&&(this.plugin.app.workspace.trigger("templater:all-templates-executed"),await this.functions_generator.teardown())}async create_new_note_from_template(e,t,n,o=!0){if(!t)switch(this.plugin.app.vault.getConfig("newFileLocation")){case"current":{let b=xt(this.plugin.app);b&&(t=b.parent);break}case"folder":t=this.plugin.app.fileManager.getNewFileParent("");break;case"root":t=this.plugin.app.vault.getRoot();break;default:break}let s=e instanceof ve.TFile&&e.extension||"md",a=await ye(async()=>{let f=t instanceof ve.TFolder?t.path:t,b=this.plugin.app.vault.getAvailablePath((0,ve.normalizePath)(`${f??""}/${n||"Untitled"}`),s),k=On(b);return k&&!this.plugin.app.vault.getAbstractFileByPathInsensitive(k)&&await this.plugin.app.vault.createFolder(k),this.plugin.app.vault.create(b,"")},`Couldn't create ${s} file.`);if(a==null)return;let{path:A}=a;this.start_templater_task(A);let c,d;if(e instanceof ve.TFile?(c=this.create_running_config(e,a,0),d=await ye(async()=>this.read_and_parse_template(c),"Template parsing error, aborting.")):(c=this.create_running_config(void 0,a,0),d=await ye(async()=>this.parse_template(c,e),"Template parsing error, aborting.")),d==null){await this.plugin.app.vault.delete(a),await this.end_templater_task(A);return}if(await this.plugin.app.vault.modify(a,d),this.plugin.app.workspace.trigger("templater:new-note-from-template",{file:a,content:d}),o){let f=this.plugin.app.workspace.getLeaf(!1);if(!f){K(new w("No active leaf"));return}await f.openFile(a,{state:{mode:"source"}}),await this.plugin.editor_handler.jump_to_next_cursor_location(a,!0),f.setEphemeralState({rename:"all"})}return await this.end_templater_task(A),a}async append_template_to_active_file(e){let t=this.plugin.app.workspace.getActiveViewOfType(ve.MarkdownView),n=this.plugin.app.workspace.activeEditor;if(!n||!n.file||!n.editor){K(new w("No active editor, can't append templates."));return}let{path:o}=n.file;this.start_templater_task(o);let s=this.create_running_config(e,n.file,1),a=await ye(async()=>this.read_and_parse_template(s),"Template parsing error, aborting.");if(a==null){await this.end_templater_task(o);return}let c=n.editor.getDoc(),d=c.listSelections();c.replaceSelection(a),n.file&&await this.plugin.app.vault.append(n.file,""),this.plugin.app.workspace.trigger("templater:template-appended",{view:t,editor:n,content:a,oldSelections:d,newSelections:c.listSelections()}),await this.plugin.editor_handler.jump_to_next_cursor_location(n.file,!0),await this.end_templater_task(o)}async write_template_to_file(e,t){let{path:n}=t;this.start_templater_task(n);let o=this.plugin.app.workspace.activeEditor,s=xt(this.plugin.app),a=this.create_running_config(e,t,2),A=await ye(async()=>this.read_and_parse_template(a),"Template parsing error, aborting.");if(A==null){await this.end_templater_task(n);return}await this.plugin.app.vault.modify(t,A),s?.path===t.path&&o&&o.editor&&o.editor.setSelection({line:0,ch:0},{line:0,ch:0}),this.plugin.app.workspace.trigger("templater:new-note-from-template",{file:t,content:A}),await this.plugin.editor_handler.jump_to_next_cursor_location(t,!0),await this.end_templater_task(n)}overwrite_active_file_commands(){let e=this.plugin.app.workspace.activeEditor;if(!e||!e.file){K(new w("Active editor is null, can't overwrite content"));return}this.overwrite_file_commands(e.file,!0)}async overwrite_file_commands(e,t=!1){let{path:n}=e;this.start_templater_task(n);let o=this.create_running_config(e,e,t?3:2),s=await ye(async()=>this.read_and_parse_template(o),"Template parsing error, aborting.");if(s==null){await this.end_templater_task(n);return}await this.plugin.app.vault.modify(e,s),this.plugin.app.workspace.trigger("templater:overwrite-file",{file:e,content:s}),await this.plugin.editor_handler.jump_to_next_cursor_location(e,!0),await this.end_templater_task(n)}async process_dynamic_templates(e,t){let n=qn(),o=document.createNodeIterator(e,NodeFilter.SHOW_TEXT),s,a=!1,A;for(;s=o.nextNode();){let c=s.nodeValue;if(c!==null){let d=n.exec(c);if(d!==null){let f=this.plugin.app.metadataCache.getFirstLinkpathDest("",t.sourcePath);if(!f||!(f instanceof ve.TFile))return;if(!a){a=!0;let b=this.create_running_config(f,f,4);A=await this.functions_generator.generate_object(b,De.USER_INTERNAL),this.current_functions_object=A}}for(;d!=null;){let f=d[1]+d[2],b=await ye(async()=>await this.parser.parse_commands(f,A),`Command Parsing error in dynamic command '${f}'`);if(b==null)return;let k=n.lastIndex-d[0].length,x=n.lastIndex;c=c.substring(0,k)+b+c.substring(x),n.lastIndex+=b.length-d[0].length,d=n.exec(c)}s.nodeValue=c}}}get_new_file_template_for_folder(e){do{let t=this.plugin.settings.folder_templates.find(n=>n.folder==e.path);if(t&&t.template)return t.template;e=e.parent}while(e)}get_new_file_template_for_file(e){let t=this.plugin.settings.file_templates.find(n=>new RegExp(n.regex).test(e.path));if(t&&t.template)return t.template}static async on_file_creation(e,t,n){if(!(n instanceof ve.TFile)||n.extension!=="md")return;let o=(0,ve.normalizePath)(e.plugin.settings.templates_folder);if(!(n.path.includes(o)&&o!=="/")&&(await sr(300),!e.files_with_pending_templates.has(n.path)))if(n.stat.size==0&&e.plugin.settings.enable_folder_templates){let s=e.get_new_file_template_for_folder(n.parent);if(!s)return;let a=await ye(async()=>lt(t,s),`Couldn't find template ${s}`);if(a==null)return;await e.write_template_to_file(a,n)}else if(n.stat.size==0&&e.plugin.settings.enable_file_templates){let s=e.get_new_file_template_for_file(n);if(!s)return;let a=await ye(async()=>lt(t,s),`Couldn't find template ${s}`);if(a==null)return;await e.write_template_to_file(a,n)}else n.stat.size<=1e5?await e.overwrite_file_commands(n):console.log(`Templater skipped parsing ${n.path} because file size exceeds 10000`)}async execute_startup_scripts(){for(let e of this.plugin.settings.startup_templates){if(!e)continue;let t=ce(()=>lt(this.plugin.app,e),`Couldn't find startup template "${e}"`);if(!t)continue;let{path:n}=t;this.start_templater_task(n);let o=this.create_running_config(t,t,5);await ye(async()=>this.read_and_parse_template(o),"Startup Template parsing error, aborting."),await this.end_templater_task(n)}}};var gi=V(require("obsidian")),_r=class{constructor(e,t,n){this.plugin=e;this.templater=t;this.settings=n}setup(){this.plugin.app.workspace.onLayoutReady(()=>{this.update_trigger_file_on_creation()}),this.update_syntax_highlighting(),this.update_file_menu()}update_syntax_highlighting(){let e=this.plugin.editor_handler.desktopShouldHighlight(),t=this.plugin.editor_handler.mobileShouldHighlight();e||t?this.plugin.editor_handler.enable_highlighter():this.plugin.editor_handler.disable_highlighter()}update_trigger_file_on_creation(){this.settings.trigger_on_file_creation?(this.trigger_on_file_creation_event=this.plugin.app.vault.on("create",e=>Kt.on_file_creation(this.templater,this.plugin.app,e)),this.plugin.registerEvent(this.trigger_on_file_creation_event)):this.trigger_on_file_creation_event&&(this.plugin.app.vault.offref(this.trigger_on_file_creation_event),this.trigger_on_file_creation_event=void 0)}update_file_menu(){this.plugin.registerEvent(this.plugin.app.workspace.on("file-menu",(e,t)=>{t instanceof gi.TFolder&&e.addItem(n=>{n.setTitle("Create new note from template").setIcon("templater-icon").onClick(()=>{this.plugin.fuzzy_suggester.create_new_note_from_template(t)})})}))}};var ln=class{constructor(e){this.plugin=e}setup(){this.plugin.addCommand({id:"insert-templater",name:"Open insert template modal",icon:"templater-icon",hotkeys:[{modifiers:["Alt"],key:"e"}],callback:()=>{this.plugin.fuzzy_suggester.insert_template()}}),this.plugin.addCommand({id:"replace-in-file-templater",name:"Replace templates in the active file",icon:"templater-icon",hotkeys:[{modifiers:["Alt"],key:"r"}],callback:()=>{this.plugin.templater.overwrite_active_file_commands()}}),this.plugin.addCommand({id:"jump-to-next-cursor-location",name:"Jump to next cursor location",icon:"text-cursor",hotkeys:[{modifiers:["Alt"],key:"Tab"}],callback:()=>{this.plugin.editor_handler.jump_to_next_cursor_location()}}),this.plugin.addCommand({id:"create-new-note-from-template",name:"Create new note from template",icon:"templater-icon",hotkeys:[{modifiers:["Alt"],key:"n"}],callback:()=>{this.plugin.fuzzy_suggester.create_new_note_from_template()}}),this.register_templates_hotkeys()}register_templates_hotkeys(){this.plugin.settings.enabled_templates_hotkeys.forEach(e=>{e&&this.add_template_hotkey(null,e)})}add_template_hotkey(e,t){this.remove_template_hotkey(e),t&&(this.plugin.addCommand({id:t,name:`Insert ${t}`,icon:"templater-icon",callback:()=>{let n=ce(()=>lt(this.plugin.app,t),"Couldn't find the template file associated with this hotkey");!n||this.plugin.templater.append_template_to_active_file(n)}}),this.plugin.addCommand({id:`create-${t}`,name:`Create ${t}`,icon:"templater-icon",callback:()=>{let n=ce(()=>lt(this.plugin.app,t),"Couldn't find the template file associated with this hotkey");!n||this.plugin.templater.create_new_note_from_template(n)}}))}remove_template_hotkey(e){e&&(this.plugin.removeCommand(`${this.plugin.manifest.id}:create-${e}`),this.plugin.removeCommand(`${this.plugin.manifest.id}:${e}`))}};var mn=V(require("obsidian"));var pn=V(require("obsidian"));var cn=class{constructor(e){this.app=e}async jump_to_next_cursor_location(){let e=this.app.workspace.activeEditor;if(!e||!e.editor)return;let t=e.editor.getValue(),{new_content:n,positions:o}=this.replace_and_get_cursor_positions(t);if(o){let s=e instanceof pn.MarkdownView?e.currentMode.getFoldInfo():null;e.editor.setValue(n),s&&Array.isArray(s.folds)&&(o.forEach(a=>{s.folds=s.folds.filter(A=>A.from>a.line||A.to<a.line)}),e instanceof pn.MarkdownView&&e.currentMode.applyFoldInfo(s)),this.set_cursor_location(o)}if(this.app.vault.getConfig("vimMode")){let s=e.editor.cm.cm;window.CodeMirrorAdapter.Vim.handleKey(s,"i","mapping")}}get_editor_position_from_index(e,t){let n=e.slice(0,t),o=0,s=-1,a=-1;for(;(a=n.indexOf(`
`,a+1))!==-1;o++,s=a);s+=1;let A=e.slice(s,t).length;return{line:o,ch:A}}replace_and_get_cursor_positions(e){let t=[],n,o=new RegExp("<%\\s*tp.file.cursor\\((?<order>[0-9]*)\\)\\s*%>","g");for(;(n=o.exec(e))!=null;)t.push(n);if(t.length===0)return{};t.sort((c,d)=>Number(c.groups&&c.groups.order)-Number(d.groups&&d.groups.order));let s=t[0][0];t=t.filter(c=>c[0]===s);let a=[],A=0;for(let c of t){let d=c.index-A;if(a.push(this.get_editor_position_from_index(e,d)),e=e.replace(new RegExp(Bn(c[0])),""),A+=c[0].length,c[1]==="")break}return{new_content:e,positions:a}}set_cursor_location(e){let t=this.app.workspace.activeEditor;if(!t||!t.editor)return;let n=t.editor,o=[];for(let a of e)o.push({from:a});let s={selections:o};n.transaction(s)}};var wi=V(require("obsidian"));var Ho={app:{name:"app",description:"This module exposes the app instance. Prefer to use this over the global app instance."},config:{name:"config",description:`This module exposes Templater's running configuration.

This is mostly useful when writing scripts requiring some context information.
`,functions:{template_file:{name:"template_file",description:"The `TFile` object representing the template file.",definition:"tp.config.template_file"},target_file:{name:"target_file",description:"The `TFile` object representing the target file where the template will be inserted.",definition:"tp.config.target_file"},run_mode:{name:"run_mode",description:"The `RunMode`, representing the way Templater was launched (Create new from template, Append to active file, ...).",definition:"tp.config.run_mode"},active_file:{name:"active_file",description:"The active file (if existing) when launching Templater.",definition:"tp.config.active_file?"}}},date:{name:"date",description:"This module contains every internal function related to dates.",functions:{now:{name:"now",description:"Retrieves the date.",definition:'tp.date.now(format: string = "YYYY-MM-DD", offset?: number\u23AEstring, reference?: string, reference_format?: string)',args:[{name:"format",description:'The format for the date. Defaults to `"YYYY-MM-DD"`. Refer to [format reference](https://momentjs.com/docs/#/displaying/format/).'},{name:"offset",description:"Duration to offset the date from. If a number is provided, duration will be added to the date in days. You can also specify the offset as a string using the ISO 8601 format."},{name:"reference",description:"The date referential, e.g. set this to the note's title."},{name:"reference_format",description:"The format for the reference date. Refer to [format reference](https://momentjs.com/docs/#/displaying/format/)."}],examples:[{name:"Date now",example:"<% tp.date.now() %>"},{name:"Date now with format",example:'<% tp.date.now("Do MMMM YYYY") %>'},{name:"Last week",example:'<% tp.date.now("YYYY-MM-DD", -7) %>'},{name:"Next week",example:'<% tp.date.now("YYYY-MM-DD", 7) %>'},{name:"Last month",example:'<% tp.date.now("YYYY-MM-DD", "P-1M") %>'},{name:"Next year",example:'<% tp.date.now("YYYY-MM-DD", "P1Y") %>'},{name:"File's title date + 1 day (tomorrow)",example:'<% tp.date.now("YYYY-MM-DD", 1, tp.file.title, "YYYY-MM-DD") %>'},{name:"File's title date - 1 day (yesterday)",example:'<% tp.date.now("YYYY-MM-DD", -1, tp.file.title, "YYYY-MM-DD") %>'}]},tomorrow:{name:"tomorrow",description:"Retrieves tomorrow's date.",definition:'tp.date.tomorrow(format: string = "YYYY-MM-DD")',args:[{name:"format",description:'The format for the date. Defaults to `"YYYY-MM-DD"`. Refer to [format reference](https://momentjs.com/docs/#/displaying/format/).'}],examples:[{name:"Date tomorrow",example:"<% tp.date.tomorrow() %>"},{name:"Date tomorrow with format",example:'<% tp.date.tomorrow("Do MMMM YYYY") %>'}]},yesterday:{name:"yesterday",description:"Retrieves yesterday's date.",definition:'tp.date.yesterday(format: string = "YYYY-MM-DD")',args:[{name:"format",description:'The format for the date. Defaults to `"YYYY-MM-DD"`. Refer to [format reference](https://momentjs.com/docs/#/displaying/format/).'}],examples:[{name:"Date yesterday",example:"<% tp.date.yesterday() %>"},{name:"Date yesterday with format",example:'<% tp.date.yesterday("Do MMMM YYYY") %>'}]},weekday:{name:"weekday",description:"",definition:'tp.date.weekday(format: string = "YYYY-MM-DD", weekday: number, reference?: string, reference_format?: string)',args:[{name:"format",description:'The format for the date. Defaults to `"YYYY-MM-DD"`. Refer to [format reference](https://momentjs.com/docs/#/displaying/format/).'},{name:"weekday",description:"Week day number. If the locale assigns Monday as the first day of the week, `0` will be Monday, `-7` will be last week's day."},{name:"reference",description:"The date referential, e.g. set this to the note's title."},{name:"reference_format",description:"The format for the reference date. Refer to [format reference](https://momentjs.com/docs/#/displaying/format/)."}],examples:[{name:"This week's Monday",example:'<% tp.date.weekday("YYYY-MM-DD", 0) %>'},{name:"Next Monday",example:'<% tp.date.weekday("YYYY-MM-DD", 7) %>'},{name:"File's title Monday",example:'<% tp.date.weekday("YYYY-MM-DD", 0, tp.file.title, "YYYY-MM-DD") %>'},{name:"File's title previous Monday",example:'<% tp.date.weekday("YYYY-MM-DD", -7, tp.file.title, "YYYY-MM-DD") %>'}]}},momentjs:{examples:[{name:"Date now",example:'<% moment(tp.file.title, "YYYY-MM-DD").format("YYYY-MM-DD") %>'},{name:"Get start of month from note title",example:'<% moment(tp.file.title, "YYYY-MM-DD").startOf("month").format("YYYY-MM-DD") %>'},{name:"Get end of month from note title",example:'<% moment(tp.file.title, "YYYY-MM-DD").endOf("month").format("YYYY-MM-DD") %>'}]}},file:{name:"file",description:"This module contains every internal function related to files.",functions:{content:{name:"content",description:"The string contents of the file at the time that Templater was executed. Manipulating this string will *not* update the current file.",definition:"tp.file.content",examples:[{name:"Retrieve file content",example:"<% tp.file.content %>"}]},create_new:{name:"create_new",description:"Creates a new file using a specified template or with a specified content.",definition:"tp.file.create_new(template: TFile \u23AE string, filename?: string, open_new: boolean = false, folder?: TFolder | string)",args:[{name:"template",description:"Either the template used for the new file content, or the file content as a string. If it is the template to use, you retrieve it with `tp.file.find_tfile(TEMPLATENAME)`."},{name:"filename",description:'The filename of the new file, defaults to "Untitled".'},{name:"open_new",description:"Whether to open or not the newly created file. Warning: if you use this option, since commands are executed asynchronously, the file can be opened first and then other commands are appended to that new file and not the previous file."},{name:"folder",description:'The folder to put the new file in, defaults to Obsidian\'s default location. If you want the file to appear in a different folder, specify it with `"PATH/TO/FOLDERNAME"` or `app.vault.getAbstractFileByPath("PATH/TO/FOLDERNAME")`.'}],examples:[{name:"File creation",example:'<%* await tp.file.create_new("MyFileContent", "MyFilename") %>'},{name:"File creation with template",example:'<%* await tp.file.create_new(tp.file.find_tfile("MyTemplate"), "MyFilename") %>'},{name:"File creation and open created note",example:'<%* await tp.file.create_new("MyFileContent", "MyFilename", true) %>'},{name:"File creation in current folder",example:'<%* await tp.file.create_new("MyFileContent", "MyFilename", false, tp.file.folder(true)) %>'},{name:"File creation in specified folder with string path",example:'<%* await tp.file.create_new("MyFileContent", "MyFilename", false, "Path/To/MyFolder") %>'},{name:"File creation in specified folder with TFolder",example:'<%* await tp.file.create_new("MyFileContent", "MyFilename", false, app.vault.getAbstractFileByPath("MyFolder")) %>'},{name:"File creation and append link to current note",example:'[[<% (await tp.file.create_new("MyFileContent", "MyFilename")).basename %>]]'}]},creation_date:{name:"creation_date",description:"Retrieves the file's creation date.",definition:'tp.file.creation_date(format: string = "YYYY-MM-DD HH:mm")',args:[{name:"format",description:'The format for the date. Defaults to `"YYYY-MM-DD HH:mm"`. Refer to [format reference](https://momentjs.com/docs/#/displaying/format/).'}],examples:[{name:"File creation date",example:"<% tp.file.creation_date() %>"},{name:"File creation date with format",example:'<% tp.file.creation_date("dddd Do MMMM YYYY HH:mm") %>'}]},cursor:{name:"cursor",description:`Sets the cursor to this location after the template has been inserted. 

You can navigate between the different cursors using the configured hotkey in Obsidian settings.
`,definition:"tp.file.cursor(order?: number)",args:[{name:"order",description:`The order of the different cursors jump, e.g. it will jump from 1 to 2 to 3, and so on.
If you specify multiple tp.file.cursor with the same order, the editor will switch to multi-cursor.
`}],examples:[{name:"File cursor",example:"<% tp.file.cursor() %>"},{name:"File multi-cursor",example:"<% tp.file.cursor(1) %>Content<% tp.file.cursor(1) %>"}]},cursor_append:{name:"cursor_append",description:"Appends some content after the active cursor in the file.",definition:"tp.file.cursor_append(content: string)",args:[{name:"content",description:"The content to append after the active cursor."}],examples:[{name:"File cursor append",example:'<% tp.file.cursor_append("Some text") %>'}]},exists:{name:"exists",description:"Check to see if a file exists by it's file path. The full path to the file, relative to the Vault and containing the extension, must be provided.",definition:"tp.file.exists(filepath: string)",args:[{name:"filepath",description:"The full file path of the file we want to check existence for."}],examples:[{name:"File existence",example:'<% await tp.file.exists("MyFolder/MyFile.md") %>'},{name:"File existence of current file",example:'<% await tp.file.exists(tp.file.folder(true) + "/" + tp.file.title + ".md") %>'}]},find_tfile:{name:"find_tfile",description:"Search for a file and returns its `TFile` instance.",definition:"tp.file.find_tfile(filename: string)",args:[{name:"filename",description:"The filename we want to search and resolve as a `TFile`."}],examples:[{name:"File find TFile",example:'<% tp.file.find_tfile("MyFile").basename %>'}]},folder:{name:"folder",description:"Retrieves the file's folder name.",definition:"tp.file.folder(absolute: boolean = false)",args:[{name:"absolute",description:"If set to `true`, returns the vault-absolute path of the folder. If `false`, only returns the basename of the folder (the last part). Defaults to `false`."}],examples:[{name:"File folder (Folder)",example:"<% tp.file.folder() %>"},{name:"File folder with vault-absolute path (Path/To/Folder)",example:"<% tp.file.folder(true) %>"}]},include:{name:"include",description:"Includes the file's link content. Templates in the included content will be resolved.",definition:"tp.file.include(include_link: string \u23AE TFile)",args:[{name:"include_link",description:'The link to the file to include, e.g. `"[[MyFile]]"`, or a TFile object. Also supports sections or blocks inclusions.'}],examples:[{name:"File include",example:'<% tp.file.include("[[Template1]]") %>'},{name:"File include TFile",example:'<% tp.file.include(tp.file.find_tfile("MyFile")) %>'},{name:"File include section",example:'<% tp.file.include("[[MyFile#Section1]]") %>'},{name:"File include block",example:'<% tp.file.include("[[MyFile#^block1]]") %>'}]},last_modified_date:{name:"last_modified_date",description:"Retrieves the file's last modification date.",definition:'tp.file.last_modified_date(format: string = "YYYY-MM-DD HH:mm")',args:[{name:"format",description:'The format for the date. Defaults to `"YYYY-MM-DD HH:mm"`. Refer to [format reference](https://momentjs.com/docs/#/displaying/format/).'}],examples:[{name:"File last modified date",example:"<% tp.file.last_modified_date() %>"},{name:"File last modified date with format",example:'<% tp.file.last_modified_date("dddd Do MMMM YYYY HH:mm") %>'}]},move:{name:"move",description:"Moves the file to the desired vault location.",definition:"tp.file.move(new_path: string, file_to_move?: TFile)",args:[{name:"new_path",description:'The new vault relative path of the file, without the file extension. Note: the new path needs to include the folder and the filename, e.g. `"/Notes/MyNote"`.'},{name:"file_to_move",description:"The file to move, defaults to the current file."}],examples:[{name:"File move",example:'<% await tp.file.move("/A/B/" + tp.file.title) %>'},{name:"File move and rename",example:'<% await tp.file.move("/A/B/NewTitle") %>'}]},path:{name:"path",description:"Retrieves the file's absolute path on the system.",definition:"tp.file.path(relative: boolean = false)",args:[{name:"relative",description:"If set to `true`, only retrieves the vault's relative path."}],examples:[{name:"File path",example:"<% tp.file.path() %>"},{name:"File relative path (relative to vault root)",example:"<% tp.file.path(true) %>"}]},rename:{name:"rename",description:"Renames the file (keeps the same file extension).",definition:"tp.file.rename(new_title: string)",args:[{name:"new_title",description:"The new file title."}],examples:[{name:"File rename",example:'<% await tp.file.rename("MyNewName") %>'},{name:"File append a 2 to the file name",example:'<% await tp.file.rename(tp.file.title + "2") %>'}]},selection:{name:"selection",description:"Retrieves the active file's text selection.",definition:"tp.file.selection()",examples:[{name:"File selection",example:"<% tp.file.selection() %>"}]},tags:{name:"tags",description:"Retrieves the file's tags (array of string).",definition:"tp.file.tags",examples:[{name:"File tags",example:"<% tp.file.tags %>"}]},title:{name:"title",definition:"tp.file.title",description:"Retrieves the file's title.",examples:[{name:"File title",example:"<% tp.file.title %>"},{name:"Strip the Zettelkasten ID of title (if space separated)",example:'<% tp.file.title.split(" ")[1] %>'}]}}},frontmatter:{name:"frontmatter",description:"This modules exposes all the frontmatter variables of a file as variables."},hooks:{name:"hooks",description:"This module exposes hooks that allow you to execute code when a Templater event occurs.",functions:{on_all_templates_executed:{name:"on_all_templates_executed",description:"Hooks into when all actively running templates have finished executing. Most of the time this will be a single template, unless you are using `tp.file.include` or `tp.file.create_new`.\n\nMultiple invokations of this method will have their callback functions run in parallel.",definition:"tp.hooks.on_all_templates_executed(callback_function: () => any)",args:[{name:"callback_function",description:"Callback function that will be executed when all actively running templates have finished executing."}]}}},obsidian:{name:"obsidian",description:"This module exposes all the functions and classes from the Obsidian API."},system:{name:"system",description:"This module contains system related functions.",functions:{clipboard:{name:"clipboard",description:"Retrieves the clipboard's content.",definition:"tp.system.clipboard()",examples:[{name:"Clipboard",example:"<% tp.system.clipboard() %>"}]},prompt:{name:"prompt",description:"Spawns a prompt modal and returns the user's input.",definition:"tp.system.prompt(prompt_text?: string, default_value?: string, throw_on_cancel: boolean = false, multiline?: boolean = false)",args:[{name:"prompt_text",description:"Text placed above the input field."},{name:"default_value",description:"A default value for the input field."},{name:"throw_on_cancel",description:"Throws an error if the prompt is canceled, instead of returning a `null` value."},{name:"multiline",description:"If set to `true`, the input field will be a multiline textarea. Defaults to `false`."}],examples:[{name:"Prompt",example:'<% tp.system.prompt("Please enter a value") %>'},{name:"Prompt with default value",example:'<% tp.system.prompt("What is your mood today?", "happy") %>'},{name:"Multiline prompt",example:'<% tp.system.prompt("What is your mood today?", null, false, true) %>'}]},suggester:{name:"suggester",description:"Spawns a suggester prompt and returns the user's chosen item.",definition:'tp.system.suggester(text_items: string[] \u23AE ((item: T) => string), items: T[], throw_on_cancel: boolean = false, placeholder: string = "", limit?: number = undefined)',args:[{name:"text_items",description:"Array of strings representing the text that will be displayed for each item in the suggester prompt. This can also be a function that maps an item to its text representation."},{name:"items",description:"Array containing the values of each item in the correct order."},{name:"throw_on_cancel",description:"Throws an error if the prompt is canceled, instead of returning a `null` value."},{name:"placeholder",description:"Placeholder string of the prompt."},{name:"limit",description:"Limit the number of items rendered at once (useful to improve performance when displaying large lists)."}],examples:[{name:"Suggester",example:'<% tp.system.suggester(["Happy", "Sad", "Confused"], ["Happy", "Sad", "Confused"]) %>'},{name:"Suggester with mapping function (same as above example)",example:'<% tp.system.suggester((item) => item, ["Happy", "Sad", "Confused"]) %>'},{name:"Suggester for files",example:"[[<% (await tp.system.suggester((item) => item.basename, app.vault.getMarkdownFiles())).basename %>]]"},{name:"Suggester for tags",example:'<% tp.system.suggester(item => item, Object.keys(app.metadataCache.getTags()).map(x => x.replace("#", ""))) %>'}]}}},web:{name:"web",description:"This modules contains every internal function related to the web (making web requests).",functions:{daily_quote:{name:"daily_quote",description:"Retrieves and parses the daily quote from `https://github.com/Zachatoo/quotes-database` as a callout.",definition:"tp.web.daily_quote()",examples:[{name:"Daily quote",example:"<% tp.web.daily_quote() %>"}]},random_picture:{name:"random_picture",description:"Gets a random image from `https://unsplash.com/`.",definition:"tp.web.random_picture(size?: string, query?: string, include_size?: boolean)",args:[{name:"size",description:"Image size in the format `<width>x<height>`."},{name:"query",description:"Limits selection to photos matching a search term. Multiple search terms can be passed separated by a comma."},{name:"include_size",description:"Optional argument to include the specified size in the image link markdown. Defaults to false."}],examples:[{name:"Random picture",example:"<% tp.web.random_picture() %>"},{name:"Random picture with size",example:'<% tp.web.random_picture("200x200") %>'},{name:"Random picture with size and query",example:'<% tp.web.random_picture("200x200", "landscape,water") %>'}]},request:{name:"request",description:"Makes a HTTP request to the specified URL. Optionally, you can specify a path to extract specific data from the response.",definition:"tp.web.request(url: string, path?: string)",args:[{name:"url",description:"The URL to which the HTTP request will be made."},{name:"path",description:"A path within the response JSON to extract specific data."}],examples:[{name:"Simple request",example:'<% tp.web.request("https://jsonplaceholder.typicode.com/todos/1") %>'},{name:"Request with path",example:'<% tp.web.request("https://jsonplaceholder.typicode.com/todos", "0.title") %>'}]}}}},ji={tp:Ho};var Go=["app","config","date","file","frontmatter","hooks","obsidian","system","user","web"],Vo=new Set(Go);function _i(r){return typeof r=="string"&&Vo.has(r)}function vi(r){return!!r.definition}var An=class{constructor(e){this.plugin=e;this.documentation=ji}get_all_modules_documentation(){return Object.values(this.documentation.tp).map(e=>(e.queryKey=e.name,e))}get_all_functions_documentation(e,t){if(e==="app")return this.get_app_functions_documentation(this.plugin.app,t);if(e==="user"){if(!this.plugin.settings||!this.plugin.settings.user_scripts_folder)return;let n=ce(()=>Ie(this.plugin.app,this.plugin.settings.user_scripts_folder),"User Scripts folder doesn't exist");return!n||n.length===0?void 0:n.reduce((o,s)=>s.extension!=="js"?o:[...o,{name:s.basename,queryKey:s.basename,definition:"",description:"",example:""}],[])}if(!!this.documentation.tp[e].functions)return Object.values(this.documentation.tp[e].functions).map(n=>(n.queryKey=n.name,n))}get_app_functions_documentation(e,t){if(!kr(e))return[];let n=t.split(".");if(n.length===0)return[];let o=e;for(let c=0;c<n.length-1;c++){let d=n[c];if(d in o){if(!kr(o[d]))return[];o=o[d]}}let s=["tp","app",...n.slice(0,n.length-1)].join("."),a=n.slice(0,n.length-1).join("."),A=[];for(let c in o){let d=`${s}.${c}`,f=a?`${a}.${c}`:c;A.push({name:c,queryKey:f,definition:typeof o[c]=="function"?`${d}(${Cn(o[c])})`:d,description:"",example:""})}return A}get_module_documentation(e){return this.documentation.tp[e]}get_function_documentation(e,t){return this.documentation.tp[e].functions[t]}get_argument_documentation(e,t,n){let o=this.get_function_documentation(e,t);return!o||!o.args?null:o.args[n]}};var un=class extends wi.EditorSuggest{constructor(e){super(e.app);this.tp_keyword_regex=/tp\.(?<module>[a-z]*)?(?<fn_trigger>\.(?<fn>[a-zA-Z_.]*)?)?$/;this.documentation=new An(e)}onTrigger(e,t,n){let o=t.getRange({line:e.line,ch:0},{line:e.line,ch:e.ch}),s=this.tp_keyword_regex.exec(o);if(!s)return null;let a,A=s.groups&&s.groups.module||"";if(this.module_name=A,s.groups&&s.groups.fn_trigger){if(A==""||!_i(A))return null;this.function_trigger=!0,this.function_name=s.groups.fn||"",a=this.function_name}else this.function_trigger=!1,a=this.module_name;let c={start:{line:e.line,ch:e.ch-a.length},end:{line:e.line,ch:e.ch},query:a};return this.latest_trigger_info=c,c}getSuggestions(e){let t;return this.module_name&&this.function_trigger?t=this.documentation.get_all_functions_documentation(this.module_name,this.function_name):t=this.documentation.get_all_modules_documentation(),t?t.filter(n=>n.queryKey.toLowerCase().startsWith(e.query.toLowerCase())):[]}renderSuggestion(e,t){t.createEl("b",{text:e.name}),t.createEl("br"),this.function_trigger&&vi(e)&&t.createEl("code",{text:e.definition}),e.description&&t.createEl("div",{text:e.description})}selectSuggestion(e,t){let n=this.app.workspace.activeEditor;if(!(!n||!n.editor)&&(n.editor.replaceRange(e.queryKey,this.latest_trigger_info.start,this.latest_trigger_info.end),this.latest_trigger_info.start.ch==this.latest_trigger_info.end.ch)){let o=this.latest_trigger_info.end;o.ch+=e.queryKey.length,n.editor.setCursor(o)}}};(function(r){r(window.CodeMirror)})(function(r){"use strict";r.defineMode("javascript",function(e,t){var n=e.indentUnit,o=t.statementIndent,s=t.jsonld,a=t.json||s,A=t.trackScope!==!1,c=t.typescript,d=t.wordCharacters||/[\w$\xa1-\uffff]/,f=function(){function i(pe){return{type:pe,style:"keyword"}}var l=i("keyword a"),u=i("keyword b"),g=i("keyword c"),E=i("keyword d"),q=i("operator"),$={type:"atom",style:"atom"};return{if:i("if"),while:l,with:l,else:u,do:u,try:u,finally:u,return:E,break:E,continue:E,new:i("new"),delete:g,void:g,throw:g,debugger:i("debugger"),var:i("var"),const:i("var"),let:i("var"),function:i("function"),catch:i("catch"),for:i("for"),switch:i("switch"),case:i("case"),default:i("default"),in:q,typeof:q,instanceof:q,true:$,false:$,null:$,undefined:$,NaN:$,Infinity:$,this:i("this"),class:i("class"),super:i("atom"),yield:g,export:i("export"),import:i("import"),extends:g,await:g}}(),b=/[+\-*&%=<>!?|~^@]/,k=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/;function x(i){for(var l=!1,u,g=!1;(u=i.next())!=null;){if(!l){if(u=="/"&&!g)return;u=="["?g=!0:g&&u=="]"&&(g=!1)}l=!l&&u=="\\"}}var O,P;function j(i,l,u){return O=i,P=u,l}function B(i,l){var u=i.next();if(u=='"'||u=="'")return l.tokenize=Y(u),l.tokenize(i,l);if(u=="."&&i.match(/^\d[\d_]*(?:[eE][+\-]?[\d_]+)?/))return j("number","number");if(u=="."&&i.match(".."))return j("spread","meta");if(/[\[\]{}\(\),;\:\.]/.test(u))return j(u);if(u=="="&&i.eat(">"))return j("=>","operator");if(u=="0"&&i.match(/^(?:x[\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/))return j("number","number");if(/\d/.test(u))return i.match(/^[\d_]*(?:n|(?:\.[\d_]*)?(?:[eE][+\-]?[\d_]+)?)?/),j("number","number");if(u=="/")return i.eat("*")?(l.tokenize=N,N(i,l)):i.eat("/")?(i.skipToEnd(),j("comment","comment")):Tn(i,l,1)?(x(i),i.match(/^\b(([gimyus])(?![gimyus]*\2))+\b/),j("regexp","string-2")):(i.eat("="),j("operator","operator",i.current()));if(u=="`")return l.tokenize=T,T(i,l);if(u=="#"&&i.peek()=="!")return i.skipToEnd(),j("meta","meta");if(u=="#"&&i.eatWhile(d))return j("variable","property");if(u=="<"&&i.match("!--")||u=="-"&&i.match("->")&&!/\S/.test(i.string.slice(0,i.start)))return i.skipToEnd(),j("comment","comment");if(b.test(u))return(u!=">"||!l.lexical||l.lexical.type!=">")&&(i.eat("=")?(u=="!"||u=="=")&&i.eat("="):/[<>*+\-|&?]/.test(u)&&(i.eat(u),u==">"&&i.eat(u))),u=="?"&&i.eat(".")?j("."):j("operator","operator",i.current());if(d.test(u)){i.eatWhile(d);var g=i.current();if(l.lastType!="."){if(f.propertyIsEnumerable(g)){var E=f[g];return j(E.type,E.style,g)}if(g=="async"&&i.match(/^(\s|\/\*([^*]|\*(?!\/))*?\*\/)*[\[\(\w]/,!1))return j("async","keyword",g)}return j("variable","variable",g)}}function Y(i){return function(l,u){var g=!1,E;if(s&&l.peek()=="@"&&l.match(k))return u.tokenize=B,j("jsonld-keyword","meta");for(;(E=l.next())!=null&&!(E==i&&!g);)g=!g&&E=="\\";return g||(u.tokenize=B),j("string","string")}}function N(i,l){for(var u=!1,g;g=i.next();){if(g=="/"&&u){l.tokenize=B;break}u=g=="*"}return j("comment","comment")}function T(i,l){for(var u=!1,g;(g=i.next())!=null;){if(!u&&(g=="`"||g=="$"&&i.eat("{"))){l.tokenize=B;break}u=!u&&g=="\\"}return j("quasi","string-2",i.current())}var I="([{}])";function C(i,l){l.fatArrowAt&&(l.fatArrowAt=null);var u=i.string.indexOf("=>",i.start);if(!(u<0)){if(c){var g=/:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(i.string.slice(i.start,u));g&&(u=g.index)}for(var E=0,q=!1,$=u-1;$>=0;--$){var pe=i.string.charAt($),Oe=I.indexOf(pe);if(Oe>=0&&Oe<3){if(!E){++$;break}if(--E==0){pe=="("&&(q=!0);break}}else if(Oe>=3&&Oe<6)++E;else if(d.test(pe))q=!0;else if(/["'\/`]/.test(pe))for(;;--$){if($==0)return;var Li=i.string.charAt($-1);if(Li==pe&&i.string.charAt($-2)!="\\"){$--;break}}else if(q&&!E){++$;break}}q&&!E&&(l.fatArrowAt=$)}}var R={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,this:!0,import:!0,"jsonld-keyword":!0};function z(i,l,u,g,E,q){this.indented=i,this.column=l,this.type=u,this.prev=E,this.info=q,g!=null&&(this.align=g)}function J(i,l){if(!A)return!1;for(var u=i.localVars;u;u=u.next)if(u.name==l)return!0;for(var g=i.context;g;g=g.prev)for(var u=g.vars;u;u=u.next)if(u.name==l)return!0}function G(i,l,u,g,E){var q=i.cc;for(m.state=i,m.stream=E,m.marked=null,m.cc=q,m.style=l,i.lexical.hasOwnProperty("align")||(i.lexical.align=!0);;){var $=q.length?q.pop():a?S:H;if($(u,g)){for(;q.length&&q[q.length-1].lex;)q.pop()();return m.marked?m.marked:u=="variable"&&J(i,g)?"variable-2":l}}}var m={state:null,column:null,marked:null,cc:null};function h(){for(var i=arguments.length-1;i>=0;i--)m.cc.push(arguments[i])}function p(){return h.apply(null,arguments),!0}function we(i,l){for(var u=l;u;u=u.next)if(u.name==i)return!0;return!1}function me(i){var l=m.state;if(m.marked="def",!!A){if(l.context){if(l.lexical.info=="var"&&l.context&&l.context.block){var u=oe(i,l.context);if(u!=null){l.context=u;return}}else if(!we(i,l.localVars)){l.localVars=new fe(i,l.localVars);return}}t.globalVars&&!we(i,l.globalVars)&&(l.globalVars=new fe(i,l.globalVars))}}function oe(i,l){if(l)if(l.block){var u=oe(i,l.prev);return u?u==l.prev?l:new Pe(u,l.vars,!0):null}else return we(i,l.vars)?l:new Pe(l.prev,new fe(i,l.vars),!1);else return null}function se(i){return i=="public"||i=="private"||i=="protected"||i=="abstract"||i=="readonly"}function Pe(i,l,u){this.prev=i,this.vars=l,this.block=u}function fe(i,l){this.name=i,this.next=l}var _t=new fe("this",new fe("arguments",null));function Be(){m.state.context=new Pe(m.state.context,m.state.localVars,!1),m.state.localVars=_t}function Ne(){m.state.context=new Pe(m.state.context,m.state.localVars,!0),m.state.localVars=null}function ae(){m.state.localVars=m.state.context.vars,m.state.context=m.state.context.prev}ae.lex=!0;function y(i,l){var u=function(){var g=m.state,E=g.indented;if(g.lexical.type=="stat")E=g.lexical.indented;else for(var q=g.lexical;q&&q.type==")"&&q.align;q=q.prev)E=q.indented;g.lexical=new z(E,m.stream.column(),i,null,g.lexical,l)};return u.lex=!0,u}function _(){var i=m.state;i.lexical.prev&&(i.lexical.type==")"&&(i.indented=i.lexical.indented),i.lexical=i.lexical.prev)}_.lex=!0;function F(i){function l(u){return u==i?p():i==";"||u=="}"||u==")"||u=="]"?h():p(l)}return l}function H(i,l){return i=="var"?p(y("vardef",l),xr,F(";"),_):i=="keyword a"?p(y("form"),it,H,_):i=="keyword b"?p(y("form"),H,_):i=="keyword d"?m.stream.match(/^\s*$/,!1)?p():p(y("stat"),Le,F(";"),_):i=="debugger"?p(F(";")):i=="{"?p(y("}"),Ne,tr,_,ae):i==";"?p():i=="if"?(m.state.lexical.info=="else"&&m.state.cc[m.state.cc.length-1]==_&&m.state.cc.pop()(),p(y("form"),it,H,_,vn)):i=="function"?p(Ze):i=="for"?p(y("form"),Ne,wn,H,ae,_):i=="class"||c&&l=="interface"?(m.marked="keyword",p(y("form",i=="class"?i:l),yn,_)):i=="variable"?c&&l=="declare"?(m.marked="keyword",p(H)):c&&(l=="module"||l=="enum"||l=="type")&&m.stream.match(/^\s*\w/,!1)?(m.marked="keyword",l=="enum"?p(kn):l=="type"?p(bn,F("operator"),D,F(";")):p(y("form"),be,F("{"),y("}"),tr,_,_)):c&&l=="namespace"?(m.marked="keyword",p(y("form"),S,H,_)):c&&l=="abstract"?(m.marked="keyword",p(H)):p(y("stat"),Zt):i=="switch"?p(y("form"),it,F("{"),y("}","switch"),Ne,tr,_,_,ae):i=="case"?p(S,F(":")):i=="default"?p(F(":")):i=="catch"?p(y("form"),Be,Re,H,_,ae):i=="export"?p(y("stat"),Ii,_):i=="import"?p(y("stat"),Si,_):i=="async"?p(H):l=="@"?p(S,H):h(y("stat"),S,F(";"),_)}function Re(i){if(i=="(")return p(at,F(")"))}function S(i,l){return Ut(i,l,!1)}function le(i,l){return Ut(i,l,!0)}function it(i){return i!="("?h():p(y(")"),Le,F(")"),_)}function Ut(i,l,u){if(m.state.fatArrowAt==m.stream.start){var g=u?Xt:Jt;if(i=="(")return p(Be,y(")"),X(at,")"),_,F("=>"),g,ae);if(i=="variable")return h(Be,be,F("=>"),g,ae)}var E=u?Xe:Ye;return R.hasOwnProperty(i)?p(E):i=="function"?p(Ze,E):i=="class"||c&&l=="interface"?(m.marked="keyword",p(y("form"),Ci,_)):i=="keyword c"||i=="async"?p(u?le:S):i=="("?p(y(")"),Le,F(")"),_,E):i=="operator"||i=="spread"?p(u?le:S):i=="["?p(y("]"),$i,_,E):i=="{"?Ct(st,"}",null,E):i=="quasi"?h(ot,E):i=="new"?p(He(u)):p()}function Le(i){return i.match(/[;\}\)\],]/)?h():h(S)}function Ye(i,l){return i==","?p(Le):Xe(i,l,!1)}function Xe(i,l,u){var g=u==!1?Ye:Xe,E=u==!1?S:le;if(i=="=>")return p(Be,u?Xt:Jt,ae);if(i=="operator")return/\+\+|--/.test(l)||c&&l=="!"?p(g):c&&l=="<"&&m.stream.match(/^([^<>]|<[^<>]*>)*>\s*\(/,!1)?p(y(">"),X(D,">"),_,g):l=="?"?p(S,F(":"),E):p(E);if(i=="quasi")return h(ot,g);if(i!=";"){if(i=="(")return Ct(le,")","call",g);if(i==".")return p(Ot,g);if(i=="[")return p(y("]"),Le,F("]"),_,g);if(c&&l=="as")return m.marked="keyword",p(D,g);if(i=="regexp")return m.state.lastType=m.marked="operator",m.stream.backUp(m.stream.pos-m.stream.start-1),p(E)}}function ot(i,l){return i!="quasi"?h():l.slice(l.length-2)!="${"?p(ot):p(Le,zt)}function zt(i){if(i=="}")return m.marked="string-2",m.state.tokenize=T,p(ot)}function Jt(i){return C(m.stream,m.state),h(i=="{"?H:S)}function Xt(i){return C(m.stream,m.state),h(i=="{"?H:le)}function He(i){return function(l){return l=="."?p(i?Qt:vt):l=="variable"&&c?p(Mi,i?Xe:Ye):h(i?le:S)}}function vt(i,l){if(l=="target")return m.marked="keyword",p(Ye)}function Qt(i,l){if(l=="target")return m.marked="keyword",p(Xe)}function Zt(i){return i==":"?p(_,H):h(Ye,F(";"),_)}function Ot(i){if(i=="variable")return m.marked="property",p()}function st(i,l){if(i=="async")return m.marked="property",p(st);if(i=="variable"||m.style=="keyword"){if(m.marked="property",l=="get"||l=="set")return p(er);var u;return c&&m.state.fatArrowAt==m.stream.start&&(u=m.stream.match(/^\s*:\s*/,!1))&&(m.state.fatArrowAt=m.stream.pos+u[0].length),p(qe)}else{if(i=="number"||i=="string")return m.marked=s?"property":m.style+" property",p(qe);if(i=="jsonld-keyword")return p(qe);if(c&&se(l))return m.marked="keyword",p(st);if(i=="[")return p(S,wt,F("]"),qe);if(i=="spread")return p(le,qe);if(l=="*")return m.marked="keyword",p(st);if(i==":")return h(qe)}}function er(i){return i!="variable"?h(qe):(m.marked="property",p(Ze))}function qe(i){if(i==":")return p(le);if(i=="(")return h(Ze)}function X(i,l,u){function g(E,q){if(u?u.indexOf(E)>-1:E==","){var $=m.state.lexical;return $.info=="call"&&($.pos=($.pos||0)+1),p(function(pe,Oe){return pe==l||Oe==l?h():h(i)},g)}return E==l||q==l?p():u&&u.indexOf(";")>-1?h(i):p(F(l))}return function(E,q){return E==l||q==l?p():h(i,g)}}function Ct(i,l,u){for(var g=3;g<arguments.length;g++)m.cc.push(arguments[g]);return p(y(l,u),X(i,l),_)}function tr(i){return i=="}"?p():h(H,tr)}function wt(i,l){if(c){if(i==":")return p(D);if(l=="?")return p(wt)}}function Ei(i,l){if(c&&(i==":"||l=="in"))return p(D)}function jn(i){if(c&&i==":")return m.stream.match(/^\s*\w+\s+is\b/,!1)?p(S,ki,D):p(D)}function ki(i,l){if(l=="is")return m.marked="keyword",p()}function D(i,l){if(l=="keyof"||l=="typeof"||l=="infer"||l=="readonly")return m.marked="keyword",p(l=="typeof"?le:D);if(i=="variable"||l=="void")return m.marked="type",p(Ge);if(l=="|"||l=="&")return p(D);if(i=="string"||i=="number"||i=="atom")return p(Ge);if(i=="[")return p(y("]"),X(D,"]",","),_,Ge);if(i=="{")return p(y("}"),wr,_,Ge);if(i=="(")return p(X(yr,")"),Ti,Ge);if(i=="<")return p(X(D,">"),D);if(i=="quasi")return h(br,Ge)}function Ti(i){if(i=="=>")return p(D)}function wr(i){return i.match(/[\}\)\]]/)?p():i==","||i==";"?p(wr):h(It,wr)}function It(i,l){if(i=="variable"||m.style=="keyword")return m.marked="property",p(It);if(l=="?"||i=="number"||i=="string")return p(It);if(i==":")return p(D);if(i=="[")return p(F("variable"),Ei,F("]"),It);if(i=="(")return h(yt,It);if(!i.match(/[;\}\)\],]/))return p()}function br(i,l){return i!="quasi"?h():l.slice(l.length-2)!="${"?p(br):p(D,Fi)}function Fi(i){if(i=="}")return m.marked="string-2",m.state.tokenize=T,p(br)}function yr(i,l){return i=="variable"&&m.stream.match(/^\s*[?:]/,!1)||l=="?"?p(yr):i==":"?p(D):i=="spread"?p(yr):h(D)}function Ge(i,l){if(l=="<")return p(y(">"),X(D,">"),_,Ge);if(l=="|"||i=="."||l=="&")return p(D);if(i=="[")return p(D,F("]"),Ge);if(l=="extends"||l=="implements")return m.marked="keyword",p(D);if(l=="?")return p(D,F(":"),D)}function Mi(i,l){if(l=="<")return p(y(">"),X(D,">"),_,Ge)}function rr(){return h(D,Pi)}function Pi(i,l){if(l=="=")return p(D)}function xr(i,l){return l=="enum"?(m.marked="keyword",p(kn)):h(be,wt,Qe,qi)}function be(i,l){if(c&&se(l))return m.marked="keyword",p(be);if(i=="variable")return me(l),p();if(i=="spread")return p(be);if(i=="[")return Ct(Bi,"]");if(i=="{")return Ct(_n,"}")}function _n(i,l){return i=="variable"&&!m.stream.match(/^\s*:/,!1)?(me(l),p(Qe)):(i=="variable"&&(m.marked="property"),i=="spread"?p(be):i=="}"?h():i=="["?p(S,F("]"),F(":"),_n):p(F(":"),be,Qe))}function Bi(){return h(be,Qe)}function Qe(i,l){if(l=="=")return p(le)}function qi(i){if(i==",")return p(xr)}function vn(i,l){if(i=="keyword b"&&l=="else")return p(y("form","else"),H,_)}function wn(i,l){if(l=="await")return p(wn);if(i=="(")return p(y(")"),Oi,_)}function Oi(i){return i=="var"?p(xr,bt):i=="variable"?p(bt):h(bt)}function bt(i,l){return i==")"?p():i==";"?p(bt):l=="in"||l=="of"?(m.marked="keyword",p(S,bt)):h(S,bt)}function Ze(i,l){if(l=="*")return m.marked="keyword",p(Ze);if(i=="variable")return me(l),p(Ze);if(i=="(")return p(Be,y(")"),X(at,")"),_,jn,H,ae);if(c&&l=="<")return p(y(">"),X(rr,">"),_,Ze)}function yt(i,l){if(l=="*")return m.marked="keyword",p(yt);if(i=="variable")return me(l),p(yt);if(i=="(")return p(Be,y(")"),X(at,")"),_,jn,ae);if(c&&l=="<")return p(y(">"),X(rr,">"),_,yt)}function bn(i,l){if(i=="keyword"||i=="variable")return m.marked="type",p(bn);if(l=="<")return p(y(">"),X(rr,">"),_)}function at(i,l){return l=="@"&&p(S,at),i=="spread"?p(at):c&&se(l)?(m.marked="keyword",p(at)):c&&i=="this"?p(wt,Qe):h(be,wt,Qe)}function Ci(i,l){return i=="variable"?yn(i,l):nr(i,l)}function yn(i,l){if(i=="variable")return me(l),p(nr)}function nr(i,l){if(l=="<")return p(y(">"),X(rr,">"),_,nr);if(l=="extends"||l=="implements"||c&&i==",")return l=="implements"&&(m.marked="keyword"),p(c?D:S,nr);if(i=="{")return p(y("}"),Ve,_)}function Ve(i,l){if(i=="async"||i=="variable"&&(l=="static"||l=="get"||l=="set"||c&&se(l))&&m.stream.match(/^\s+[\w$\xa1-\uffff]/,!1))return m.marked="keyword",p(Ve);if(i=="variable"||m.style=="keyword")return m.marked="property",p(St,Ve);if(i=="number"||i=="string")return p(St,Ve);if(i=="[")return p(S,wt,F("]"),St,Ve);if(l=="*")return m.marked="keyword",p(Ve);if(c&&i=="(")return h(yt,Ve);if(i==";"||i==",")return p(Ve);if(i=="}")return p();if(l=="@")return p(S,Ve)}function St(i,l){if(l=="!"||l=="?")return p(St);if(i==":")return p(D,Qe);if(l=="=")return p(le);var u=m.state.lexical.prev,g=u&&u.info=="interface";return h(g?yt:Ze)}function Ii(i,l){return l=="*"?(m.marked="keyword",p(Er,F(";"))):l=="default"?(m.marked="keyword",p(S,F(";"))):i=="{"?p(X(xn,"}"),Er,F(";")):h(H)}function xn(i,l){if(l=="as")return m.marked="keyword",p(F("variable"));if(i=="variable")return h(le,xn)}function Si(i){return i=="string"?p():i=="("?h(S):i=="."?h(Ye):h(ir,En,Er)}function ir(i,l){return i=="{"?Ct(ir,"}"):(i=="variable"&&me(l),l=="*"&&(m.marked="keyword"),p(Di))}function En(i){if(i==",")return p(ir,En)}function Di(i,l){if(l=="as")return m.marked="keyword",p(ir)}function Er(i,l){if(l=="from")return m.marked="keyword",p(S)}function $i(i){return i=="]"?p():h(X(le,"]"))}function kn(){return h(y("form"),be,F("{"),y("}"),X(Ni,"}"),_,_)}function Ni(){return h(be,Qe)}function Ri(i,l){return i.lastType=="operator"||i.lastType==","||b.test(l.charAt(0))||/[,.]/.test(l.charAt(0))}function Tn(i,l,u){return l.tokenize==B&&/^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\[{}\(,;:]|=>)$/.test(l.lastType)||l.lastType=="quasi"&&/\{\s*$/.test(i.string.slice(0,i.pos-(u||0)))}return{startState:function(i){var l={tokenize:B,lastType:"sof",cc:[],lexical:new z((i||0)-n,0,"block",!1),localVars:t.localVars,context:t.localVars&&new Pe(null,null,!1),indented:i||0};return t.globalVars&&typeof t.globalVars=="object"&&(l.globalVars=t.globalVars),l},token:function(i,l){if(i.sol()&&(l.lexical.hasOwnProperty("align")||(l.lexical.align=!1),l.indented=i.indentation(),C(i,l)),l.tokenize!=N&&i.eatSpace())return null;var u=l.tokenize(i,l);return O=="comment"?u:(l.lastType=O=="operator"&&(P=="++"||P=="--")?"incdec":O,G(l,u,O,P,i))},indent:function(i,l){if(i.tokenize==N||i.tokenize==T)return r.Pass;if(i.tokenize!=B)return 0;var u=l&&l.charAt(0),g=i.lexical,E;if(!/^\s*else\b/.test(l))for(var q=i.cc.length-1;q>=0;--q){var $=i.cc[q];if($==_)g=g.prev;else if($!=vn&&$!=ae)break}for(;(g.type=="stat"||g.type=="form")&&(u=="}"||(E=i.cc[i.cc.length-1])&&(E==Ye||E==Xe)&&!/^[,\.=+\-*:?[\(]/.test(l));)g=g.prev;o&&g.type==")"&&g.prev.type=="stat"&&(g=g.prev);var pe=g.type,Oe=u==pe;return pe=="vardef"?g.indented+(i.lastType=="operator"||i.lastType==","?g.info.length+1:0):pe=="form"&&u=="{"?g.indented:pe=="form"?g.indented+n:pe=="stat"?g.indented+(Ri(i,l)?o||n:0):g.info=="switch"&&!Oe&&t.doubleIndentSwitch!=!1?g.indented+(/^(?:case|default)\b/.test(l)?n:2*n):g.align?g.column+(Oe?0:1):g.indented+(Oe?0:n)},electricInput:/^\s*(?:case .*?:|default:|\{|\})$/,blockCommentStart:a?null:"/*",blockCommentEnd:a?null:"*/",blockCommentContinue:a?null:" * ",lineComment:a?null:"//",fold:"brace",closeBrackets:"()[]{}''\"\"``",helperType:a?"json":"javascript",jsonldMode:s,jsonMode:a,expressionAllowed:Tn,skipExpression:function(i){G(i,"atom","atom","true",new r.StringStream("",2,null))}}}),r.registerHelper("wordChars","javascript",/[\w$]/),r.defineMIME("text/javascript","javascript"),r.defineMIME("text/ecmascript","javascript"),r.defineMIME("application/javascript","javascript"),r.defineMIME("application/x-javascript","javascript"),r.defineMIME("application/ecmascript","javascript"),r.defineMIME("application/json",{name:"javascript",json:!0}),r.defineMIME("application/x-json",{name:"javascript",json:!0}),r.defineMIME("application/manifest+json",{name:"javascript",json:!0}),r.defineMIME("application/ld+json",{name:"javascript",jsonld:!0}),r.defineMIME("text/typescript",{name:"javascript",typescript:!0}),r.defineMIME("application/typescript",{name:"javascript",typescript:!0})});(function(r){r(window.CodeMirror)})(function(r){"use strict";r.customOverlayMode=function(e,t,n){return{startState:function(){return{base:r.startState(e),overlay:r.startState(t),basePos:0,baseCur:null,overlayPos:0,overlayCur:null,streamSeen:null}},copyState:function(o){return{base:r.copyState(e,o.base),overlay:r.copyState(t,o.overlay),basePos:o.basePos,baseCur:null,overlayPos:o.overlayPos,overlayCur:null}},token:function(o,s){return(o!=s.streamSeen||Math.min(s.basePos,s.overlayPos)<o.start)&&(s.streamSeen=o,s.basePos=s.overlayPos=o.start),o.start==s.basePos&&(s.baseCur=e.token(o,s.base),s.basePos=o.pos),o.start==s.overlayPos&&(o.pos=o.start,s.overlayCur=t.token(o,s.overlay),s.overlayPos=o.pos),o.pos=Math.min(s.basePos,s.overlayPos),s.baseCur&&s.overlayCur&&s.baseCur.contains("line-HyperMD-codeblock")&&(s.overlayCur=s.overlayCur.replace("line-templater-inline",""),s.overlayCur+=" line-background-HyperMD-codeblock-bg"),s.overlayCur==null?s.baseCur:s.baseCur!=null&&s.overlay.combineTokens||n&&s.overlay.combineTokens==null?s.baseCur+" "+s.overlayCur:s.overlayCur},indent:e.indent&&function(o,s,a){return e.indent(o.base,s,a)},electricChars:e.electricChars,innerMode:function(o){return{state:o.base,mode:e}},blankLine:function(o){let s,a;return e.blankLine&&(s=e.blankLine(o.base)),t.blankLine&&(a=t.blankLine(o.overlay)),a==null?s:n&&s!=null?s+" "+a:a}}}});var bi=V(require("@codemirror/language")),yi=V(require("@codemirror/state")),xi="templater",fn="templater-command",dn="templater-inline",Wo="templater-opening-tag",Ko="templater-closing-tag",Uo="templater-interpolation-tag",zo="templater-execution-tag",gn=class{constructor(e){this.plugin=e;this.cursor_jumper=new cn(e.app),this.activeEditorExtensions=[]}desktopShouldHighlight(){return mn.Platform.isDesktopApp&&this.plugin.settings.syntax_highlighting}mobileShouldHighlight(){return mn.Platform.isMobile&&this.plugin.settings.syntax_highlighting_mobile}async setup(){this.plugin.registerEditorSuggest(new un(this.plugin)),await this.registerCodeMirrorMode(),this.templaterLanguage=yi.Prec.high(bi.StreamLanguage.define(window.CodeMirror.getMode({},xi))),this.templaterLanguage===void 0&&K(new w("Unable to enable syntax highlighting. Could not define language.")),this.plugin.registerEditorExtension(this.activeEditorExtensions),(this.desktopShouldHighlight()||this.mobileShouldHighlight())&&await this.enable_highlighter()}async enable_highlighter(){this.activeEditorExtensions.length===0&&this.templaterLanguage&&(this.activeEditorExtensions.push(this.templaterLanguage),this.plugin.app.workspace.updateOptions())}async disable_highlighter(){this.activeEditorExtensions.length>0&&(this.activeEditorExtensions.pop(),this.plugin.app.workspace.updateOptions())}async jump_to_next_cursor_location(e=null,t=!1){t&&!this.plugin.settings.auto_jump_to_cursor||e&&xt(this.plugin.app)!==e||await this.cursor_jumper.jump_to_next_cursor_location()}async registerCodeMirrorMode(){if(!this.desktopShouldHighlight()&&!this.mobileShouldHighlight())return;let e=window.CodeMirror.getMode({},"javascript");if(e.name==="null"){K(new w("Javascript syntax mode couldn't be found, can't enable syntax highlighting."));return}let t=window.CodeMirror.customOverlayMode;if(t==null){K(new w("Couldn't find customOverlayMode, can't enable syntax highlighting."));return}window.CodeMirror.defineMode(xi,function(n){let o={startState:function(){return{...window.CodeMirror.startState(e),inCommand:!1,tag_class:"",freeLine:!1}},copyState:function(s){return{...window.CodeMirror.startState(e),inCommand:s.inCommand,tag_class:s.tag_class,freeLine:s.freeLine}},blankLine:function(s){return s.inCommand?"line-background-templater-command-bg":null},token:function(s,a){if(s.sol()&&a.inCommand&&(a.freeLine=!0),a.inCommand){let c="";if(s.match(/[-_]{0,1}%>/,!0)){a.inCommand=!1,a.freeLine=!1;let f=a.tag_class;return a.tag_class="",`line-${dn} ${fn} ${Ko} ${f}`}let d=e.token&&e.token(s,a);return s.peek()==null&&a.freeLine&&(c+=" line-background-templater-command-bg"),a.freeLine||(c+=` line-${dn}`),`${c} ${fn} ${d}`}let A=s.match(/<%[-_]{0,1}\s*([*+]{0,1})/,!0);if(A!=null){switch(A[1]){case"*":a.tag_class=zo;break;default:a.tag_class=Uo;break}return a.inCommand=!0,`line-${dn} ${fn} ${Wo} ${a.tag_class}`}for(;s.next()!=null&&!s.match(/<%/,!1););return null}};return t(window.CodeMirror.getMode(n,"hypermd"),o)})}};var hn=class extends vr.Plugin{async onload(){await this.load_settings(),this.templater=new Kt(this),await this.templater.setup(),this.editor_handler=new gn(this),await this.editor_handler.setup(),this.fuzzy_suggester=new Yr(this),this.event_handler=new _r(this,this.templater,this.settings),this.event_handler.setup(),this.command_handler=new ln(this),this.command_handler.setup(),(0,vr.addIcon)("templater-icon",li),this.addRibbonIcon("templater-icon","Templater",async()=>{this.fuzzy_suggester.insert_template()}).setAttribute("id","rb-templater-icon"),this.addSettingTab(new Lr(this)),this.app.workspace.onLayoutReady(()=>{this.templater.execute_startup_scripts()})}onunload(){this.templater.functions_generator.teardown()}async save_settings(){await this.saveData(this.settings)}async load_settings(){this.settings=Object.assign({},si,await this.loadData())}};
