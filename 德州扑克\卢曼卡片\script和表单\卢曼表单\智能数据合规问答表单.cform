{"id": "smart-data-compliance-qa-form", "fields": [{"id": "scenario", "label": "业务场景类型", "type": "select", "options": [{"id": "ecommerce", "label": "电商平台", "value": "电商平台"}, {"id": "fintech", "label": "金融科技", "value": "金融科技"}, {"id": "healthcare", "label": "医疗健康", "value": "医疗健康"}, {"id": "education", "label": "在线教育", "value": "在线教育"}, {"id": "social-media", "label": "社交媒体", "value": "社交媒体"}, {"id": "iot", "label": "物联网", "value": "物联网"}, {"id": "ai-ml", "label": "AI/机器学习", "value": "AI/机器学习"}, {"id": "custom", "label": "其他场景", "value": "custom"}], "description": "选择具体的业务场景类型"}, {"id": "customScenario", "label": "自定义场景（如选择其他）", "type": "text", "description": "当选择其他场景时填写具体内容"}, {"id": "dataUsage", "label": "数据利用需求", "type": "select", "options": [{"id": "personalization", "label": "个性化推荐", "value": "个性化推荐"}, {"id": "user-profiling", "label": "用户画像分析", "value": "用户画像分析"}, {"id": "marketing", "label": "精准营销", "value": "精准营销"}, {"id": "risk-control", "label": "风险控制", "value": "风险控制"}, {"id": "product-optimization", "label": "产品优化", "value": "产品优化"}, {"id": "business-intelligence", "label": "商业智能分析", "value": "商业智能分析"}, {"id": "cross-border-service", "label": "跨境服务", "value": "跨境服务"}], "description": "选择主要的数据利用需求"}, {"id": "privacyRequirement", "label": "隐私保护要求", "type": "select", "options": [{"id": "user-consent", "label": "用户明确同意", "value": "用户明确同意"}, {"id": "data-minimization", "label": "数据最小化处理", "value": "数据最小化处理"}, {"id": "purpose-limitation", "label": "目的限制原则", "value": "目的限制原则"}, {"id": "anonymization", "label": "匿名化处理", "value": "匿名化处理"}, {"id": "retention-limit", "label": "数据保留期限", "value": "数据保留期限"}, {"id": "cross-border-protection", "label": "跨境传输保护", "value": "跨境传输保护"}, {"id": "user-rights", "label": "用户权利保障", "value": "用户权利保障"}], "description": "选择主要的隐私保护要求"}, {"id": "legalBasis", "label": "法规依据", "type": "select", "options": [{"id": "pipl-art13", "label": "PIPL第13条（告知同意）", "value": "PIPL第13条（告知同意）"}, {"id": "pipl-art14", "label": "PIPL第14条（敏感信息）", "value": "PIPL第14条（敏感信息）"}, {"id": "pipl-art38", "label": "PIPL第38条（跨境传输）", "value": "PIPL第38条（跨境传输）"}, {"id": "gdpr-art6", "label": "GDPR第6条（处理合法性）", "value": "GDPR第6条（处理合法性）"}, {"id": "gdpr-art7", "label": "GDPR第7条（同意条件）", "value": "GDPR第7条（同意条件）"}, {"id": "gdpr-art25", "label": "GDPR第25条（隐私设计）", "value": "GDPR第25条（隐私设计）"}, {"id": "dsl", "label": "数据安全法相关条款", "value": "数据安全法相关条款"}], "description": "选择主要的法规依据"}, {"id": "interpretation", "label": "我的解读", "type": "textarea", "rows": 3, "description": "用技术/管理手段重构的解读"}, {"id": "operationGuide", "label": "操作指南", "type": "textarea", "rows": 4, "description": "具体到可执行的合规checklist"}, {"id": "relatedRegulations", "label": "相关法条解读", "type": "text", "description": "相关法条解读的链接"}, {"id": "similarScenarios", "label": "类似场景处理", "type": "text", "description": "类似场景处理的链接"}, {"id": "riskWarning", "label": "风险提醒类型", "type": "select", "options": [{"id": "regulatory-trend", "label": "监管趋势变化", "value": "监管趋势变化"}, {"id": "enforcement-risk", "label": "执法风险加强", "value": "执法风险加强"}, {"id": "technical-gap", "label": "技术实现缺陷", "value": "技术实现缺陷"}, {"id": "process-loophole", "label": "流程管理漏洞", "value": "流程管理漏洞"}, {"id": "cross-jurisdiction", "label": "跨司法管辖风险", "value": "跨司法管辖风险"}], "description": "选择主要的风险提醒类型"}, {"id": "riskDetail", "label": "具体风险描述", "type": "textarea", "rows": 2, "description": "详细描述可能忽略的风险点"}], "action": {"type": "runScript", "scriptSource": "inline", "code": "async function entry() {\n  const { form, app, requestUrl, Notice } = this.$context;\n  \n  // 调试：打印form对象\n  console.log('Form data:', form);\n  \n  try {\n    // 获取表单数据，提供默认值\n    const scenario = form.scenario || '未填写场景';\n    const customScenario = form.customScenario || '';\n    const dataUsage = form.dataUsage || '未填写数据用途';\n    const privacyRequirement = form.privacyRequirement || '未填写隐私要求';\n    const legalBasis = form.legalBasis || '未填写法规依据';\n    const interpretation = form.interpretation || '未填写解读';\n    const operationGuide = form.operationGuide || '未填写操作指南';\n    const relatedRegulations = form.relatedRegulations || '相关法条';\n    const similarScenarios = form.similarScenarios || '类似场景';\n    const riskWarning = form.riskWarning || '未填写风险类型';\n    const riskDetail = form.riskDetail || '未填写风险详情';\n    \n    const finalScenario = scenario === 'custom' ? customScenario : scenario;\n    \n    const baseTemplate = `### Q：在${finalScenario}中，如何平衡${dataUsage}与${privacyRequirement}？\n\nA：\n- **法规依据**：${legalBasis}\n- **我的解读**：${interpretation}\n- **操作指南**：${operationGuide}\n\n→ 关联：[[${relatedRegulations}]] [[${similarScenarios}]]\n! 风险：当前方案可能忽略${riskWarning} - ${riskDetail}`;\n\n    let aiEnhancedContent = '';\n    const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec';\n    \n    const enhancedPrompt = `你是数据合规领域的专家。请基于以下问答信息，提供深度分析和补充：\n\n原始问答：\n${baseTemplate}\n\n请你作为专业的合规顾问，与这个问答进行认知碰撞，提供你独特的专业视角：\n\n1. **认知挑战**：对当前解读和操作指南的专业评估，是否存在理解偏差？\n2. **深度补充**：补充可能遗漏的合规要点和实施细节\n3. **创新方案**：提供更详细的实施步骤和技术方案\n4. **风险预警**：分析潜在的合规陷阱和应对策略\n5. **版权化建议**：形成具有独特价值的专业建议\n\n要求：\n- 不要简单重复原内容，要有认知碰撞\n- 体现专业深度和独特视角\n- 提供具体可操作的建议\n- 形成有版权价值的专业内容\n\n请以资深合规专家的身份，对这个问答进行深度改造和升华。`;\n    \n    try {\n      const aiResponse = await requestUrl({\n        url: 'https://api.deepseek.com/v1/chat/completions',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: 'deepseek-chat',\n          messages: [{ role: 'user', content: enhancedPrompt }],\n          temperature: 0.8,\n          max_tokens: 2000\n        })\n      });\n      \n      if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n        aiEnhancedContent = aiResponse.json.choices[0].message.content;\n      }\n    } catch (e) {\n      aiEnhancedContent = '(AI认知碰撞暂时不可用，请手动补充专业洞察)';\n    }\n\n    const template = `${baseTemplate}\n\n---\n\n## 🧠 专业认知碰撞与版权化改造\n\n${aiEnhancedContent}\n\n---\n\n## 📝 实践记录\n<!-- 记录实际应用中的效果和调整 -->\n\n\n## 🔄 方案优化\n<!-- 记录方案的持续优化过程 -->\n\n\n## 🏷️ 标签\n#数据合规问答 #${privacyRequirement} #认知碰撞 #版权化方案 #数据合规\n\n---\n*生成时间：${new Date().toLocaleString()} | AI认知碰撞：DeepSeek | 版权化改造完成*`;\n    \n    // 简化文件名格式\n    const today = new Date();\n    const dateStr = today.getDate().toString().padStart(2, '0'); // 只要日期，如 03\n    const timeStr = today.getHours().toString().padStart(2, '0') + today.getMinutes().toString().padStart(2, '0'); // 如 0922\n    const fileName = `合规问答-${finalScenario}-${dateStr}-${timeStr}.md`;\n    const filePath = `工作室/肌肉/生成笔记/数据合规问答/${fileName}`;\n    \n    const folderPath = '工作室/肌肉/生成笔记/数据合规问答';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    const file = await app.vault.create(filePath, template);\n    new Notice(`数据合规问答已创建: ${fileName}`);\n    \n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 数据合规问答已生成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('生成数据合规问答失败:', error);\n    new Notice('生成失败: ' + error.message);\n    return '❌ 生成失败，请检查配置';\n  }\n}\n\nexports.default = {\n  entry: entry\n};"}, "title": "智能数据合规问答表单"}