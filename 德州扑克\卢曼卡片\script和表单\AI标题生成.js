﻿/**
 * AI标题生成脚本
 * 功能：为文章生成多个标题选项
 * 作者：Builder
 * 版本�?.0
 */

/**
 * AI标题生成函数
 * @param {string} token - API密钥
 * @param {string} propertyName - 保存标题的属性名
 * @param {number} titleCount - 生成标题数量，默认为5
 * @param {string} modelType - AI模型类型，默认为'GLM-4-Flash'
 * @returns {Promise<Object>} 标题生成结果
 */
async function aiTitle(token, propertyName, titleCount = 5, modelType = 'GLM-4-Flash') {
    try {
        // 验证参数
        if (!token || !propertyName) {
            throw new Error('缺少必要参数：token, propertyName');
        }

        console.log(`开始生成标题：数量=${titleCount}, 属�?${propertyName}`);
        
        // 获取当前文件内容
        const activeFile = app.workspace.getActiveFile();
        if (!activeFile) {
            throw new Error('请先打开一个文�?);
        }
        
        const content = await app.vault.read(activeFile);
        
        if (!content.trim()) {
            throw new Error('文件内容为空，无法生成标�?);
        }
        
        // 分析内容特征
        const contentAnalysis = analyzeContentForTitle(content);
        
        // 构建标题生成提示
        const prompt = buildTitlePrompt(content, titleCount, contentAnalysis);
        
        // 调用AI API
        const titles = await callTitleAPI(prompt, token, modelType);
        
        // 处理标题
        const processedTitles = processTitles(titles, contentAnalysis);
        
        // 保存标题到属�?        await saveToProperty(activeFile, propertyName, processedTitles);
        
        // 生成标题统计
        const stats = analyzeTitles(processedTitles);
        
        new Notice(`标题生成完成！共生成 ${processedTitles.titles.length} 个标题`);
        
        return {
            success: true,
            titles: processedTitles,
            stats,
            contentAnalysis,
            propertyName
        };
        
    } catch (error) {
        console.error('标题生成失败:', error);
        new Notice(`标题生成失败: ${error.message}`);
        return { success: false, error: error.message };
    }
}

/**
 * 分析内容特征（用于标题生成）
 * @param {string} content - 文章内容
 * @returns {Object} 内容分析结果
 */
function analyzeContentForTitle(content) {
    const analysis = {
        type: 'article',
        keywords: [],
        mainTopic: '',
        tone: 'neutral',
        length: 'medium',
        hasNumbers: false,
        hasQuestions: false,
        isHowTo: false,
        isListArticle: false
    };
    
    // 检测内容类�?    if (content.includes('教程') || content.includes('如何') || content.includes('步骤')) {
        analysis.type = 'tutorial';
        analysis.isHowTo = true;
    } else if (content.includes('分析') || content.includes('研究') || content.includes('报告')) {
        analysis.type = 'analysis';
    } else if (content.includes('评测') || content.includes('测试') || content.includes('对比')) {
        analysis.type = 'review';
    } else if (content.includes('新闻') || content.includes('发布') || content.includes('宣布')) {
        analysis.type = 'news';
    } else if (content.includes('观点') || content.includes('看法') || content.includes('思�?)) {
        analysis.type = 'opinion';
    }
    
    // 检测是否为列表文章
    if (content.includes('1.') || content.includes('第一') || content.includes('首先')) {
        analysis.isListArticle = true;
    }
    
    // 检测数�?    if (/\d+/.test(content)) {
        analysis.hasNumbers = true;
    }
    
    // 检测问�?    if (content.includes('�?) || content.includes('?')) {
        analysis.hasQuestions = true;
    }
    
    // 提取关键词（简单实现）
    const words = content.match(/[\u4e00-\u9fa5]{2,}/g) || [];
    const wordCount = {};
    words.forEach(word => {
        if (word.length >= 2 && word.length <= 6) {
            wordCount[word] = (wordCount[word] || 0) + 1;
        }
    });
    
    analysis.keywords = Object.entries(wordCount)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([word]) => word);
    
    // 确定主题（取最频繁的关键词�?    analysis.mainTopic = analysis.keywords[0] || '文章';
    
    // 检测语�?    if (content.includes('�?) || content.includes('惊人') || content.includes('震撼')) {
        analysis.tone = 'exciting';
    } else if (content.includes('专业') || content.includes('技�?) || content.includes('深度')) {
        analysis.tone = 'professional';
    } else if (content.includes('简�?) || content.includes('轻松') || content.includes('有趣')) {
        analysis.tone = 'casual';
    }
    
    // 检测长�?    if (content.length < 500) {
        analysis.length = 'short';
    } else if (content.length > 2000) {
        analysis.length = 'long';
    }
    
    return analysis;
}

/**
 * 构建标题生成提示
 * @param {string} content - 文章内容
 * @param {number} titleCount - 标题数量
 * @param {Object} analysis - 内容分析
 * @returns {string} 标题生成提示
 */
function buildTitlePrompt(content, titleCount, analysis) {
    let prompt = `请为以下文章生成${titleCount}个不同风格的标题，要求：\n\n`;
    
    // 基础要求
    prompt += `基础要求：\n`;
    prompt += `1. 标题要准确反映文章的核心内容\n`;
    prompt += `2. 标题要吸引读者注意力\n`;
    prompt += `3. 标题长度控制�?-25个字之间\n`;
    prompt += `4. 每个标题风格要有所不同\n`;
    prompt += `5. 避免使用过于夸张或误导性的词汇\n\n`;
    
    // 根据内容类型添加特定要求
    const typeRequirements = {
        'tutorial': '教程类标题应突出实用性和可操作性，可以使用"如何"�?教你"�?步骤"等词�?,
        'analysis': '分析类标题应突出深度和专业性，可以使用"深度分析"�?全面解读"等词�?,
        'review': '评测类标题应突出对比和评价，可以使用"评测"�?对比"�?测试"等词�?,
        'news': '新闻类标题应突出时效性和重要性，可以使用"最�?�?重磅"等词�?,
        'opinion': '观点类标题应突出个人见解，可以使�?我的看法"�?深度思�?等词�?
    };
    
    if (typeRequirements[analysis.type]) {
        prompt += `内容类型要求�?{typeRequirements[analysis.type]}\n\n`;
    }
    
    // 风格要求
    prompt += `标题风格要求（请生成不同风格的标题）：\n`;
    prompt += `1. 直接描述型：直接说明文章内容\n`;
    prompt += `2. 疑问引导型：使用疑问句吸引读者\n`;
    prompt += `3. 数字突出型：突出具体数字或数量\n`;
    prompt += `4. 价值承诺型：承诺读者能获得的价值\n`;
    prompt += `5. 对比冲突型：使用对比或冲突元素\n\n`;
    
    // 关键词提�?    if (analysis.keywords.length > 0) {
        prompt += `重要关键词（请在标题中适当使用）：${analysis.keywords.slice(0, 5).join('�?)}\n\n`;
    }
    
    // 特殊要求
    if (analysis.isHowTo) {
        prompt += `特殊要求：这是一篇教程文章，标题应体现指导性\n`;
    }
    if (analysis.isListArticle) {
        prompt += `特殊要求：这是一篇列表文章，可以在标题中体现条目数量\n`;
    }
    
    prompt += `\n文章内容：\n${content.substring(0, 1000)}${content.length > 1000 ? '...' : ''}\n\n`;
    prompt += `请生�?{titleCount}个不同风格的标题，每个标题单独一行：`;
    
    return prompt;
}

/**
 * 调用标题生成API
 * @param {string} prompt - 标题生成提示
 * @param {string} token - API密钥
 * @param {string} modelType - 模型类型
 * @returns {Promise<string>} 标题生成结果
 */
async function callTitleAPI(prompt, token, modelType) {
    const requestBody = {
        model: modelType,
        messages: [
            {
                role: "system",
                content: "你是一个专业的标题创作专家，擅长为各种类型的文章创作吸引人且准确的标题。你能够分析文章内容的核心要点，并创作出既能准确反映内容又能吸引读者的标题�?
            },
            {
                role: "user",
                content: prompt
            }
        ],
        temperature: 0.8,
        max_tokens: 1000
    };
    
    const response = await fetch('https://open.bigmodel.cn/api/paas/v4/chat/completions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
    }
    
    const data = await response.json();
    return data.choices[0].message.content;
}

/**
 * 处理标题
 * @param {string} titlesString - 原始标题字符�? * @param {Object} analysis - 内容分析
 * @returns {Object} 处理后的标题对象
 */
function processTitles(titlesString, analysis) {
    const lines = titlesString.split('\n').filter(line => line.trim());
    const titles = [];
    
    for (const line of lines) {
        let title = line.trim()
            .replace(/^\d+[.、]\s*/, '') // 移除序号
            .replace(/^[\-\*]\s*/, '') // 移除列表标记
            .replace(/^标题\d*[�?]\s*/i, '') // 移除"标题:"前缀
            .replace(/^[""'']/, '') // 移除引号
            .replace(/[""'']$/, '')
            .trim();
        
        if (title && title.length >= 3 && title.length <= 50) {
            const titleAnalysis = analyzeSingleTitle(title);
            titles.push({
                title,
                length: title.length,
                type: titleAnalysis.type,
                hasNumbers: titleAnalysis.hasNumbers,
                hasQuestion: titleAnalysis.hasQuestion,
                keywords: titleAnalysis.keywords
            });
        }
    }
    
    return {
        titles,
        contentAnalysis: analysis,
        timestamp: new Date().toISOString(),
        raw: titlesString
    };
}

/**
 * 分析单个标题
 * @param {string} title - 标题
 * @returns {Object} 标题分析结果
 */
function analyzeSingleTitle(title) {
    const analysis = {
        type: 'descriptive',
        hasNumbers: /\d+/.test(title),
        hasQuestion: title.includes('�?) || title.includes('?'),
        keywords: []
    };
    
    // 检测标题类�?    if (analysis.hasQuestion) {
        analysis.type = 'question';
    } else if (analysis.hasNumbers) {
        analysis.type = 'numbered';
    } else if (title.includes('如何') || title.includes('怎么')) {
        analysis.type = 'howto';
    } else if (title.includes('vs') || title.includes('对比') || title.includes('VS')) {
        analysis.type = 'comparison';
    } else if (title.includes('最') || title.includes('第一') || title.includes('顶级')) {
        analysis.type = 'superlative';
    }
    
    // 提取关键�?    const words = title.match(/[\u4e00-\u9fa5]{2,}/g) || [];
    analysis.keywords = words.filter(word => word.length >= 2 && word.length <= 6);
    
    return analysis;
}

/**
 * 分析标题统计
 * @param {Object} titlesObj - 标题对象
 * @returns {Object} 统计信息
 */
function analyzeTitles(titlesObj) {
    const titles = titlesObj.titles || [];
    
    const typeCount = {};
    let totalLength = 0;
    let questionCount = 0;
    let numberCount = 0;
    
    titles.forEach(titleObj => {
        typeCount[titleObj.type] = (typeCount[titleObj.type] || 0) + 1;
        totalLength += titleObj.length;
        if (titleObj.hasQuestion) questionCount++;
        if (titleObj.hasNumbers) numberCount++;
    });
    
    return {
        totalTitles: titles.length,
        averageLength: Math.round(totalLength / titles.length),
        typeDistribution: typeCount,
        questionTitles: questionCount,
        numberedTitles: numberCount,
        lengthRange: {
            min: Math.min(...titles.map(t => t.length)),
            max: Math.max(...titles.map(t => t.length))
        }
    };
}

/**
 * SEO标题生成
 * @param {string} token - API密钥
 * @param {string} propertyName - 属性名
 * @param {Object} seoOptions - SEO选项
 * @param {string} modelType - 模型类型
 * @returns {Promise<Object>} SEO标题生成结果
 */
async function seoTitle(token, propertyName, seoOptions = {}, modelType = 'GLM-4-Flash') {
    const {
        targetKeywords = [],
        maxLength = 60,
        includeNumbers = true,
        includeBrand = false,
        brandName = '',
        titleCount = 5
    } = seoOptions;
    
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
        throw new Error('请先打开一个文�?);
    }
    
    const content = await app.vault.read(activeFile);
    
    let prompt = `请为以下文章生成${titleCount}个SEO优化的标题，要求：\n\n`;
    
    prompt += `SEO要求：\n`;
    prompt += `1. 标题长度不超�?{maxLength}个字符\n`;
    prompt += `2. 标题要包含主要关键词\n`;
    prompt += `3. 标题要吸引点击，提高CTR\n`;
    prompt += `4. 标题要准确描述内容\n`;
    prompt += `5. 避免关键词堆砌\n\n`;
    
    if (targetKeywords.length > 0) {
        prompt += `目标关键词（请在标题中合理使用）�?{targetKeywords.join('�?)}\n\n`;
    }
    
    if (includeNumbers) {
        prompt += `特殊要求：优先使用包含数字的标题（如"5个方�?�?10大技�?等）\n`;
    }
    
    if (includeBrand && brandName) {
        prompt += `品牌要求：在适当的标题中包含品牌�?${brandName}"\n`;
    }
    
    prompt += `\n文章内容：\n${content.substring(0, 1000)}${content.length > 1000 ? '...' : ''}\n\n`;
    prompt += `请生�?{titleCount}个SEO优化的标题：`;
    
    const titles = await callTitleAPI(prompt, token, modelType);
    const contentAnalysis = analyzeContentForTitle(content);
    const processedTitles = processTitles(titles, contentAnalysis);
    
    // 添加SEO信息
    processedTitles.seoOptions = seoOptions;
    processedTitles.titles.forEach(titleObj => {
        titleObj.seoScore = calculateSEOScore(titleObj.title, targetKeywords, maxLength);
    });
    
    await saveToProperty(activeFile, propertyName, processedTitles);
    
    const stats = analyzeTitles(processedTitles);
    
    return {
        success: true,
        titles: processedTitles,
        stats,
        seoOptions,
        propertyName
    };
}

/**
 * 计算SEO评分
 * @param {string} title - 标题
 * @param {Array} keywords - 关键�? * @param {number} maxLength - 最大长�? * @returns {number} SEO评分
 */
function calculateSEOScore(title, keywords, maxLength) {
    let score = 0;
    
    // 长度评分�?-30分）
    if (title.length <= maxLength) {
        score += 30;
    } else {
        score += Math.max(0, 30 - (title.length - maxLength) * 2);
    }
    
    // 关键词评分（0-40分）
    if (keywords.length > 0) {
        const keywordMatches = keywords.filter(keyword => title.includes(keyword)).length;
        score += (keywordMatches / keywords.length) * 40;
    } else {
        score += 20; // 没有指定关键词时给基础�?    }
    
    // 吸引力评分（0-30分）
    let attractivenessScore = 0;
    if (/\d+/.test(title)) attractivenessScore += 10; // 包含数字
    if (title.includes('�?) || title.includes('?')) attractivenessScore += 10; // 疑问�?    if (title.includes('如何') || title.includes('怎么')) attractivenessScore += 5; // 指导�?    if (title.includes('最') || title.includes('第一')) attractivenessScore += 5; // 权威�?    
    score += Math.min(attractivenessScore, 30);
    
    return Math.round(score);
}

/**
 * 创意标题生成
 * @param {string} token - API密钥
 * @param {string} propertyName - 属性名
 * @param {Array} creativeModes - 创意模式
 * @param {string} modelType - 模型类型
 * @returns {Promise<Object>} 创意标题生成结果
 */
async function creativeTitle(token, propertyName, creativeModes = ['metaphor', 'wordplay', 'contrast'], modelType = 'GLM-4-Flash') {
    const modePrompts = {
        'metaphor': '使用比喻和象征手法的创意标题',
        'wordplay': '使用双关语和文字游戏的趣味标�?,
        'contrast': '使用对比和反差的冲击性标�?,
        'emotional': '使用情感共鸣的感性标�?,
        'suspense': '使用悬念和好奇心的神秘标�?,
        'humor': '使用幽默和轻松的有趣标题'
    };
    
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
        throw new Error('请先打开一个文�?);
    }
    
    const content = await app.vault.read(activeFile);
    const contentAnalysis = analyzeContentForTitle(content);
    
    const creativeResults = [];
    
    for (const mode of creativeModes) {
        const modeDescription = modePrompts[mode] || '创意标题';
        
        let prompt = `请为以下文章生成3�?{modeDescription}，要求：\n\n`;
        prompt += `创意要求：\n`;
        prompt += `1. 标题要有创意和新颖性\n`;
        prompt += `2. 标题要准确反映文章内容\n`;
        prompt += `3. 标题要吸引读者注意\n`;
        prompt += `4. 标题长度控制�?-20个字\n`;
        prompt += `5. 体现${modeDescription}的特点\n\n`;
        
        prompt += `文章内容：\n${content.substring(0, 800)}${content.length > 800 ? '...' : ''}\n\n`;
        prompt += `请生�?�?{modeDescription}：`;
        
        try {
            const titles = await callTitleAPI(prompt, token, modelType);
            const processed = processTitles(titles, contentAnalysis);
            
            creativeResults.push({
                mode,
                description: modeDescription,
                titles: processed.titles
            });
            
            // 添加延迟
            if (creativeModes.indexOf(mode) < creativeModes.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
        } catch (error) {
            console.error(`创意模式 ${mode} 失败:`, error);
            creativeResults.push({
                mode,
                description: modeDescription,
                error: error.message
            });
        }
    }
    
    const allTitles = {
        type: 'creative_titles',
        modes: creativeResults,
        contentAnalysis,
        timestamp: new Date().toISOString()
    };
    
    await saveToProperty(activeFile, propertyName, allTitles);
    
    const totalTitles = creativeResults.reduce((sum, result) => {
        return sum + (result.titles ? result.titles.length : 0);
    }, 0);
    
    new Notice(`创意标题生成完成！共生成 ${totalTitles} 个创意标题`);
    
    return {
        success: true,
        creativeResults,
        totalTitles,
        propertyName
    };
}

/**
 * 批量标题生成
 * @param {string} token - API密钥
 * @param {string} filePattern - 文件匹配模式
 * @param {number} titleCount - 每个文件生成的标题数�? * @param {string} modelType - 模型类型
 * @returns {Promise<Object>} 批量标题生成结果
 */
async function batchTitle(token, filePattern, titleCount = 3, modelType = 'GLM-4-Flash') {
    try {
        const vault = app.vault;
        const files = vault.getMarkdownFiles();
        
        const matchedFiles = files.filter(file => {
            if (filePattern === '*.md') return true;
            return file.path.includes(filePattern);
        });
        
        if (matchedFiles.length === 0) {
            throw new Error('没有找到匹配的文�?);
        }
        
        new Notice(`开始批量生成标题，�?${matchedFiles.length} 个文�?..`);
        
        const results = [];
        let successCount = 0;
        
        for (let i = 0; i < matchedFiles.length; i++) {
            const file = matchedFiles[i];
            try {
                console.log(`处理文件 ${i + 1}/${matchedFiles.length}: ${file.name}`);
                
                const content = await vault.read(file);
                if (!content.trim()) continue;
                
                const contentAnalysis = analyzeContentForTitle(content);
                const prompt = buildTitlePrompt(content, titleCount, contentAnalysis);
                const titles = await callTitleAPI(prompt, token, modelType);
                const processedTitles = processTitles(titles, contentAnalysis);
                
                await saveToProperty(file, 'AI标题', processedTitles);
                
                const stats = analyzeTitles(processedTitles);
                
                results.push({
                    file: file.name,
                    success: true,
                    titleCount: processedTitles.titles.length,
                    stats
                });
                
                successCount++;
                
                // 添加延迟
                await new Promise(resolve => setTimeout(resolve, 1500));
                
            } catch (error) {
                console.error(`处理文件 ${file.name} 失败:`, error);
                results.push({
                    file: file.name,
                    success: false,
                    error: error.message
                });
            }
        }
        
        new Notice(`批量标题生成完成！成功：${successCount}/${matchedFiles.length}`);
        
        return {
            success: true,
            totalFiles: matchedFiles.length,
            successCount,
            results
        };
        
    } catch (error) {
        console.error('批量标题生成失败:', error);
        new Notice(`批量标题生成失败: ${error.message}`);
        return { success: false, error: error.message };
    }
}

/**
 * 保存到文件属�? * @param {Object} file - 文件对象
 * @param {string} propertyName - 属性名
 * @param {Object} titles - 标题对象
 */
async function saveToProperty(file, propertyName, titles) {
    await app.fileManager.processFrontMatter(file, (frontmatter) => {
        if (titles.titles && Array.isArray(titles.titles)) {
            // 保存标题列表
            frontmatter[propertyName] = titles.titles.map(t => t.title);
            frontmatter[`${propertyName}_analysis`] = {
                stats: analyzeTitles(titles),
                contentAnalysis: titles.contentAnalysis
            };
        } else {
            frontmatter[propertyName] = titles;
        }
    });
}

// 导出函数
module.exports = {
    aiTitle,
    seoTitle,
    creativeTitle,
    batchTitle
};

// 使用说明
console.log(`
AI标题生成脚本已加载！

主要函数�?1. aiTitle(token, propertyName, titleCount, modelType)
2. seoTitle(token, propertyName, seoOptions, modelType)
3. creativeTitle(token, propertyName, creativeModes, modelType)
4. batchTitle(token, filePattern, titleCount, modelType)

SEO选项�?- targetKeywords: 目标关键词数�?- maxLength: 最大长度（默认60字符�?- includeNumbers: 是否包含数字
- includeBrand: 是否包含品牌�?- brandName: 品牌名称

创意模式�?- metaphor: 比喻象征
- wordplay: 文字游戏
- contrast: 对比反差
- emotional: 情感共鸣
- suspense: 悬念神秘
- humor: 幽默轻松

使用示例�?// 基础标题生成
aiTitle('your-token', '文章标题', 5)

// SEO标题生成
seoTitle('your-token', 'SEO标题', {targetKeywords: ['AI', '写作'], maxLength: 50})

// 创意标题生成
creativeTitle('your-token', '创意标题', ['metaphor', 'wordplay'])

// 批量标题生成
batchTitle('your-token', '*.md', 3)
`);
