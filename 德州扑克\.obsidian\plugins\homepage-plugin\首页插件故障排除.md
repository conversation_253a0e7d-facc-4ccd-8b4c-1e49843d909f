# 首页插件故障排除指南

## 问题：首页不自动打开

如果您发现 Homepage 插件没有在启动时自动打开首页，请按照以下步骤进行排查：

### 1. 检查插件设置

1. 打开 Obsidian 设置（Ctrl + ,）
2. 在左侧菜单中找到「社区插件」
3. 找到「Homepage」插件并点击设置图标
4. 确保「启动时打开首页」选项已开启（切换按钮为蓝色）

### 2. 检查首页路径设置

1. 在 Homepage 插件设置中
2. 确保「首页路径」字段已正确填写
3. 路径应该是相对于库根目录的文件路径
4. 支持各种文件类型，例如：`首页.md`、`文件夹/首页.md`、`🌳主页.components` 等

### 3. 验证首页文件存在

1. 确认设置的首页文件确实存在于您的库中
2. 检查文件名和路径是否完全匹配（注意大小写）
3. 如果文件不存在，请创建该文件或修改设置中的路径

### 4. 重启 Obsidian

1. 完全关闭 Obsidian
2. 重新启动应用程序
3. 观察是否自动打开了设置的首页

### 5. 检查其他设置

- **替换当前标签页**：如果开启，首页会在当前标签页打开；如果关闭，会新建标签页
- **固定首页位置**：如果开启，首页会固定在指定位置

### 6. 插件冲突排查

如果以上步骤都无效，可能存在插件冲突：

1. 暂时禁用其他插件
2. 重启 Obsidian 测试 Homepage 插件
3. 逐个启用其他插件，找出冲突的插件

### 常见问题

**Q: 我之前关闭了自动打开功能，现在想重新开启怎么办？**
A: 进入插件设置，将「启动时打开首页」选项重新开启即可。

**Q: 首页路径应该怎么填写？**
A: 使用相对于库根目录的路径，例如 `README.md` 或 `笔记/首页.md`。

**Q: 为什么有时候首页会在新标签页打开，有时候替换当前标签页？**
A: 这取决于「替换当前标签页」设置。开启时会替换当前标签页，关闭时会新建标签页。

### 技术支持

如果问题仍然存在，请检查：
1. Obsidian 版本是否为最新
2. Homepage 插件是否为最新版本
3. 控制台是否有错误信息（按 Ctrl+Shift+I 打开开发者工具）

---

*最后更新：2024年*