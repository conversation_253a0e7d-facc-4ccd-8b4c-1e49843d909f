{"id": "fixed-cross-border-transfer-form", "fields": [{"id": "scenario", "label": "传输场景类型", "type": "select", "options": [{"id": "cloud-service", "label": "云服务数据传输", "value": "云服务数据传输"}, {"id": "multinational-operation", "label": "跨国公司内部传输", "value": "跨国公司内部传输"}, {"id": "third-party-service", "label": "第三方服务商传输", "value": "第三方服务商传输"}, {"id": "business-cooperation", "label": "商业合作数据共享", "value": "商业合作数据共享"}, {"id": "research-collaboration", "label": "科研合作数据传输", "value": "科研合作数据传输"}, {"id": "customer-service", "label": "客户服务支持", "value": "客户服务支持"}, {"id": "custom", "label": "其他场景", "value": "custom"}], "description": "选择跨境数据传输的场景类型"}, {"id": "customScenario", "label": "自定义场景（如选择其他）", "type": "text", "description": "当选择其他场景时填写具体内容"}, {"id": "dataSource", "label": "数据源地区", "type": "select", "options": [{"id": "china-mainland", "label": "中国大陆", "value": "中国大陆"}, {"id": "hong-kong", "label": "中国香港", "value": "中国香港"}, {"id": "singapore", "label": "新加坡", "value": "新加坡"}, {"id": "japan", "label": "日本", "value": "日本"}, {"id": "south-korea", "label": "韩国", "value": "韩国"}, {"id": "eu", "label": "欧盟", "value": "欧盟"}, {"id": "us", "label": "美国", "value": "美国"}], "description": "选择数据的来源地区"}, {"id": "destination", "label": "目标地区", "type": "select", "options": [{"id": "china-mainland", "label": "中国大陆", "value": "中国大陆"}, {"id": "hong-kong", "label": "中国香港", "value": "中国香港"}, {"id": "singapore", "label": "新加坡", "value": "新加坡"}, {"id": "japan", "label": "日本", "value": "日本"}, {"id": "south-korea", "label": "韩国", "value": "韩国"}, {"id": "eu", "label": "欧盟", "value": "欧盟"}, {"id": "us", "label": "美国", "value": "美国"}], "description": "选择数据的目标地区"}, {"id": "chinaRegulations", "label": "中国适用法规", "type": "select", "options": [{"id": "pipl-38", "label": "PIPL第38条（数据出境安全评估）", "value": "PIPL第38条（数据出境安全评估）"}, {"id": "pipl-40", "label": "PIPL第40条（标准合同条款）", "value": "PIPL第40条（标准合同条款）"}, {"id": "dsl-31", "label": "数据安全法第31条（数据出境安全管理）", "value": "数据安全法第31条（数据出境安全管理）"}, {"id": "cybersecurity", "label": "网络安全法相关条款", "value": "网络安全法相关条款"}, {"id": "security-assessment", "label": "数据出境安全评估办法", "value": "数据出境安全评估办法"}], "description": "选择适用的中国法规"}, {"id": "destinationRegulations", "label": "目标国法规", "type": "select", "options": [{"id": "gdpr", "label": "GDPR（欧盟通用数据保护条例）", "value": "GDPR（欧盟通用数据保护条例）"}, {"id": "ccpa", "label": "CCPA（加州消费者隐私法）", "value": "CCPA（加州消费者隐私法）"}, {"id": "pipeda", "label": "PIPEDA（加拿大个人信息保护法）", "value": "PIPEDA（加拿大个人信息保护法）"}, {"id": "japan-appi", "label": "日本个人信息保护法", "value": "日本个人信息保护法"}, {"id": "singapore-pdpa", "label": "新加坡个人数据保护法", "value": "新加坡个人数据保护法"}, {"id": "local-regulation", "label": "其他当地法规", "value": "其他当地法规"}], "description": "选择目标国的相关法规"}, {"id": "compliancePath", "label": "合规路径选择", "type": "select", "options": [{"id": "security-assessment", "label": "数据出境安全评估", "value": "数据出境安全评估"}, {"id": "scc", "label": "标准合同条款（SCC）", "value": "标准合同条款（SCC）"}, {"id": "certification", "label": "个人信息保护认证", "value": "个人信息保护认证"}, {"id": "adequacy-decision", "label": "充分性决定", "value": "充分性决定"}, {"id": "bcr", "label": "约束性公司规则（BCR）", "value": "约束性公司规则（BCR）"}], "description": "选择主要的合规路径"}, {"id": "technicalMeasures", "label": "技术保障措施", "type": "select", "options": [{"id": "encryption", "label": "端到端加密传输", "value": "端到端加密传输"}, {"id": "anonymization", "label": "数据匿名化处理", "value": "数据匿名化处理"}, {"id": "access-control", "label": "严格访问控制", "value": "严格访问控制"}, {"id": "audit-logging", "label": "完整审计日志", "value": "完整审计日志"}, {"id": "data-localization", "label": "数据本地化存储", "value": "数据本地化存储"}, {"id": "secure-channel", "label": "专用安全通道", "value": "专用安全通道"}], "description": "选择主要的技术保障措施"}, {"id": "riskControl", "label": "风险控制重点", "type": "select", "options": [{"id": "data-classification", "label": "数据分类分级管理", "value": "数据分类分级管理"}, {"id": "purpose-limitation", "label": "传输目的限制", "value": "传输目的限制"}, {"id": "retention-control", "label": "数据保留期限控制", "value": "数据保留期限控制"}, {"id": "third-party-management", "label": "第三方管理", "value": "第三方管理"}, {"id": "incident-response", "label": "安全事件响应", "value": "安全事件响应"}, {"id": "compliance-monitoring", "label": "合规性监控", "value": "合规性监控"}], "description": "选择主要的风险控制重点"}, {"id": "detailedMeasures", "label": "具体实施措施", "type": "textarea", "rows": 4, "description": "详细描述技术和管理措施的具体实施方案"}, {"id": "similarTransfer", "label": "类似传输场景", "type": "text", "description": "类似传输场景的链接"}, {"id": "regulatoryDynamics", "label": "监管动态", "type": "text", "description": "相关监管动态的链接"}], "action": {"type": "runScript", "scriptSource": "inline", "code": "async function entry() {\n  const { form, app, requestUrl, Notice } = this.$context;\n  \n  try {\n    // 调试：打印所有可能的form属性\n    console.log('=== 表单数据调试 ===');\n    console.log('form对象:', form);\n    console.log('form的所有属性:', Object.keys(form));\n    for (let key in form) {\n      console.log(`${key}: ${form[key]}`);\n    }\n    \n    // 获取表单数据 - 尝试多种可能的访问方式\n    const scenario = form.scenario || form['scenario'] || '云服务数据传输';\n    const customScenario = form.customScenario || form['customScenario'] || '';\n    const dataSource = form.dataSource || form['dataSource'] || '中国大陆';\n    const destination = form.destination || form['destination'] || '新加坡';\n    const chinaRegulations = form.chinaRegulations || form['chinaRegulations'] || 'PIPL第38条（数据出境安全评估）';\n    const destinationRegulations = form.destinationRegulations || form['destinationRegulations'] || 'GDPR（欧盟通用数据保护条例）';\n    const compliancePath = form.compliancePath || form['compliancePath'] || '标准合同条款（SCC）';\n    const technicalMeasures = form.technicalMeasures || form['technicalMeasures'] || '端到端加密传输';\n    const riskControl = form.riskControl || form['riskControl'] || '数据分类分级管理';\n    const detailedMeasures = form.detailedMeasures || form['detailedMeasures'] || '采用分级加密和访问控制措施';\n    const similarTransfer = form.similarTransfer || form['similarTransfer'] || '相关案例';\n    const regulatoryDynamics = form.regulatoryDynamics || form['regulatoryDynamics'] || '监管动态';\n    \n    const finalScenario = scenario === 'custom' ? (customScenario || '自定义场景') : scenario;\n    \n    // 生成当前日期（20250803格式）\n    const today = new Date();\n    const year = today.getFullYear();\n    const month = (today.getMonth() + 1).toString().padStart(2, '0');\n    const day = today.getDate().toString().padStart(2, '0');\n    const dateStr = `${year}${month}${day}`;\n    const timeStr = today.getHours().toString().padStart(2, '0') + today.getMinutes().toString().padStart(2, '0');\n    \n    // 使用YAML前置元数据结构\n    const yamlFrontmatter = `---\ndate: ${dateStr}\nscenario: ${finalScenario}\ndataSource: ${dataSource}\ndestination: ${destination}\ntransferPath: ${dataSource} → ${destination}\nchinaRegulations: ${chinaRegulations}\ndestinationRegulations: ${destinationRegulations}\ncompliancePath: ${compliancePath}\ntechnicalMeasures: ${technicalMeasures}\nriskControl: ${riskControl}\nrelatedLinks:\n  - ${similarTransfer}\n  - ${regulatoryDynamics}\ntags:\n  - 跨境数据传输\n  - ${compliancePath}\n  - 认知碰撞\n  - 版权化方案\n  - 数据合规\ncreatedBy: AI认知碰撞系统\naiModel: DeepSeek\n---`;\n    \n    // 生成基础内容模板\n    const baseTemplate = `# 跨境传输合规分析：${finalScenario}\n\n## 📋 基本信息\n\n**传输路径**：${dataSource} → ${destination}\n\n**适用法规**：\n- 🇨🇳 中国：${chinaRegulations}\n- 🌍 目标国：${destinationRegulations}\n\n**合规路径选择**：${compliancePath}\n\n**技术保障措施**：${technicalMeasures}\n\n**风险控制重点**：${riskControl}\n\n**具体实施措施**：\n${detailedMeasures}\n\n**关联资源**：\n- [[${similarTransfer}]]\n- [[${regulatoryDynamics}]]`;\n\n    // AI认知碰撞和版权化改造\n    let aiEnhancedContent = '';\n    const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec';\n    \n    const enhancedPrompt = `你是跨境数据传输合规专家。请基于以下信息，提供专业的合规分析：\n\n原始分析：\n${baseTemplate}\n\n请你作为专业的合规顾问，与这个分析进行认知碰撞，提供你独特的专业视角：\n\n1. **认知挑战**：对跨境传输合规路径的优劣分析，是否存在更优方案？\n2. **深度洞察**：基于监管趋势，分析潜在的合规风险点和应对策略\n3. **创新方案**：提供技术和管理措施的完善建议\n4. **实战经验**：分享类似传输场景的实际处理经验\n5. **版权化建议**：形成具有独特价值的跨境传输解决方案\n\n要求：\n- 不要简单重复原内容，要有认知碰撞\n- 体现专业深度和前瞻性\n- 提供具体可操作的建议\n- 形成有版权价值的专业内容\n\n请以资深跨境合规专家的身份，对这个传输方案进行深度改造和升华。`;\n    \n    try {\n      const aiResponse = await requestUrl({\n        url: 'https://api.deepseek.com/v1/chat/completions',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: 'deepseek-chat',\n          messages: [{ role: 'user', content: enhancedPrompt }],\n          temperature: 0.8,\n          max_tokens: 2000\n        })\n      });\n      \n      if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n        aiEnhancedContent = aiResponse.json.choices[0].message.content;\n      }\n    } catch (e) {\n      aiEnhancedContent = '(AI认知碰撞暂时不可用，请手动补充专业洞察)';\n    }\n\n    // 生成完整的markdown内容\n    const fullTemplate = `${yamlFrontmatter}\n\n${baseTemplate}\n\n---\n\n## 🧠 专业认知碰撞与版权化改造\n\n${aiEnhancedContent}\n\n---\n\n## 📝 实施跟踪记录\n\n<!-- 记录实际实施过程中的问题和调整 -->\n\n## 🔄 方案持续优化\n\n<!-- 记录方案的持续优化过程 -->\n\n---\n\n*生成时间：${new Date().toLocaleString('zh-CN')} | AI认知碰撞：DeepSeek | 版权化改造完成*`;\n    \n    // 生成文件名（使用20250803格式）\n    const fileName = `跨境传输-${finalScenario}-${dateStr}.md`;\n    const filePath = `工作室/肌肉/生成笔记/跨境数据传输/${fileName}`;\n    \n    // 确保文件夹存在\n    const folderPath = '工作室/肌肉/生成笔记/跨境数据传输';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    // 创建文件\n    const file = await app.vault.create(filePath, fullTemplate);\n    new Notice(`跨境数据传输分析已创建: ${fileName}`);\n    \n    // 打开新创建的文件\n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 跨境数据传输分析已生成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('生成跨境数据传输分析失败:', error);\n    new Notice('生成失败: ' + error.message);\n    return `❌ 生成失败: ${error.message}`;\n  }\n}\n\nexports.default = {\n  entry: entry\n};"}, "title": "修复版跨境数据传输表单"}