{"id": "compliance-knowledge-mvp-form", "fields": [{"id": "innovation-point", "label": "微创新点", "type": "text", "description": "合规知识的微创新点"}, {"id": "gdpr-article", "label": "GDPR法条", "type": "text", "description": "相关的GDPR法条链接"}, {"id": "pipl-article", "label": "PIPL法条", "type": "text", "description": "相关的PIPL法条链接"}, {"id": "actual-case", "label": "实际案例", "type": "text", "description": "相关实际案例的链接"}, {"id": "combination-hypothesis", "label": "组合假设", "type": "textarea", "rows": 3, "description": "跨法域的合规创新思路"}, {"id": "minimum-verification", "label": "最小验证", "type": "textarea", "rows": 2, "description": "内部分享或行业交流的验证方式"}, {"id": "peer-feedback", "label": "同行反馈", "type": "textarea", "rows": 3, "description": "收到的同行反馈"}, {"id": "iteration-direction", "label": "迭代方向", "type": "textarea", "rows": 2, "description": "后续的迭代方向"}, {"id": "project-application", "label": "实际项目应用", "type": "textarea", "rows": 2, "description": "在实际项目中的应用情况"}, {"id": "risk-avoidance", "label": "避免合规风险", "type": "textarea", "rows": 2, "description": "避免的具体合规风险"}, {"id": "standardization-potential", "label": "可标准化推广", "type": "textarea", "rows": 2, "description": "标准化推广的可能性"}], "action": {"id": "generate-compliance-knowledge-mvp", "type": "runScript", "targetFolder": "", "fileName": "", "scriptSource": "inline", "code": "async function entry() {\n  const { form } = this.$context;\n  \n  const template = `### 合规知识产品：${form['innovation-point']}\n**原料法条**：[[${form['gdpr-article']}]] + [[${form['pipl-article']}]] + [[${form['actual-case']}]]\n**组合假设**：${form['combination-hypothesis']}\n**最小验证**：${form['minimum-verification']}\n**同行反馈**：${form['peer-feedback']}\n**迭代方向**：${form['iteration-direction']}\n**ROI计算**：\n- 实际项目应用：${form['project-application']}\n- 避免合规风险：${form['risk-avoidance']}\n- 可标准化推广：${form['standardization-potential']}`;\n  \n  return template;\n}\n\nexports.default = {\n  entry: entry,\n};"}, "title": "合规知识MVP表单"}