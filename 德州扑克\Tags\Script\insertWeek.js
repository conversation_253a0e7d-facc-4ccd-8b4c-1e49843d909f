module.exports = async function selectWeeklyDiaryWithTask(params) {
    const { quickAddApi } = params;
    
    // 1. 获取所有带有 #diary 标签的文件
    const diaryFiles = [];
    const files = app.vault.getMarkdownFiles();
    
    // 获取本周的开始和结束日期
    const now = new Date();
    const dayOfWeek = now.getDay(); // 0是周日，6是周六
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1)); // 调整为周一
    startOfWeek.setHours(0, 0, 0, 0);
    
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999);
    
    for (const file of files) {
        const cache = app.metadataCache.getFileCache(file);
        // 检查是否有 #diary 标签
        const hasDiaryTag = cache?.tags?.some(tag => tag.tag === "#diary");
        
        if (hasDiaryTag && cache?.frontmatter?.date) {
            const diaryDate = new Date(cache.frontmatter.date);
            // 检查日期是否在本周范围内
            if (diaryDate >= startOfWeek && diaryDate <= endOfWeek) {
                diaryFiles.push({
                    path: file.path,
                    date: diaryDate,
                    stat: app.vault.getFileByPath(file.path)?.stat
                });
            }
        }
    }
    
    if (diaryFiles.length === 0) {
        new Notice("⚠️ 没有找到本周的日记（需带有 #diary 标签和有效的 date 属性）");
        return;
    }
    
    // 2. 按日期排序（最近的在前）
    diaryFiles.sort((a, b) => b.date - a.date);
    
    // 3. 让用户选择日记（显示日期和星期几）
    const selectedDiary = await quickAddApi.suggester(
        (fileObj) => {
            const fileName = fileObj.path.split("/").pop();
            const dateStr = fileObj.date.toLocaleDateString();
            const dayNames = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
            const dayOfWeek = dayNames[fileObj.date.getDay()];
            return `${fileName} (${dateStr} ${dayOfWeek})`;
        },
        diaryFiles
    );
    
    if (!selectedDiary) return;
    
    // 4. 让用户输入任务内容
    const taskContent = await quickAddApi.inputPrompt(
        "请输入任务内容",
        "例如：完成周报",
        "新任务"
    );
    
    if (!taskContent) return;
    
    // 5. 让用户选择任务状态
    const taskStatus = await quickAddApi.suggester(
        ["To Do - 待办", "Done - 已完成"],
        ["todo", "done"]
    );
    
    if (!taskStatus) return;
    
    // 6. 根据选择格式化任务
    let formattedTask;
    if (taskStatus === "todo") {
        formattedTask = `- [ ] ${taskContent}`;
    } else {
        formattedTask = `- ${taskContent}`;
    }
    
    // 7. 读取文件内容并插入任务
    const file = app.vault.getAbstractFileByPath(selectedDiary.path);
    let content = await app.vault.read(file);
    
    // 查找对应的标题位置
    const targetHeading = taskStatus === "todo" ? "# To Do" : "# Done";
    const headingIndex = content.indexOf(targetHeading);
    
    if (headingIndex === -1) {
        // 如果标题不存在，在文件末尾添加
        content += `\n\n${targetHeading}\n${formattedTask}`;
    } else {
        // 找到标题后，在标题下方插入任务
        const nextHeadingIndex = content.indexOf('\n#', headingIndex + 1);
        const insertPosition = nextHeadingIndex === -1 ? content.length : nextHeadingIndex;
        
        // 在标题和下一个标题之间插入任务
        const before = content.substring(0, insertPosition).trim();
        const after = content.substring(