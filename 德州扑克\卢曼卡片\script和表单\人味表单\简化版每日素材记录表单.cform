{"id": "simple-daily-material-form", "fields": [{"id": "date", "label": "记录日期", "type": "text", "defaultValue": "{{date:YYYY-MM-DD}}", "description": "素材记录日期"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "观察到的具体行为/对话", "type": "textarea", "rows": 3, "description": "记录具体的行为或对话内容"}, {"id": "myReaction", "label": "我的反应", "type": "select", "options": [{"id": "angry", "label": "愤怒", "value": "愤怒"}, {"id": "speechless", "label": "无语", "value": "无语"}, {"id": "enlightenment", "label": "顿悟", "value": "顿悟"}, {"id": "helpless", "label": "无奈", "value": "无奈"}, {"id": "shocked", "label": "震惊", "value": "震惊"}, {"id": "amused", "label": "好笑", "value": "好笑"}, {"id": "worried", "label": "担忧", "value": "担忧"}], "description": "选择你的情感反应"}, {"id": "industryRule", "label": "行业潜规则（你看到的本质）", "type": "textarea", "rows": 3, "description": "透过现象看到的行业本质或潜规则"}, {"id": "evidence", "label": "证据", "type": "textarea", "rows": 2, "description": "截图/录音/邮件片段等可用证据"}, {"id": "clientType", "label": "客户/案例类型", "type": "select", "options": [{"id": "ecommerce", "label": "电商平台", "value": "电商平台"}, {"id": "fintech", "label": "金融科技", "value": "金融科技"}, {"id": "healthcare", "label": "医疗健康", "value": "医疗健康"}, {"id": "education", "label": "在线教育", "value": "在线教育"}, {"id": "social-media", "label": "社交媒体", "value": "社交媒体"}, {"id": "gaming", "label": "游戏娱乐", "value": "游戏娱乐"}, {"id": "other", "label": "其他行业", "value": "其他行业"}], "description": "选择客户或案例的行业类型"}, {"id": "conflictPoint", "label": "冲突点", "type": "select", "options": [{"id": "no-assessment", "label": "不愿做数据跨境评估", "value": "不愿做数据跨境评估"}, {"id": "vague-consent", "label": "用户同意条款模糊", "value": "用户同意条款模糊"}, {"id": "data-retention", "label": "数据保留期限过长", "value": "数据保留期限过长"}, {"id": "third-party-sharing", "label": "第三方数据共享", "value": "第三方数据共享"}, {"id": "cost-concern", "label": "合规成本过高", "value": "合规成本过高"}, {"id": "technical-limitation", "label": "技术实现困难", "value": "技术实现困难"}, {"id": "business-impact", "label": "影响业务发展", "value": "影响业务发展"}], "description": "选择主要的冲突点"}, {"id": "myOperation", "label": "我的操作", "type": "textarea", "rows": 2, "description": "如：用某国罚款案例说服、提供技术解决方案等"}, {"id": "goldenQuote", "label": "金句/吐槽", "type": "textarea", "rows": 2, "description": "如：老板们总说'小公司没人查'"}, {"id": "materialValue", "label": "素材价值", "type": "select", "options": [{"id": "article-opening", "label": "文章开头", "value": "文章开头"}, {"id": "case-study", "label": "案例分析", "value": "案例分析"}, {"id": "industry-insight", "label": "行业洞察", "value": "行业洞察"}, {"id": "client-education", "label": "客户教育", "value": "客户教育"}, {"id": "social-content", "label": "社交内容", "value": "社交内容"}, {"id": "book-material", "label": "出书素材", "value": "出书素材"}], "description": "这个素材最适合用于什么场景"}, {"id": "urgencyLevel", "label": "紧急程度", "type": "select", "options": [{"id": "hot", "label": "热点素材（立即使用）", "value": "热点素材"}, {"id": "valuable", "label": "有价值（一周内处理）", "value": "有价值"}, {"id": "archive", "label": "存档备用", "value": "存档备用"}], "description": "选择素材的紧急程度"}], "action": {"type": "runScript", "scriptSource": "inline", "code": "async function entry() {\n  const { form, app, requestUrl, Notice } = this.$context;\n  \n  try {\n    console.log('=== 简化版每日素材记录表单调试 ===');\n    console.log('form对象:', form);\n    \n    const date = form.date || new Date().toISOString().split('T')[0];\n    const observedBehavior = form.observedBehavior || '观察到的行为';\n    const myReaction = form.myReaction || '无奈';\n    const industryRule = form.industryRule || '行业潜规则';\n    const evidence = form.evidence || '相关证据';\n    const clientType = form.clientType || '电商平台';\n    const conflictPoint = form.conflictPoint || '不愿做数据跨境评估';\n    const myOperation = form.myOperation || '我的操作方式';\n    const goldenQuote = form.goldenQuote || '经典吐槽';\n    const materialValue = form.materialValue || '文章开头';\n    const urgencyLevel = form.urgencyLevel || '有价值';\n    \n    const today = new Date();\n    const year = today.getFullYear();\n    const month = (today.getMonth() + 1).toString().padStart(2, '0');\n    const day = today.getDate().toString().padStart(2, '0');\n    const dateStr = `${year}${month}${day}`;\n    const timeStr = today.getHours().toString().padStart(2, '0') + today.getMinutes().toString().padStart(2, '0');\n    \n    const yamlFrontmatter = `---\\ndate: ${dateStr}\\nclientType: ${clientType}\\nconflictPoint: ${conflictPoint}\\nmyReaction: ${myReaction}\\nmaterialValue: ${materialValue}\\nurgencyLevel: ${urgencyLevel}\\ntags:\\n  - 每日素材记录\\n  - ${clientType}\\n  - ${conflictPoint}\\n  - ${myReaction}\\n  - ${urgencyLevel}\\ncreatedBy: 简化版每日素材记录系统\\naiModel: DeepSeek\\n---`;\n    \n    const baseTemplate = `# 每日素材记录 - ${dateStr}\\n\\n## 📋 基础记录\\n\\n**【日期】** ${date}\\n**【客户/案例类型】** ${clientType}\\n**【冲突点】** ${conflictPoint}\\n\\n## 🎯 核心内容\\n\\n**观察到：** ${observedBehavior}\\n\\n**我的反应：** ${myReaction}\\n\\n**行业潜规则：** ${industryRule}\\n\\n**我的操作：** ${myOperation}\\n\\n**金句/吐槽：** \\n> \"${goldenQuote}\"\\n\\n## 📎 证据存档\\n\\n${evidence}\\n\\n## 💡 素材价值\\n\\n**适用场景：** ${materialValue}\\n**紧急程度：** ${urgencyLevel}\\n\\n---\\n\\n**快速标签：** #${clientType} #${conflictPoint} #${myReaction}`;\n\n    let aiEnhancedContent = '';\n    const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec';\n    \n    const enhancedPrompt = `你是一位擅长素材挖掘和内容创作的专家。请基于以下每日素材记录，提供深度的价值挖掘和应用建议：\\n\\n素材记录：\\n${baseTemplate}\\n\\n请从素材价值评估、深度挖掘、应用建议、连接拓展、创作角度等方面进行分析，提供具体可操作的建议。`;\n    \n    try {\n      const aiResponse = await requestUrl({\n        url: 'https://api.deepseek.com/v1/chat/completions',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: 'deepseek-chat',\n          messages: [{ role: 'user', content: enhancedPrompt }],\n          temperature: 0.8,\n          max_tokens: 1800\n        })\n      });\n      \n      if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n        aiEnhancedContent = aiResponse.json.choices[0].message.content;\n      }\n    } catch (e) {\n      aiEnhancedContent = '(AI素材价值挖掘暂时不可用，请手动补充分析)';\n    }\n\n    const fullTemplate = `${yamlFrontmatter}\\n\\n${baseTemplate}\\n\\n---\\n\\n## 🔍 AI素材价值挖掘\\n\\n${aiEnhancedContent}\\n\\n---\\n\\n## 📝 后续应用记录\\n\\n<!-- 记录这个素材在实际创作中的使用情况 -->\\n\\n## 🔄 素材迭代优化\\n\\n<!-- 记录素材的进一步加工和优化 -->\\n\\n---\\n\\n*记录时间：${new Date().toLocaleString('zh-CN')} | AI价值挖掘：DeepSeek | 每日素材库*`;\n    \n    const fileName = `每日素材-${clientType}-${dateStr}-${timeStr}.md`;\n    const filePath = `工作室/肌肉/生成笔记/每日素材记录/${fileName}`;\n    \n    const folderPath = '工作室/肌肉/生成笔记/每日素材记录';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    const file = await app.vault.create(filePath, fullTemplate);\n    new Notice(`每日素材记录已创建: ${fileName}`);\n    \n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 每日素材记录已生成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('生成每日素材记录失败:', error);\n    new Notice('生成失败: ' + error.message);\n    return `❌ 生成失败: ${error.message}`;\n  }\n}\n\nexports.default = {\n  entry: entry\n};"}, "title": "简化版每日素材记录表单"}