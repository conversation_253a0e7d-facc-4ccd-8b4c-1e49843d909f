﻿/**
 * AI情感分析脚本
 * 功能：分析文本的情感倾向
 * 作者：Builder
 * 版本�?.0
 */

/**
 * AI情感分析函数
 * @param {string} token - API密钥
 * @param {string} propertyName - 保存分析结果的属性名
 * @param {string} modelType - AI模型类型，默认为'GLM-4-Flash'
 * @returns {Promise<Object>} 情感分析结果
 */
async function aiSentiment(token, propertyName, modelType = 'GLM-4-Flash') {
    try {
        // 验证参数
        if (!token || !propertyName) {
            throw new Error('缺少必要参数：token, propertyName');
        }

        console.log(`开始情感分析：属�?${propertyName}`);
        
        // 获取当前文件内容
        const activeFile = app.workspace.getActiveFile();
        if (!activeFile) {
            throw new Error('请先打开一个文�?);
        }
        
        const content = await app.vault.read(activeFile);
        
        if (!content.trim()) {
            throw new Error('文件内容为空，无法进行情感分�?);
        }
        
        // 构建情感分析提示
        const prompt = buildSentimentPrompt(content);
        
        // 调用AI API
        const sentimentResult = await callSentimentAPI(prompt, token, modelType);
        
        // 处理分析结果
        const processedResult = processSentimentResult(sentimentResult, content);
        
        // 保存分析结果到属�?        await saveToProperty(activeFile, propertyName, processedResult);
        
        new Notice(`情感分析完成！主要情感：${processedResult.primaryEmotion}，强度：${processedResult.intensity}`);
        
        return {
            success: true,
            sentiment: processedResult,
            propertyName
        };
        
    } catch (error) {
        console.error('情感分析失败:', error);
        new Notice(`情感分析失败: ${error.message}`);
        return { success: false, error: error.message };
    }
}

/**
 * 构建情感分析提示
 * @param {string} content - 文章内容
 * @returns {string} 情感分析提示
 */
function buildSentimentPrompt(content) {
    let prompt = `请对以下文本进行详细的情感分析，要求：\n\n`;
    
    prompt += `分析维度：\n`;
    prompt += `1. 整体情感倾向：正�?负面/中性\n`;
    prompt += `2. 情感强度�?-10分（1=非常微弱�?0=非常强烈）\n`;
    prompt += `3. 主要情感类型：喜悦、愤怒、悲伤、恐惧、惊讶、厌恶、信任、期待等\n`;
    prompt += `4. 情感分布：各种情感的占比\n`;
    prompt += `5. 情感变化：文本中情感的变化趋势\n`;
    prompt += `6. 关键情感词汇：体现情感的重要词汇\n`;
    prompt += `7. 语调特征：正�?非正式、客�?主观、冷�?激动等\n\n`;
    
    prompt += `输出格式要求：\n`;
    prompt += `请以JSON格式输出分析结果，包含以下字段：\n`;
    prompt += `{\n`;
    prompt += `  "overallSentiment": "positive/negative/neutral",\n`;
    prompt += `  "intensity": 数字(1-10),\n`;
    prompt += `  "primaryEmotion": "主要情感类型",\n`;
    prompt += `  "emotionDistribution": {\n`;
    prompt += `    "positive": 百分�?\n`;
    prompt += `    "negative": 百分�?\n`;
    prompt += `    "neutral": 百分比\n`;
    prompt += `  },\n`;
    prompt += `  "detailedEmotions": {\n`;
    prompt += `    "joy": 分数(0-10),\n`;
    prompt += `    "anger": 分数(0-10),\n`;
    prompt += `    "sadness": 分数(0-10),\n`;
    prompt += `    "fear": 分数(0-10),\n`;
    prompt += `    "surprise": 分数(0-10),\n`;
    prompt += `    "disgust": 分数(0-10),\n`;
    prompt += `    "trust": 分数(0-10),\n`;
    prompt += `    "anticipation": 分数(0-10)\n`;
    prompt += `  },\n`;
    prompt += `  "keyEmotionalWords": ["词汇1", "词汇2", "词汇3"],\n`;
    prompt += `  "toneCharacteristics": ["特征1", "特征2"],\n`;
    prompt += `  "emotionalTrend": "情感变化趋势描述",\n`;
    prompt += `  "confidence": 分析置信�?0-100),\n`;
    prompt += `  "summary": "情感分析总结"\n`;
    prompt += `}\n\n`;
    
    prompt += `待分析文本：\n${content}\n\n`;
    prompt += `请进行详细的情感分析：`;
    
    return prompt;
}

/**
 * 调用情感分析API
 * @param {string} prompt - 分析提示
 * @param {string} token - API密钥
 * @param {string} modelType - 模型类型
 * @returns {Promise<string>} 分析结果
 */
async function callSentimentAPI(prompt, token, modelType) {
    const requestBody = {
        model: modelType,
        messages: [
            {
                role: "system",
                content: "你是一个专业的情感分析专家，擅长分析文本的情感倾向、强度和细节。你能够准确识别各种情感类型，并提供详细的分析报告。请严格按照要求的JSON格式输出结果�?
            },
            {
                role: "user",
                content: prompt
            }
        ],
        temperature: 0.3,
        max_tokens: 2000
    };
    
    const response = await fetch('https://open.bigmodel.cn/api/paas/v4/chat/completions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
    }
    
    const data = await response.json();
    return data.choices[0].message.content;
}

/**
 * 处理情感分析结果
 * @param {string} sentimentResult - 原始分析结果
 * @param {string} originalContent - 原始内容
 * @returns {Object} 处理后的分析对象
 */
function processSentimentResult(sentimentResult, originalContent) {
    try {
        // 尝试解析JSON
        const jsonMatch = sentimentResult.match(/\{[\s\S]*\}/);
        let parsedResult;
        
        if (jsonMatch) {
            parsedResult = JSON.parse(jsonMatch[0]);
        } else {
            // 如果无法解析JSON，使用文本解�?            parsedResult = parseTextSentiment(sentimentResult);
        }
        
        // 添加额外信息
        parsedResult.timestamp = new Date().toISOString();
        parsedResult.contentLength = originalContent.length;
        parsedResult.analysisMethod = 'AI';
        parsedResult.raw = sentimentResult;
        
        // 验证和修正数�?        parsedResult = validateSentimentData(parsedResult);
        
        return parsedResult;
        
    } catch (error) {
        console.error('解析情感分析结果失败:', error);
        // 返回基础分析结果
        return {
            overallSentiment: 'neutral',
            intensity: 5,
            primaryEmotion: '中�?,
            confidence: 50,
            summary: '分析结果解析失败，返回默认�?,
            error: error.message,
            raw: sentimentResult,
            timestamp: new Date().toISOString()
        };
    }
}

/**
 * 文本解析情感分析（备用方法）
 * @param {string} text - 分析文本
 * @returns {Object} 解析结果
 */
function parseTextSentiment(text) {
    const result = {
        overallSentiment: 'neutral',
        intensity: 5,
        primaryEmotion: '中�?,
        emotionDistribution: { positive: 33, negative: 33, neutral: 34 },
        detailedEmotions: {},
        keyEmotionalWords: [],
        toneCharacteristics: [],
        emotionalTrend: '无明显变�?,
        confidence: 60,
        summary: '基于文本解析的情感分�?
    };
    
    // 简单的关键词匹�?    const positiveWords = ['�?, '�?, '优秀', '喜欢', '开�?, '满意', '成功', '�?];
    const negativeWords = ['�?, '�?, '失败', '讨厌', '难过', '愤�?, '失望', '糟糕'];
    
    let positiveCount = 0;
    let negativeCount = 0;
    
    positiveWords.forEach(word => {
        if (text.includes(word)) {
            positiveCount++;
            result.keyEmotionalWords.push(word);
        }
    });
    
    negativeWords.forEach(word => {
        if (text.includes(word)) {
            negativeCount++;
            result.keyEmotionalWords.push(word);
        }
    });
    
    if (positiveCount > negativeCount) {
        result.overallSentiment = 'positive';
        result.primaryEmotion = '积极';
        result.intensity = Math.min(5 + positiveCount, 10);
    } else if (negativeCount > positiveCount) {
        result.overallSentiment = 'negative';
        result.primaryEmotion = '消极';
        result.intensity = Math.min(5 + negativeCount, 10);
    }
    
    return result;
}

/**
 * 验证和修正情感数�? * @param {Object} data - 情感数据
 * @returns {Object} 修正后的数据
 */
function validateSentimentData(data) {
    // 确保必要字段存在
    if (!data.overallSentiment) data.overallSentiment = 'neutral';
    if (!data.intensity) data.intensity = 5;
    if (!data.primaryEmotion) data.primaryEmotion = '中�?;
    if (!data.confidence) data.confidence = 70;
    
    // 验证数值范�?    data.intensity = Math.max(1, Math.min(10, data.intensity));
    data.confidence = Math.max(0, Math.min(100, data.confidence));
    
    // 确保数组字段
    if (!Array.isArray(data.keyEmotionalWords)) data.keyEmotionalWords = [];
    if (!Array.isArray(data.toneCharacteristics)) data.toneCharacteristics = [];
    
    // 确保对象字段
    if (!data.emotionDistribution) {
        data.emotionDistribution = { positive: 33, negative: 33, neutral: 34 };
    }
    if (!data.detailedEmotions) {
        data.detailedEmotions = {
            joy: 0, anger: 0, sadness: 0, fear: 0,
            surprise: 0, disgust: 0, trust: 0, anticipation: 0
        };
    }
    
    return data;
}

/**
 * 情感对比分析
 * @param {string} token - API密钥
 * @param {string} propertyName - 属性名
 * @param {Array} compareFiles - 对比文件路径
 * @param {string} modelType - 模型类型
 * @returns {Promise<Object>} 对比分析结果
 */
async function sentimentComparison(token, propertyName, compareFiles = [], modelType = 'GLM-4-Flash') {
    try {
        const activeFile = app.workspace.getActiveFile();
        if (!activeFile) {
            throw new Error('请先打开一个文�?);
        }
        
        const vault = app.vault;
        const currentContent = await vault.read(activeFile);
        
        // 分析当前文件
        const currentAnalysis = await aiSentiment(token, 'temp_sentiment', modelType);
        
        const comparisons = [];
        
        // 分析对比文件
        for (const filePath of compareFiles) {
            try {
                const file = vault.getAbstractFileByPath(filePath);
                if (file && file.extension === 'md') {
                    const content = await vault.read(file);
                    const prompt = buildSentimentPrompt(content);
                    const result = await callSentimentAPI(prompt, token, modelType);
                    const processed = processSentimentResult(result, content);
                    
                    comparisons.push({
                        file: file.name,
                        path: filePath,
                        sentiment: processed
                    });
                }
            } catch (error) {
                console.error(`分析文件 ${filePath} 失败:`, error);
                comparisons.push({
                    file: filePath,
                    error: error.message
                });
            }
        }
        
        // 生成对比报告
        const comparisonReport = generateComparisonReport(currentAnalysis.sentiment, comparisons);
        
        const result = {
            type: 'sentiment_comparison',
            currentFile: {
                name: activeFile.name,
                sentiment: currentAnalysis.sentiment
            },
            comparisons,
            report: comparisonReport,
            timestamp: new Date().toISOString()
        };
        
        await saveToProperty(activeFile, propertyName, result);
        
        new Notice(`情感对比分析完成！对比了 ${comparisons.length} 个文件`);
        
        return {
            success: true,
            comparison: result,
            propertyName
        };
        
    } catch (error) {
        console.error('情感对比分析失败:', error);
        new Notice(`情感对比分析失败: ${error.message}`);
        return { success: false, error: error.message };
    }
}

/**
 * 生成对比报告
 * @param {Object} currentSentiment - 当前文件情感
 * @param {Array} comparisons - 对比结果
 * @returns {Object} 对比报告
 */
function generateComparisonReport(currentSentiment, comparisons) {
    const validComparisons = comparisons.filter(c => c.sentiment && !c.error);
    
    if (validComparisons.length === 0) {
        return {
            summary: '没有有效的对比数�?,
            insights: []
        };
    }
    
    const report = {
        summary: '',
        insights: [],
        statistics: {
            totalFiles: validComparisons.length,
            averageIntensity: 0,
            sentimentDistribution: { positive: 0, negative: 0, neutral: 0 }
        }
    };
    
    // 计算统计数据
    let totalIntensity = currentSentiment.intensity;
    const sentimentCounts = { positive: 0, negative: 0, neutral: 0 };
    
    if (currentSentiment.overallSentiment) {
        sentimentCounts[currentSentiment.overallSentiment]++;
    }
    
    validComparisons.forEach(comp => {
        totalIntensity += comp.sentiment.intensity || 5;
        if (comp.sentiment.overallSentiment) {
            sentimentCounts[comp.sentiment.overallSentiment]++;
        }
    });
    
    report.statistics.averageIntensity = Math.round(totalIntensity / (validComparisons.length + 1));
    
    const total = validComparisons.length + 1;
    report.statistics.sentimentDistribution = {
        positive: Math.round((sentimentCounts.positive / total) * 100),
        negative: Math.round((sentimentCounts.negative / total) * 100),
        neutral: Math.round((sentimentCounts.neutral / total) * 100)
    };
    
    // 生成洞察
    const currentIntensity = currentSentiment.intensity || 5;
    const avgIntensity = report.statistics.averageIntensity;
    
    if (currentIntensity > avgIntensity + 2) {
        report.insights.push('当前文件的情感强度明显高于对比文�?);
    } else if (currentIntensity < avgIntensity - 2) {
        report.insights.push('当前文件的情感强度明显低于对比文�?);
    }
    
    const currentSent = currentSentiment.overallSentiment;
    const dominantSentiment = Object.keys(sentimentCounts).reduce((a, b) => 
        sentimentCounts[a] > sentimentCounts[b] ? a : b
    );
    
    if (currentSent !== dominantSentiment) {
        report.insights.push(`当前文件情感倾向(${currentSent})与主流倾向(${dominantSentiment})不同`);
    }
    
    report.summary = `对比分析�?{validComparisons.length}个文件，平均情感强度${avgIntensity}，主要情感倾向�?{dominantSentiment}`;
    
    return report;
}

/**
 * 情感趋势分析
 * @param {string} token - API密钥
 * @param {string} propertyName - 属性名
 * @param {Object} options - 分析选项
 * @param {string} modelType - 模型类型
 * @returns {Promise<Object>} 趋势分析结果
 */
async function sentimentTrend(token, propertyName, options = {}, modelType = 'GLM-4-Flash') {
    const {
        segmentLength = 200, // 每段字数
        overlapLength = 50   // 重叠字数
    } = options;
    
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
        throw new Error('请先打开一个文�?);
    }
    
    const content = await app.vault.read(activeFile);
    
    // 分段分析
    const segments = segmentText(content, segmentLength, overlapLength);
    const segmentAnalyses = [];
    
    new Notice(`开始情感趋势分析，�?${segments.length} 个片�?..`);
    
    for (let i = 0; i < segments.length; i++) {
        try {
            console.log(`分析片段 ${i + 1}/${segments.length}`);
            
            const prompt = buildSentimentPrompt(segments[i].text);
            const result = await callSentimentAPI(prompt, token, modelType);
            const processed = processSentimentResult(result, segments[i].text);
            
            segmentAnalyses.push({
                segmentIndex: i,
                startPosition: segments[i].start,
                endPosition: segments[i].end,
                sentiment: processed
            });
            
            // 添加延迟
            if (i < segments.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
        } catch (error) {
            console.error(`分析片段 ${i + 1} 失败:`, error);
            segmentAnalyses.push({
                segmentIndex: i,
                startPosition: segments[i].start,
                endPosition: segments[i].end,
                error: error.message
            });
        }
    }
    
    // 分析趋势
    const trendAnalysis = analyzeSentimentTrend(segmentAnalyses);
    
    const result = {
        type: 'sentiment_trend',
        segments: segmentAnalyses,
        trend: trendAnalysis,
        options,
        timestamp: new Date().toISOString()
    };
    
    await saveToProperty(activeFile, propertyName, result);
    
    new Notice(`情感趋势分析完成！发�?${trendAnalysis.changePoints.length} 个情感转折点`);
    
    return {
        success: true,
        trend: result,
        propertyName
    };
}

/**
 * 分段文本
 * @param {string} text - 文本
 * @param {number} segmentLength - 段长�? * @param {number} overlapLength - 重叠长度
 * @returns {Array} 分段结果
 */
function segmentText(text, segmentLength, overlapLength) {
    const segments = [];
    let start = 0;
    
    while (start < text.length) {
        const end = Math.min(start + segmentLength, text.length);
        const segmentText = text.substring(start, end);
        
        segments.push({
            start,
            end,
            text: segmentText
        });
        
        start += segmentLength - overlapLength;
        
        if (start >= text.length) break;
    }
    
    return segments;
}

/**
 * 分析情感趋势
 * @param {Array} segmentAnalyses - 分段分析结果
 * @returns {Object} 趋势分析
 */
function analyzeSentimentTrend(segmentAnalyses) {
    const validSegments = segmentAnalyses.filter(s => s.sentiment && !s.error);
    
    if (validSegments.length === 0) {
        return {
            summary: '无有效分析数�?,
            changePoints: [],
            overallTrend: 'stable'
        };
    }
    
    const intensities = validSegments.map(s => s.sentiment.intensity || 5);
    const sentiments = validSegments.map(s => s.sentiment.overallSentiment);
    
    // 检测变化点
    const changePoints = [];
    for (let i = 1; i < validSegments.length; i++) {
        const prev = validSegments[i - 1].sentiment;
        const curr = validSegments[i].sentiment;
        
        const intensityChange = Math.abs((curr.intensity || 5) - (prev.intensity || 5));
        const sentimentChange = prev.overallSentiment !== curr.overallSentiment;
        
        if (intensityChange >= 3 || sentimentChange) {
            changePoints.push({
                position: validSegments[i].startPosition,
                type: sentimentChange ? 'sentiment_change' : 'intensity_change',
                from: prev.overallSentiment,
                to: curr.overallSentiment,
                intensityChange
            });
        }
    }
    
    // 计算整体趋势
    const firstIntensity = intensities[0];
    const lastIntensity = intensities[intensities.length - 1];
    const intensityDiff = lastIntensity - firstIntensity;
    
    let overallTrend = 'stable';
    if (intensityDiff > 2) {
        overallTrend = 'increasing';
    } else if (intensityDiff < -2) {
        overallTrend = 'decreasing';
    }
    
    return {
        summary: `情感强度�?{firstIntensity}变化�?{lastIntensity}，发�?{changePoints.length}个转折点`,
        changePoints,
        overallTrend,
        averageIntensity: Math.round(intensities.reduce((a, b) => a + b, 0) / intensities.length),
        intensityRange: {
            min: Math.min(...intensities),
            max: Math.max(...intensities)
        }
    };
}

/**
 * 批量情感分析
 * @param {string} token - API密钥
 * @param {string} filePattern - 文件匹配模式
 * @param {string} modelType - 模型类型
 * @returns {Promise<Object>} 批量分析结果
 */
async function batchSentiment(token, filePattern, modelType = 'GLM-4-Flash') {
    try {
        const vault = app.vault;
        const files = vault.getMarkdownFiles();
        
        const matchedFiles = files.filter(file => {
            if (filePattern === '*.md') return true;
            return file.path.includes(filePattern);
        });
        
        if (matchedFiles.length === 0) {
            throw new Error('没有找到匹配的文�?);
        }
        
        new Notice(`开始批量情感分析，�?${matchedFiles.length} 个文�?..`);
        
        const results = [];
        let successCount = 0;
        
        for (let i = 0; i < matchedFiles.length; i++) {
            const file = matchedFiles[i];
            try {
                console.log(`处理文件 ${i + 1}/${matchedFiles.length}: ${file.name}`);
                
                const content = await vault.read(file);
                if (!content.trim()) continue;
                
                const prompt = buildSentimentPrompt(content);
                const sentimentResult = await callSentimentAPI(prompt, token, modelType);
                const processed = processSentimentResult(sentimentResult, content);
                
                await saveToProperty(file, 'AI情感分析', processed);
                
                results.push({
                    file: file.name,
                    path: file.path,
                    success: true,
                    sentiment: {
                        overall: processed.overallSentiment,
                        intensity: processed.intensity,
                        primary: processed.primaryEmotion
                    }
                });
                
                successCount++;
                
                // 添加延迟
                await new Promise(resolve => setTimeout(resolve, 2000));
                
            } catch (error) {
                console.error(`处理文件 ${file.name} 失败:`, error);
                results.push({
                    file: file.name,
                    path: file.path,
                    success: false,
                    error: error.message
                });
            }
        }
        
        new Notice(`批量情感分析完成！成功：${successCount}/${matchedFiles.length}`);
        
        return {
            success: true,
            totalFiles: matchedFiles.length,
            successCount,
            results
        };
        
    } catch (error) {
        console.error('批量情感分析失败:', error);
        new Notice(`批量情感分析失败: ${error.message}`);
        return { success: false, error: error.message };
    }
}

/**
 * 保存到文件属�? * @param {Object} file - 文件对象
 * @param {string} propertyName - 属性名
 * @param {Object} sentiment - 情感分析对象
 */
async function saveToProperty(file, propertyName, sentiment) {
    await app.fileManager.processFrontMatter(file, (frontmatter) => {
        frontmatter[propertyName] = sentiment;
    });
}

// 导出函数
module.exports = {
    aiSentiment,
    sentimentComparison,
    sentimentTrend,
    batchSentiment
};

// 使用说明
console.log(`
AI情感分析脚本已加载！

主要函数�?1. aiSentiment(token, propertyName, modelType)
2. sentimentComparison(token, propertyName, compareFiles, modelType)
3. sentimentTrend(token, propertyName, options, modelType)
4. batchSentiment(token, filePattern, modelType)

情感类型�?- 整体倾向：positive/negative/neutral
- 详细情感：joy, anger, sadness, fear, surprise, disgust, trust, anticipation
- 强度范围�?-10�?- 置信度：0-100%

趋势分析选项�?- segmentLength: 每段字数（默�?00�?- overlapLength: 重叠字数（默�?0�?
使用示例�?// 基础情感分析
aiSentiment('your-token', '情感分析')

// 情感对比分析
sentimentComparison('your-token', '情感对比', ['file1.md', 'file2.md'])

// 情感趋势分析
sentimentTrend('your-token', '情感趋势', {segmentLength: 300, overlapLength: 100})

// 批量情感分析
batchSentiment('your-token', '*.md')
`);
