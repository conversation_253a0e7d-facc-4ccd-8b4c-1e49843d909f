async function customAI(token, propertyName, modelType) {
  const model = modelType || "GLM-4-Flash"; // 智谱清言模型，GLM-4-Flash 是一个免费模型

  if (!propertyName || !token) {
    new Notice("请设置密钥或属性名");
    return;
  }
  
  const { currentFile } = this;
  const file = currentFile;
  const fileContent = await app.vault.cachedRead(file);
  const title = file.basename;
  
  // 获取文件的frontmatter属性
  const frontmatter = app.metadataCache.getFileCache(file)?.frontmatter || {};
  const tags = frontmatter.tags || [];
  const customPrompt = frontmatter.prompt || frontmatter.提示词 || "";
  const outputFormat = frontmatter.format || frontmatter.输出格式 || "markdown";
  const language = frontmatter.language || frontmatter.语言 || "中文";
  
  // 构建文件信息
  let fileInfo = `文件标题：${title}\n`;
  if (tags.length > 0) fileInfo += `文件标签：${tags.join(", ")}\n`;
  if (fileContent) fileInfo += `文件内容：\n${fileContent}\n`;
  
  // 如果frontmatter中有自定义提示词，直接使用
  let finalPrompt = "";
  if (customPrompt) {
    finalPrompt = `${customPrompt}\n\n基于以下文件信息：\n${fileInfo}`;
  } else {
    // 如果没有自定义提示词，让用户输入
    const userPrompt = await new Promise((resolve) => {
      const modal = new obsidian.Modal(app);
      modal.titleEl.setText("自定义AI助手");
      
      const container = modal.contentEl.createDiv();
      container.createEl("h3", { text: "请输入你的需求：" });
      
      const textarea = container.createEl("textarea", {
        attr: {
          rows: "8",
          cols: "60",
          placeholder: "例如：\n- 帮我总结这篇文章的要点\n- 把这个内容改写成小红书文案\n- 翻译成英文\n- 生成思维导图\n- 写一份会议纪要\n- 等等..."
        }
      });
      textarea.style.width = "100%";
      textarea.style.marginBottom = "10px";
      
      const buttonContainer = container.createDiv();
      buttonContainer.style.textAlign = "right";
      
      const cancelBtn = buttonContainer.createEl("button", { text: "取消" });
      cancelBtn.style.marginRight = "10px";
      cancelBtn.onclick = () => {
        modal.close();
        resolve(null);
      };
      
      const confirmBtn = buttonContainer.createEl("button", { text: "确定" });
      confirmBtn.style.backgroundColor = "#007acc";
      confirmBtn.style.color = "white";
      confirmBtn.onclick = () => {
        const prompt = textarea.value.trim();
        modal.close();
        resolve(prompt);
      };
      
      modal.open();
      textarea.focus();
    });
    
    if (!userPrompt) {
      new Notice("已取消操作");
      return;
    }
    
    finalPrompt = `${userPrompt}\n\n请基于以下文件信息完成任务：\n${fileInfo}\n\n要求：\n- 输出格式：${outputFormat}\n- 使用语言：${language}\n- 请直接输出结果，不需要额外解释`;
  }

  // 调用AI
  var options = {
    method: "POST",
    url: "https://open.bigmodel.cn/api/paas/v4/chat/completions",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      model: model,
      messages: [
        {
          role: "user",
          content: finalPrompt,
        },
      ],
    }),
  };
  
  const response = await obsidian.requestUrl(options);
  const result = response.json;
  
  if (result.choices.length === 0) {
    new Notice("没有内容可输出");
    return;
  }

  const content = result.choices[0].message?.content;
  if (!content) {
    new Notice("没有内容可输出");
    return;
  }
  
  // 询问用户如何处理结果
  const action = await new Promise((resolve) => {
    const modal = new obsidian.Modal(app);
    modal.titleEl.setText("AI处理完成");
    
    const container = modal.contentEl.createDiv();
    container.createEl("h3", { text: "请选择如何处理结果：" });
    
    const preview = container.createEl("div", {
      attr: { style: "max-height: 200px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; margin: 10px 0; background: #f9f9f9;" }
    });
    preview.innerHTML = content.replace(/\n/g, '<br>');
    
    const buttonContainer = container.createDiv();
    buttonContainer.style.textAlign = "center";
    buttonContainer.style.marginTop = "15px";
    
    const appendBtn = buttonContainer.createEl("button", { text: "📝 追加到当前文件" });
    appendBtn.style.margin = "5px";
    appendBtn.onclick = () => {
      modal.close();
      resolve("append");
    };
    
    const newFileBtn = buttonContainer.createEl("button", { text: "📄 创建新文件" });
    newFileBtn.style.margin = "5px";
    newFileBtn.onclick = () => {
      modal.close();
      resolve("newfile");
    };
    
    const frontmatterBtn = buttonContainer.createEl("button", { text: "🏷️ 保存到属性" });
    frontmatterBtn.style.margin = "5px";
    frontmatterBtn.onclick = () => {
      modal.close();
      resolve("frontmatter");
    };
    
    const copyBtn = buttonContainer.createEl("button", { text: "📋 复制到剪贴板" });
    copyBtn.style.margin = "5px";
    copyBtn.onclick = () => {
      modal.close();
      resolve("copy");
    };
    
    const cancelBtn = buttonContainer.createEl("button", { text: "❌ 取消" });
    cancelBtn.style.margin = "5px";
    cancelBtn.onclick = () => {
      modal.close();
      resolve(null);
    };
    
    modal.open();
  });
  
  if (!action) {
    new Notice("已取消操作");
    return;
  }
  
  // 根据用户选择处理结果
  switch (action) {
    case "append":
      const currentContent = await app.vault.read(file);
      const newContent = currentContent + "\n\n## AI处理结果\n\n" + content;
      await app.vault.modify(file, newContent);
      new Notice("✅ 结果已追加到当前文件");
      break;
      
    case "newfile":
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
      const newFileName = `AI处理结果_${timestamp}.md`;
      const newFileContent = `# AI处理结果\n\n**来源文件**: [[${title}]]\n**处理时间**: ${new Date().toLocaleString()}\n\n---\n\n${content}`;
      await app.vault.create(newFileName, newFileContent);
      new Notice(`✅ 已创建新文件：${newFileName}`);
      break;
      
    case "frontmatter":
      app.fileManager.processFrontMatter(file, (frontmatter) => {
        frontmatter[propertyName] = content.trim();
      });
      new Notice(`✅ 结果已保存到属性：${propertyName}`);
      break;
      
    case "copy":
      await navigator.clipboard.writeText(content);
      new Notice("✅ 结果已复制到剪贴板");
      break;
  }
}

// 使用示例：
// customAI("a6ff22a8cdac4123a9d9a8ea3779009c.aOZZo3lqUQxXgZkN", "AI结果");

exports.default = {
  entry: customAI,
  name: "customAI",
  description: `自定义AI助手 - 通过智谱清言API实现各种自定义功能

  ==请先在 https://open.bigmodel.cn/ 注册并获取 API 密钥==

  使用方法：
  \`customAI('你的密钥', '属性名')\`

  功能特点：
  - 🎯 完全自定义：可以输入任何需求
  - 📝 多种输出：追加到文件、创建新文件、保存到属性、复制到剪贴板
  - 🏷️ 属性支持：可在frontmatter中预设提示词
  - 🌐 多语言：支持中英文等多种语言输出
  - 📊 多格式：支持markdown、纯文本等格式

  支持的笔记属性：
  - prompt/提示词：预设的AI提示词
  - format/输出格式：输出格式（默认：markdown）
  - language/语言：输出语言（默认：中文）

  使用场景：
  - 文章总结、翻译、改写
  - 生成各种文案、报告
  - 数据分析、思维导图
  - 代码解释、文档生成
  - 任何你能想到的AI任务
  `,
};
