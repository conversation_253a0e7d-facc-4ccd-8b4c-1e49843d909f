async function xiaoh<PERSON>shu(token, propertyName, modelType) {
  const model = modelType || "GLM-4-Flash"; // 智谱清言模型，GLM-4-Flash 是一个免费模型，其他模型需要付费

  if (!propertyName || !token) {
    new Notice("请设置密钥或属性名");
    return;
  }
  const { currentFile } = this;
  const file = currentFile;
  const fileContent = await app.vault.cachedRead(file);
  const title = file.basename;
  
  // 获取文件的frontmatter属性
  const frontmatter = app.metadataCache.getFileCache(file)?.frontmatter || {};
  const tags = frontmatter.tags || [];
  const topic = frontmatter.topic || frontmatter.话题 || "";
  const style = frontmatter.style || frontmatter.风格 || "干货分享";
  const target = frontmatter.target || frontmatter.目标人群 || "年轻人";
  const length = frontmatter.length || frontmatter.长度 || "中等";
  
  // 构建属性信息字符串
  let attributeInfo = "";
  if (tags.length > 0) attributeInfo += `标签: ${tags.join(", ")}\n`;
  if (topic) attributeInfo += `话题: ${topic}\n`;
  if (style) attributeInfo += `风格: ${style}\n`;
  if (target) attributeInfo += `目标人群: ${target}\n`;
  if (length) attributeInfo += `长度: ${length}\n`;

  // 提示词
  const prompt = `
你是一名专业的小红书博主，请根据以下笔记信息生成一篇小红书文案：

笔记标题：${title}

笔记属性：
${attributeInfo}

笔记内容：
${fileContent || ""}

创作要求：
1. 文案长度控制在200-400字
2. 开头要有吸引人的标题或hook，可以用疑问句、感叹句等
3. 内容要有干货价值，实用性强
4. 多使用emoji表情，让文案生动有趣
5. 语言要轻松活泼，贴近年轻人，避免过于正式
6. 结尾要有互动引导（比如：你们觉得呢？评论区聊聊～）
7. 添加3-5个相关话题标签（格式：#话题名）
8. 适当使用换行，让排版清晰好看
9. 突出重点信息，可以用【】或者★等符号

请直接输出完整的小红书文案内容，不需要其他解释。
`;

  var options = {
    method: "POST",
    url: "https://open.bigmodel.cn/api/paas/v4/chat/completions",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      model: model,
      messages: [
        {
          role: "user",
          content: prompt,
        },
      ],
    }),
  };
  
  const response = await obsidian.requestUrl(options);
  const result = response.json;
  
  if (result.choices.length === 0) {
    new Notice("没有内容可输出");
    return;
  }

  const content = result.choices[0].message?.content;
  if (!content) {
    new Notice("没有内容可输出");
    return;
  }
  
  // 生成文案标题（吸引人的小红书标题）
  const titlePrompt = `
请为以下小红书文案生成一个吸引人的标题，要求简洁有力，能够吸引用户点击。

文案内容：
${content}

要求：
1. 标题要概括文案的核心价值
2. 使用疑问句、感叹句或者数字等吸引眼球
3. 长度控制在20字以内
4. 符合小红书平台特色
5. 只输出标题，不要其他内容
`;

  const titleOptions = {
    method: "POST",
    url: "https://open.bigmodel.cn/api/paas/v4/chat/completions",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      model: model,
      messages: [
        {
          role: "user",
          content: titlePrompt,
        },
      ],
    }),
  };
  
  const titleResponse = await obsidian.requestUrl(titleOptions);
  const titleResult = titleResponse.json;
  const xiaohongshuTitle = titleResult.choices?.[0]?.message?.content || "小红书文案";
  
  // 将小红书标题写入指定属性，文案内容写入正文
  app.fileManager.processFrontMatter(file, (frontmatter) => {
    frontmatter[propertyName] = xiaohongshuTitle.trim();
  });
  
  // 将文案内容追加到文件正文
  const currentContent = await app.vault.read(file);
  const newContent = currentContent + "\n\n## 📱 小红书文案\n\n" + content;
  await app.vault.modify(file, newContent);
  
  new Notice(`小红书文案已生成：${xiaohongshuTitle.trim()}`);
}

// 在 Components 插件中使用示例
// xiaohongshu("a6ff22a8cdac4123a9d9a8ea3779009c.aOZZo3lqUQxXgZkN", "小红书标题");

exports.default = {
  entry: xiaohongshu,
  name: "xiaohongshu",
  description: `通过智谱清言的 API 根据笔记内容生成小红书文案(默认使用的是免费模型 GLM-4-Flash)

  ==请先在 https://open.bigmodel.cn/ 注册并获取 API 密钥。==

  使用方法

\`xiaohongshu('你的密钥', '属性名')\`

  也可以指定其他付费模型，模型类型可以在 https://open.bigmodel.cn/console/modelcenter/square 查看

\`xiaohongshu('你的密钥', '属性名', 'glm-4-plus')\`

  功能说明：
  - 根据笔记内容自动生成小红书风格文案
  - 自动生成吸引人的小红书标题
  - 支持多种文案风格和目标人群设定
  - 生成的文案会直接添加到笔记中
  
  支持的笔记属性：
  - tags/标签：影响文案风格
  - topic/话题：文案主题方向
  - style/风格：文案风格（默认：干货分享）
  - target/目标人群：目标受众（默认：年轻人）
  - length/长度：文案长度（默认：中等）
  `,
};
