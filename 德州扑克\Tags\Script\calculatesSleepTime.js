
// ===== 睡眠时间计算脚本 =====
async function calculateSleepTime() {
    try {
        const { currentFile } = this;
        const file = currentFile;
        
        // 获取文件内容
        const content = await app.vault.read(file);
        
        // 提取起床和睡觉时间
        let wakeTime = "", sleepTime = "";
        
        // 使用正则匹配 YAML 格式或行内格式（支持引号）
        const wakeMatch = content.match(/起床[:：]\s*["']?(\d{1,2}[:：]\d{2})["']?/);
        const sleepMatch = content.match(/睡觉[:：]\s*["']?(\d{1,2}[:：]\d{2})["']?/);

        wakeTime = wakeMatch ? wakeMatch[1].replace("：", ":") : "";
        sleepTime = sleepMatch ? sleepMatch[1].replace("：", ":") : "";
        
        if (!wakeTime || !sleepTime) {
            return "⚠️ 未找到有效的起床/睡觉时间";
        }
        
        // 计算睡眠时间（小时）
        function computeHours(wakeStr, sleepStr) {
            const [wakeH, wakeM] = wakeStr.split(':').map(Number);
            const [sleepH, sleepM] = sleepStr.split(':').map(Number);
            
            // 判断是否跨天（00:00-04:00算同一天，20:00-24:00算跨天）
            const isSameDay = (sleepH >= 0 && sleepH <= 4);
            
            let totalMinutes;
            if (isSameDay) {
                // 同一天（如 1:30睡觉，8:00起床 → 6.5小时）
                totalMinutes = (wakeH - sleepH) * 60 + (wakeM - sleepM);
            } else {
                // 跨天（如 23:00睡觉，7:00起床 → 8小时）
                totalMinutes = (24 - sleepH + wakeH) * 60 + (wakeM - sleepM);
            }
            
            return (totalMinutes / 60).toFixed(1);
        }
        
        const sleepHours = computeHours(wakeTime, sleepTime);
         return `${sleepHours}  `;        
    } catch (error) {
        console.error('计算睡眠时间时出错:', error);
        return "❌ 计算睡眠时间时发生错误";
    }
}

// ===== 导出模块 =====
exports.default = {
    name: "calculateSleepTime",
    description: `计算笔记中起床和睡觉时间之间的睡眠时长

    要求格式（任选其一）：
    1. YAML格式：
      起床: 7:00
      睡觉: 23:00
    2. 行内格式：
      - 起床：8:00
      - 睡觉：1:30

    返回格式：
    🛌 睡眠时间: X.X 小时 (睡觉时间 → 起床时间)
    `,
    entry: calculateSleepTime,
};