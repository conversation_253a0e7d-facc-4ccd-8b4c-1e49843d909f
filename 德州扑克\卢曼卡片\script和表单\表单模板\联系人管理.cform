{"id": "contact-management", "fields": [{"id": "name", "label": "姓名", "type": "text", "required": true, "description": "联系人姓名"}, {"id": "relationship", "label": "关系", "type": "select", "options": [{"id": "family", "label": "👨‍👩‍👧‍👦 家人", "value": "家人"}, {"id": "friend", "label": "👫 朋友", "value": "朋友"}, {"id": "colleague", "label": "👔 同事", "value": "同事"}, {"id": "client", "label": "🤝 客户", "value": "客户"}, {"id": "partner", "label": "🤝 合作伙伴", "value": "合作伙伴"}, {"id": "other", "label": "🔗 其他", "value": "其他"}], "defaultValue": "朋友", "description": "与你的关系"}, {"id": "phone", "label": "电话", "type": "text", "description": "联系电话"}, {"id": "email", "label": "邮箱", "type": "text", "description": "电子邮箱地址"}, {"id": "company", "label": "公司/组织", "type": "text", "displayConditions": [{"field": "关系", "operator": "in", "value": ["同事", "客户", "合作伙伴"]}], "description": "工作单位或组织"}, {"id": "position", "label": "职位", "type": "text", "displayConditions": [{"field": "关系", "operator": "in", "value": ["同事", "客户", "合作伙伴"]}], "description": "职务或职位"}, {"id": "address", "label": "地址", "type": "textarea", "description": "联系地址"}, {"id": "birthday", "label": "生日", "type": "date", "description": "生日日期"}, {"id": "notes", "label": "备注", "type": "textarea", "description": "其他重要信息"}, {"id": "tags", "label": "标签", "type": "text", "description": "用逗号分隔的标签，如：重要,北京,技术"}], "action": {"type": "createFile", "filePath": "工作室/肌肉/生成笔记/联系人管理/{{@姓名}}.md", "content": "# {{@姓名}}\n\n## 👤 基本信息\n- **姓名**: {{@姓名}}\n- **关系**: {{@关系}}\n- **电话**: {{@电话}}\n- **邮箱**: {{@邮箱}}\n{{@公司/组织 ? '- **公司/组织**: ' + @公司/组织 + '\\n' : ''}}{{@职位 ? '- **职位**: ' + @职位 + '\\n' : ''}}- **地址**: {{@地址}}\n- **生日**: {{@生日}}\n- **添加日期**: {{date:YYYY-MM-DD}}\n\n## 📝 备注\n{{@备注}}\n\n## 📞 联系记录\n<!-- 记录与此人的联系历史 -->\n\n### {{date:YYYY-MM-DD}}\n- 初次添加联系人\n\n## 🎁 重要日期提醒\n{{@生日 ? '- [ ] 生日提醒: ' + @生日 + '\\n' : ''}}\n## 🔗 相关链接\n<!-- 社交媒体、个人网站等 -->\n\n## 🏷️ 标签\n#联系人 #{{@关系}} {{@标签 ? @标签.split(',').map(tag => '#' + tag.trim()).join(' ') : ''}}\n\n---\n*联系人创建于 {{date:YYYY-MM-DD}} {{time:HH:mm}}*"}}