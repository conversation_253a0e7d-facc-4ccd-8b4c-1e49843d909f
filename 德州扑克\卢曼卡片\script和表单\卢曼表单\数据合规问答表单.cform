{"id": "data-compliance-qa-form", "fields": [{"id": "business-scenario", "label": "具体业务场景", "type": "text", "description": "描述具体的业务场景"}, {"id": "data-usage-need", "label": "数据利用需求", "type": "textarea", "rows": 2, "description": "具体的数据利用需求"}, {"id": "privacy-protection-requirement", "label": "隐私保护要求", "type": "textarea", "rows": 2, "description": "相关的隐私保护要求"}, {"id": "legal-basis", "label": "法规依据", "type": "textarea", "rows": 2, "description": "GDPR/PIPL等相关法条"}, {"id": "interpretation", "label": "我的解读", "type": "textarea", "rows": 3, "description": "用技术/管理手段重构的解读"}, {"id": "operation-guide", "label": "操作指南", "type": "textarea", "rows": 4, "description": "具体到可执行的合规checklist"}, {"id": "related-regulations", "label": "相关法条解读", "type": "text", "description": "相关法条解读的链接"}, {"id": "similar-scenarios", "label": "类似场景处理", "type": "text", "description": "类似场景处理的链接"}, {"id": "risk-warning", "label": "风险提醒", "type": "textarea", "rows": 2, "description": "当前方案可能忽略的监管趋势变化"}], "action": {"id": "generate-data-compliance-qa", "type": "runScript", "targetFolder": "", "fileName": "", "scriptSource": "inline", "code": "async function entry() {\n  const { form } = this.$context;\n  \n  const template = `### Q：在${form['business-scenario']}中，如何平衡${form['data-usage-need']}与${form['privacy-protection-requirement']}？\n\nA：\n- **法规依据**：${form['legal-basis']}\n- **我的解读**：${form.interpretation}\n- **操作指南**：${form['operation-guide']}\n\n→ 关联：[[${form['related-regulations']}]] [[${form['similar-scenarios']}]]\n! 风险：当前方案可能忽略${form['risk-warning']}`;\n  \n  return template;\n}\n\nexports.default = {\n  entry: entry,\n};"}, "title": "数据合规问答表单"}