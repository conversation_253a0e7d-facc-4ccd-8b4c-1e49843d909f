﻿/**
 * AI翻译脚本
 * 功能：将笔记翻译成指定语言
 * 作者：Builder
 * 版本�?.0
 */

/**
 * AI翻译函数
 * @param {string} token - API密钥
 * @param {string} propertyName - 保存翻译结果的属性名
 * @param {string} targetLanguage - 目标语言
 * @param {string} modelType - AI模型类型，默认为'GLM-4-Flash'
 * @returns {Promise<Object>} 翻译结果
 */
async function aiTranslate(token, propertyName, targetLanguage, modelType = 'GLM-4-Flash') {
    try {
        // 验证参数
        if (!token || !propertyName || !targetLanguage) {
            throw new Error('缺少必要参数：token, propertyName, targetLanguage');
        }

        console.log(`开始翻译：目标语言=${targetLanguage}, 属�?${propertyName}`);
        
        // 获取当前文件内容
        const activeFile = app.workspace.getActiveFile();
        if (!activeFile) {
            throw new Error('请先打开一个文�?);
        }
        
        const content = await app.vault.read(activeFile);
        
        if (!content.trim()) {
            throw new Error('文件内容为空');
        }
        
        // 语言映射
        const languageMap = {
            '英语': 'English',
            '中文': 'Chinese',
            '日语': 'Japanese',
            '韩语': 'Korean',
            '法语': 'French',
            '德语': 'German',
            '西班牙语': 'Spanish',
            '俄语': 'Russian',
            '阿拉伯语': 'Arabic',
            '意大利语': 'Italian',
            '葡萄牙语': 'Portuguese',
            '荷兰�?: 'Dutch',
            '瑞典�?: 'Swedish',
            '挪威�?: 'Norwegian',
            '丹麦�?: 'Danish',
            '芬兰�?: 'Finnish',
            '泰语': 'Thai',
            '越南�?: 'Vietnamese',
            '印地�?: 'Hindi',
            '土耳其�?: 'Turkish'
        };
        
        const targetLang = languageMap[targetLanguage] || targetLanguage;
        
        // 构建翻译提示
        const prompt = `请将以下内容翻译�?{targetLang}，要求：
1. 保持原文的格式和结构
2. 保留Markdown语法
3. 专业术语要准确翻�?4. 语言要自然流�?5. 如果是代码块，请保持代码不变，只翻译注释
6. 保留链接和图片引�?
原文内容�?${content}`;
        
        // 调用AI API
        const translation = await callTranslationAPI(prompt, token, modelType);
        
        // 保存翻译结果
        await saveToProperty(activeFile, propertyName, translation);
        
        new Notice(`翻译完成！已保存到属性：${propertyName}`);
        
        return {
            success: true,
            targetLanguage: targetLang,
            propertyName,
            translation,
            originalLength: content.length,
            translationLength: translation.length
        };
        
    } catch (error) {
        console.error('翻译失败:', error);
        new Notice(`翻译失败: ${error.message}`);
        return { success: false, error: error.message };
    }
}

/**
 * 调用翻译API
 * @param {string} prompt - 翻译提示
 * @param {string} token - API密钥
 * @param {string} modelType - 模型类型
 * @returns {Promise<string>} 翻译结果
 */
async function callTranslationAPI(prompt, token, modelType) {
    const requestBody = {
        model: modelType,
        messages: [
            {
                role: "system",
                content: "你是一个专业的翻译专家，擅长各种语言之间的准确翻译。请保持原文的格式、结构和专业性，确保翻译的准确性和流畅性�?
            },
            {
                role: "user",
                content: prompt
            }
        ],
        temperature: 0.3, // 降低温度以提高翻译准确�?        max_tokens: 4000
    };
    
    const response = await fetch('https://open.bigmodel.cn/api/paas/v4/chat/completions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
    }
    
    const data = await response.json();
    return data.choices[0].message.content;
}

/**
 * 批量翻译多个文件
 * @param {string} token - API密钥
 * @param {string} targetLanguage - 目标语言
 * @param {string} filePattern - 文件匹配模式
 * @param {string} modelType - 模型类型
 * @returns {Promise<Object>} 批量翻译结果
 */
async function batchTranslate(token, targetLanguage, filePattern, modelType = 'GLM-4-Flash') {
    try {
        const vault = app.vault;
        const files = vault.getMarkdownFiles();
        
        // 筛选文�?        const matchedFiles = files.filter(file => {
            if (filePattern === '*.md') return true;
            return file.path.includes(filePattern);
        });
        
        if (matchedFiles.length === 0) {
            throw new Error('没有找到匹配的文�?);
        }
        
        new Notice(`开始批量翻�?${matchedFiles.length} 个文�?..`);
        
        const results = [];
        let successCount = 0;
        
        for (let i = 0; i < matchedFiles.length; i++) {
            const file = matchedFiles[i];
            try {
                console.log(`翻译文件 ${i + 1}/${matchedFiles.length}: ${file.name}`);
                
                const content = await vault.read(file);
                if (!content.trim()) continue;
                
                const prompt = `请将以下内容翻译�?{targetLanguage}，保持格式和结构：\n\n${content}`;
                const translation = await callTranslationAPI(prompt, token, modelType);
                
                // 创建翻译文件
                const translatedFileName = `${file.basename}_${targetLanguage}.md`;
                const translatedPath = file.parent ? `${file.parent.path}/${translatedFileName}` : translatedFileName;
                await vault.create(translatedPath, translation);
                
                results.push({
                    file: file.name,
                    success: true,
                    translatedFile: translatedFileName
                });
                
                successCount++;
                
                // 添加延迟
                await new Promise(resolve => setTimeout(resolve, 2000));
                
            } catch (error) {
                console.error(`翻译文件 ${file.name} 失败:`, error);
                results.push({
                    file: file.name,
                    success: false,
                    error: error.message
                });
            }
        }
        
        new Notice(`批量翻译完成！成功：${successCount}/${matchedFiles.length}`);
        
        return {
            success: true,
            totalFiles: matchedFiles.length,
            successCount,
            results
        };
        
    } catch (error) {
        console.error('批量翻译失败:', error);
        new Notice(`批量翻译失败: ${error.message}`);
        return { success: false, error: error.message };
    }
}

/**
 * 保存到文件属�? * @param {Object} file - 文件对象
 * @param {string} propertyName - 属性名
 * @param {string} content - 内容
 */
async function saveToProperty(file, propertyName, content) {
    await app.fileManager.processFrontMatter(file, (frontmatter) => {
        frontmatter[propertyName] = content;
    });
}

// 导出函数
module.exports = {
    aiTranslate,
    batchTranslate
};

// 使用说明
console.log(`
AI翻译脚本已加载！

主要函数�?1. aiTranslate(token, propertyName, targetLanguage, modelType)
2. batchTranslate(token, targetLanguage, filePattern, modelType)

支持的语言�?英语、中文、日语、韩语、法语、德语、西班牙语、俄语、阿拉伯语、意大利语、葡萄牙语、荷兰语、瑞典语、挪威语、丹麦语、芬兰语、泰语、越南语、印地语、土耳其�?
使用示例�?// 翻译当前文件为英�?aiTranslate('your-token', '英文翻译', '英语')

// 批量翻译所有文件为日语
batchTranslate('your-token', '日语', '*.md')
`);
