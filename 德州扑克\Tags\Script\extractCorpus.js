// AI语料提取器 - 像其他AI脚本一样使用
async function extractCorpus(token) {
  if (!token) {
    new Notice("❌ 请传入AI密钥参数");
    return;
  }

  const { currentFile } = this;
  const file = currentFile;

  // 检查是否是口语练习笔记
  if (!file.path.includes('口语练习') || !file.basename.match(/^\d{6}/)) {
    new Notice("请在口语练习笔记中使用此脚本");
    return;
  }

  try {

    const fileContent = await app.vault.cachedRead(file);
    
    // 显示处理提示
    new Notice("🤖 AI正在分析语料...", 3000);
    
    // 调用AI分析
    const analysisResult = await callAIForCorpusAnalysis(token, fileContent, file.basename);
    
    if (!analysisResult) {
      new Notice("❌ AI分析失败");
      return;
    }
    
    // 显示分析结果并让用户确认
    const userConfirmed = await showAIAnalysisModal(analysisResult);
    
    if (userConfirmed) {
      // 创建语料笔记
      await createAICorpusNotes(analysisResult, file.basename);
      
      // 更新原笔记的属性
      await updatePracticeNoteWithAI(file, analysisResult);
      
      new Notice("✅ AI语料提取完成！");
    } else {
      new Notice("已取消语料提取");
    }
    
  } catch (error) {
    new Notice(`❌ AI提取失败: ${error.message}`);
    console.error("AI语料提取错误:", error);
  }
}

// 调用AI进行语料分析
async function callAIForCorpusAnalysis(token, content, fileName) {
  const prompt = `你是一个专业的英语学习助手，请分析这篇口语练习笔记，提取精准的英语语料短语。

**重要要求：**
1. 只提取短语，不要完整句子（如："critical issue", "pose a major challenge", "data breach"）
2. 重点提取能够精准提升表达的短语
3. 为每个短语生成2个实用例句和中文翻译
4. 分析学习问题和改进建议
5. 以JSON格式返回结果

**提取类型：**
- 形容词短语：critical issue, major challenge, serious problem
- 动词短语：pose a challenge, raise concerns, implement measures  
- 介词短语：in accordance with, pursuant to, with regard to
- 专业术语：data breach, compliance framework, regulatory requirement
- 强化词汇：critical vs important, significant vs major

**分析维度：**
- 从流利程度、评分、内容分析学习问题
- 根据错误类型和场景提供改进建议

请返回JSON格式：
{
  "extractedPhrases": [
    {
      "phrase": "critical issue",
      "type": "形容词短语",
      "difficulty": "intermediate",
      "context": "问题描述",
      "translation": "关键问题",
      "usage": "用于强调问题的严重性，比important更有力度",
      "examples": [
        "Data security is a critical issue for all companies.",
        "Climate change poses a critical issue for future generations."
      ],
      "exampleTranslations": [
        "数据安全对所有公司来说都是一个关键问题。",
        "气候变化对未来几代人来说是一个关键问题。"
      ]
    }
  ],
  "identifiedProblems": [
    "表达不够流利，存在卡壳现象",
    "单复数使用不当"
  ],
  "improvementSuggestions": [
    "学习更地道的英语表达方式",
    "注意使用复数形式表达普遍性概念"
  ]
}

文件名：${fileName}
笔记内容：
${content}`;

  try {
    const response = await obsidian.requestUrl({
      method: "POST",
      url: "https://open.bigmodel.cn/api/paas/v4/chat/completions",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "GLM-4-Flash",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.3,
        max_tokens: 3000,
      }),
    });

    const result = response.json;
    const aiContent = result.choices?.[0]?.message?.content;

    if (!aiContent) {
      throw new Error("AI返回结果为空");
    }

    // 尝试解析JSON
    try {
      // 提取JSON部分（可能包含在代码块中）
      const jsonMatch = aiContent.match(/```json\n([\s\S]*?)\n```/) || aiContent.match(/\{[\s\S]*\}/);
      const jsonStr = jsonMatch ? (jsonMatch[1] || jsonMatch[0]) : aiContent;
      
      const parsedResult = JSON.parse(jsonStr);
      return parsedResult;
    } catch (parseError) {
      console.error("JSON解析失败:", parseError);
      console.log("AI原始返回:", aiContent);
      
      // 如果JSON解析失败，尝试从文本中提取信息
      return parseAITextResponse(aiContent);
    }

  } catch (error) {
    console.error("AI调用失败:", error);
    throw error;
  }
}

// 解析AI文本响应（备用方案）
function parseAITextResponse(text) {
  const result = {
    extractedPhrases: [],
    identifiedProblems: [],
    improvementSuggestions: []
  };

  // 简单的文本解析逻辑
  const lines = text.split('\n');
  let currentSection = '';

  lines.forEach(line => {
    line = line.trim();
    if (line.includes('语料') || line.includes('短语')) {
      currentSection = 'phrases';
    } else if (line.includes('问题')) {
      currentSection = 'problems';
    } else if (line.includes('建议') || line.includes('改进')) {
      currentSection = 'suggestions';
    }

    // 提取带引号的短语
    const phraseMatch = line.match(/"([^"]+)"/);
    if (phraseMatch && currentSection === 'phrases') {
      result.extractedPhrases.push({
        phrase: phraseMatch[1],
        type: "AI提取",
        difficulty: "intermediate",
        context: "专业表达",
        translation: "待翻译",
        usage: "待补充"
      });
    }

    // 提取问题和建议
    if (line.startsWith('-') || line.startsWith('•')) {
      const content = line.replace(/^[-•]\s*/, '');
      if (currentSection === 'problems') {
        result.identifiedProblems.push(content);
      } else if (currentSection === 'suggestions') {
        result.improvementSuggestions.push(content);
      }
    }
  });

  return result;
}

// 显示AI分析结果模态框
async function showAIAnalysisModal(analysis) {
  return new Promise((resolve) => {
    const modal = new obsidian.Modal(app);
    modal.titleEl.setText("🤖 AI语料分析结果");

    const container = modal.contentEl.createDiv();
    container.style.padding = "20px";
    container.style.maxHeight = "70vh";
    container.style.overflowY = "auto";

    // 显示提取的语料
    if (analysis.extractedPhrases && analysis.extractedPhrases.length > 0) {
      container.createEl("h3", { text: `📚 AI提取的精准语料 (${analysis.extractedPhrases.length}个)` });
      const phrasesList = container.createEl("ul");
      analysis.extractedPhrases.forEach(phrase => {
        const li = phrasesList.createEl("li");
        li.innerHTML = `<strong>"${phrase.phrase}"</strong> <span style="color: #666;">[${phrase.type}]</span><br>
                       <small style="color: #888;">翻译: ${phrase.translation} | 用法: ${phrase.usage}</small>`;
      });
    }

    // 显示识别的问题
    if (analysis.identifiedProblems && analysis.identifiedProblems.length > 0) {
      container.createEl("h3", { text: "⚠️ AI识别的问题" });
      const problemsList = container.createEl("ul");
      analysis.identifiedProblems.forEach(problem => {
        problemsList.createEl("li", { text: problem });
      });
    }

    // 显示改进建议
    if (analysis.improvementSuggestions && analysis.improvementSuggestions.length > 0) {
      container.createEl("h3", { text: "💡 AI改进建议" });
      const suggestionsList = container.createEl("ul");
      analysis.improvementSuggestions.forEach(suggestion => {
        suggestionsList.createEl("li", { text: suggestion });
      });
    }

    const buttonContainer = container.createDiv();
    buttonContainer.style.display = "flex";
    buttonContainer.style.gap = "10px";
    buttonContainer.style.justifyContent = "center";
    buttonContainer.style.marginTop = "20px";

    // 确认按钮
    const confirmBtn = buttonContainer.createEl("button", { text: "✅ 创建语料笔记" });
    confirmBtn.style.cssText = "background: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;";
    confirmBtn.onclick = () => {
      modal.close();
      resolve(true);
    };

    // 取消按钮
    const cancelBtn = buttonContainer.createEl("button", { text: "❌ 取消" });
    cancelBtn.style.cssText = "background: #f44336; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;";
    cancelBtn.onclick = () => {
      modal.close();
      resolve(false);
    };

    modal.open();
  });
}

// 创建AI语料笔记
async function createAICorpusNotes(analysis, practiceFileName) {
  const today = new Date();
  const fullDateString = today.toISOString().split('T')[0];
  const timeString = today.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit'
  });

  const corpusFolder = "英语/口语训练/语料卡";

  // 确保文件夹存在
  try {
    await app.vault.createFolder(corpusFolder);
  } catch (e) {
    // 文件夹已存在，忽略错误
  }

  // 为每个AI提取的语料创建笔记
  if (analysis.extractedPhrases) {
    for (let i = 0; i < analysis.extractedPhrases.length; i++) {
      const phrase = analysis.extractedPhrases[i];

      // 生成安全的文件名（只保留英文，不加日期）
      const safeTitle = phrase.phrase
        .replace(/[<>:"/\\|?*\n\r\t]/g, '_')
        .replace(/[_\s]+/g, '_')
        .replace(/^_+|_+$/g, '')
        .substring(0, 50);

      const fileName = `${safeTitle}.md`;
      const filePath = `${corpusFolder}/${fileName}`;

      // 检查文件是否已存在
      if (await app.vault.adapter.exists(filePath)) {
        continue; // 跳过已存在的文件
      }

      // 生成AI语料笔记内容
      const corpusContent = generateAICorpusContent(phrase, analysis, practiceFileName, fullDateString, timeString);

      try {
        await app.vault.create(filePath, corpusContent);
        new Notice(`✅ AI创建语料: ${phrase.phrase}`);
      } catch (error) {
        console.error(`创建AI语料笔记失败: ${fileName}`, error);
      }
    }
  }
}

// 生成AI语料笔记内容（包含例句）
function generateAICorpusContent(phrase, analysis, practiceFileName, fullDate, time) {
  // 生成例句（如果AI没有提供，使用默认例句）
  const examples = phrase.examples || [
    `This is a ${phrase.phrase} that requires immediate attention.`,
    `We need to address this ${phrase.phrase} as soon as possible.`
  ];

  // 生成例句的中文翻译
  const exampleTranslations = phrase.exampleTranslations || [
    `这是一个需要立即关注的${phrase.translation || '重要事项'}。`,
    `我们需要尽快解决这个${phrase.translation || '重要事项'}。`
  ];

  return `---
记录日期: "${fullDate}"
记录时间: "${time}"
翻译: "${phrase.translation || '待翻译'}"
具体意义: "${phrase.usage || '待补充'}"
出错点: "AI分析提取的重点语料"
语料类型: "${phrase.type}"
难度等级: "${phrase.difficulty}"
提取方式: "AI智能提取"
来源练习: "[[${practiceFileName}]]"
---

# ${phrase.phrase}

## 📚 例句

### 例句1
**英文**: ${examples[0]}
**中文**: ${exampleTranslations[0]}

### 例句2
**英文**: ${examples[1]}
**中文**: ${exampleTranslations[1]}

---
来源练习: [[${practiceFileName}]]
`;
}

// 更新练习笔记属性（AI版本）
async function updatePracticeNoteWithAI(file, analysis) {
  try {
    const content = await app.vault.read(file);

    // 提取YAML前置数据
    const yamlMatch = content.match(/^(---\n[\s\S]*?\n---)/);
    if (!yamlMatch) return;

    let yamlContent = yamlMatch[1];

    // 更新遇到的问题（AI分析结果）
    if (analysis.identifiedProblems && analysis.identifiedProblems.length > 0) {
      const problemsText = analysis.identifiedProblems.join('；');
      yamlContent = yamlContent.replace(
        /遇到的问题: ".*?"/,
        `遇到的问题: "${problemsText}"`
      );
    }

    // 更新改进点（AI建议）
    if (analysis.improvementSuggestions && analysis.improvementSuggestions.length > 0) {
      const improvementsText = analysis.improvementSuggestions.join('；');
      yamlContent = yamlContent.replace(
        /改进点: ".*?"/,
        `改进点: "${improvementsText}"`
      );
    }

    // 替换原内容中的YAML部分
    const updatedContent = content.replace(/^---\n[\s\S]*?\n---/, yamlContent);

    await app.vault.modify(file, updatedContent);
    new Notice("✅ AI已更新练习笔记属性");

  } catch (error) {
    console.error("AI更新练习笔记属性失败:", error);
  }
}

exports.default = {
  entry: extractCorpus,
  name: "extractCorpus",
  description: `🤖 AI语料提取器

🎯 特点：
- 🤖 AI智能提取精准语料短语
- 📝 简洁文件名（不含日期）
- ⚠️ 自动分析学习问题
- 💡 生成个性化改进建议

🎮 使用方法：
extractCorpus("your_api_key_here")

🔧 功能：
- 提取形容词短语：critical issue, major challenge
- 提取动词短语：pose a challenge, raise concerns
- 提取专业术语：data breach, compliance framework
- 自动填充"遇到的问题"和"改进点"属性
- 创建独立的语料笔记卡片

💡 适用于：
- 口语练习笔记（文件名格式：YYMMDD）
- 包含修正表达和建议表达的笔记
- 需要提取精准语料的学习记录

🤖 AI模型：GLM-4-Flash (智谱清言)
==请先在 https://open.bigmodel.cn/ 注册并获取 API 密钥==
  `
};
