/**
 * 一键提取人味模板脚本
 * 简化版本，直接批量复制所有模板文件
 */

(async function() {
    const { app, Notice } = this;
    
    try {
        console.log('🚀 开始一键提取人味模板...');
        
        // 配置路径
        const sourceFolder = '德州扑克/卢曼卡片/script和表单/人味表单/人味模板';
        const targetFolder = '工作室/肌肉/人味模板库';
        
        // 创建目标文件夹
        const targetFolderObj = app.vault.getAbstractFileByPath(targetFolder);
        if (!targetFolderObj) {
            await app.vault.createFolder(targetFolder);
            console.log('✅ 已创建目标文件夹: ' + targetFolder);
        }
        
        // 获取所有模板文件
        const allFiles = app.vault.getMarkdownFiles();
        const templateFiles = allFiles.filter(file => 
            file.path.startsWith(sourceFolder + '/')
        );
        
        console.log(`📁 找到 ${templateFiles.length} 个模板文件`);
        
        if (templateFiles.length === 0) {
            new Notice('未找到模板文件，请检查源文件夹路径');
            return;
        }
        
        let successCount = 0;
        let skipCount = 0;
        let errorCount = 0;
        const processedFiles = [];
        
        // 批量处理文件
        for (const file of templateFiles) {
            try {
                console.log(`📄 处理文件: ${file.name}`);
                
                // 读取文件内容
                const content = await app.vault.read(file);
                
                // 生成目标文件路径
                const targetFilePath = `${targetFolder}/${file.name}`;
                
                // 检查文件是否已存在
                const existingFile = app.vault.getAbstractFileByPath(targetFilePath);
                if (existingFile) {
                    console.log(`⏭️ 文件已存在，跳过: ${file.name}`);
                    skipCount++;
                    continue;
                }
                
                // 创建新文件
                await app.vault.create(targetFilePath, content);
                
                processedFiles.push({
                    name: file.name,
                    originalPath: file.path,
                    newPath: targetFilePath,
                    size: content.length
                });
                
                console.log(`✅ 成功复制: ${file.name}`);
                successCount++;
                
                // 添加小延迟避免过快操作
                await new Promise(resolve => setTimeout(resolve, 50));
                
            } catch (error) {
                console.error(`❌ 处理失败: ${file.name}`, error);
                errorCount++;
            }
        }
        
        // 生成处理报告
        const reportContent = generateReport(processedFiles, successCount, skipCount, errorCount);
        const reportPath = `${targetFolder}/提取报告-${new Date().toISOString().split('T')[0]}.md`;
        
        try {
            await app.vault.create(reportPath, reportContent);
            console.log('✅ 已生成提取报告');
        } catch (error) {
            console.log('⚠️ 生成报告失败，但文件提取成功');
        }
        
        // 显示结果通知
        const resultMessage = `🎉 人味模板提取完成！\n\n✅ 成功: ${successCount} 个\n⏭️ 跳过: ${skipCount} 个\n❌ 失败: ${errorCount} 个\n\n📁 保存位置: ${targetFolder}`;
        
        new Notice(resultMessage, 8000);
        console.log(resultMessage);
        
        // 打开目标文件夹
        try {
            const targetFolderFile = app.vault.getAbstractFileByPath(targetFolder);
            if (targetFolderFile) {
                const leaf = app.workspace.getLeaf();
                await leaf.openFile(targetFolderFile);
            }
        } catch (error) {
            console.log('⚠️ 无法自动打开目标文件夹');
        }
        
        return `提取完成: 成功${successCount}个, 跳过${skipCount}个, 失败${errorCount}个`;
        
    } catch (error) {
        console.error('💥 一键提取失败:', error);
        new Notice('提取失败: ' + error.message, 5000);
        return '❌ 提取失败';
    }
})();

// 生成处理报告
function generateReport(processedFiles, successCount, skipCount, errorCount) {
    const now = new Date();
    const dateStr = now.toISOString().split('T')[0];
    const timeStr = now.toLocaleTimeString('zh-CN');
    
    let report = `# 人味模板提取报告\n\n`;
    report += `**提取时间**: ${dateStr} ${timeStr}\n`;
    report += `**源文件夹**: 德州扑克/卢曼卡片/script和表单/人味表单/人味模板\n`;
    report += `**目标文件夹**: 工作室/肌肉/人味模板库\n\n`;
    
    report += `## 📊 提取统计\n\n`;
    report += `- ✅ **成功提取**: ${successCount} 个文件\n`;
    report += `- ⏭️ **跳过文件**: ${skipCount} 个文件（已存在）\n`;
    report += `- ❌ **提取失败**: ${errorCount} 个文件\n`;
    report += `- 📁 **总计处理**: ${successCount + skipCount + errorCount} 个文件\n\n`;
    
    if (processedFiles.length > 0) {
        report += `## 📋 成功提取的文件清单\n\n`;
        
        // 按文件大小排序
        const sortedFiles = processedFiles.sort((a, b) => b.size - a.size);
        
        for (let i = 0; i < sortedFiles.length; i++) {
            const file = sortedFiles[i];
            report += `${i + 1}. **${file.name}**\n`;
            report += `   - 大小: ${file.size.toLocaleString()} 字符\n`;
            report += `   - 路径: [[${file.newPath}]]\n\n`;
        }
        
        // 统计信息
        const totalSize = processedFiles.reduce((sum, file) => sum + file.size, 0);
        const avgSize = Math.round(totalSize / processedFiles.length);
        
        report += `## 📈 文件统计\n\n`;
        report += `- **总字符数**: ${totalSize.toLocaleString()} 字符\n`;
        report += `- **平均大小**: ${avgSize.toLocaleString()} 字符\n`;
        report += `- **最大文件**: ${sortedFiles[0]?.name} (${sortedFiles[0]?.size.toLocaleString()} 字符)\n`;
        report += `- **最小文件**: ${sortedFiles[sortedFiles.length - 1]?.name} (${sortedFiles[sortedFiles.length - 1]?.size.toLocaleString()} 字符)\n\n`;
    }
    
    report += `## 📝 使用说明\n\n`;
    report += `1. 所有模板文件已复制到目标文件夹\n`;
    report += `2. 点击文件名可直接打开对应模板\n`;
    report += `3. 可根据需要进一步整理和分类\n`;
    report += `4. 建议定期备份重要模板\n\n`;
    
    report += `---\n`;
    report += `*报告自动生成于 ${dateStr} ${timeStr}*\n`;
    report += `*脚本版本: 一键提取人味模板 v1.0*`;
    
    return report;
}
