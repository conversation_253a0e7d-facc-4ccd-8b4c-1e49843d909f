{
  "id": "ai-diary-helper-v2",
  "fields": [
    {
      "id": "ai-provider",
      "label": "AI服务�?,
      "type": "select",
      "options": [
        {
          "id": "deepseek",
          "label": "DeepSeek（推荐）",
          "value": "deepseek"
        },
        {
          "id": "zhipu",
          "label": "智谱AI",
          "value": "zhipu"
        }
      ],
      "defaultValue": "deepseek",
      "description": "选择AI服务�?
    },
    {
      "id": "deepseek-key",
      "label": "DeepSeek API密钥",
      "type": "text",
      "defaultValue": "a6ff22a8cdac4123a9d9a8ea3779009c.aOZZo3lqUQxXgZkN",
      "description": "DeepSeek AI的API密钥（已预填�?
    },
    {
      "id": "zhipu-key",
      "label": "智谱AI密钥",
      "type": "text",
      "defaultValue": "a6ff22a8cdac4123a9d9a8ea3779009c.aOZZo3lqUQxXgZkN",
      "description": "智谱AI的API密钥（已预填�?
    },
    {
      "id": "amap-key",
      "label": "高德API密钥",
      "type": "text",
      "defaultValue": "09a59646e96551fe2f8dfa22f3858ab8",
      "description": "高德地图API密钥（已预填�?
    },
    {
      "id": "city",
      "label": "城市",
      "type": "text",
      "defaultValue": "北京",
      "description": "输入城市名称"
    },
    {
      "id": "mood",
      "label": "心情",
      "type": "select",
      "options": [
        {
          "id": "happy",
          "label": "😊 开�?,
          "value": "😊 开�?
        },
        {
          "id": "calm",
          "label": "😐 平静",
          "value": "😐 平静"
        },
        {
          "id": "sad",
          "label": "😔 低落",
          "value": "😔 低落"
        },
        {
          "id": "tired",
          "label": "😴 疲惫",
          "value": "😴 疲惫"
        }
      ],
      "defaultValue": "😊 开�?,
      "description": "今天的心情如何？"
    },
    {
      "id": "keywords",
      "label": "今日关键�?,
      "type": "text",
      "defaultValue": "",
      "description": "用几个关键词描述今天"
    },
    {
      "id": "ai-task",
      "label": "AI任务",
      "type": "select",
      "options": [
        {
          "id": "opening",
          "label": "生成日记开�?,
          "value": "生成日记开�?
        },
        {
          "id": "summary",
          "label": "总结今日感悟",
          "value": "总结今日感悟"
        },
        {
          "id": "plan",
          "label": "制定明日计划",
          "value": "制定明日计划"
        }
      ],
      "defaultValue": "生成日记开�?,
      "description": "希望AI帮你做什么？"
    },
    {
      "id": "enable-ai",
      "label": "启用AI功能",
      "type": "select",
      "options": [
        {
          "id": "yes",
          "label": "�?,
          "value": "�?
        },
        {
          "id": "no",
          "label": "否（仅天气）",
          "value": "�?
        }
      ],
      "defaultValue": "�?,
      "description": "是否启用AI功能（避�?29错误可选择否）"
    }
  ],
  "action": {
    "type": "runScript",
    "scriptSource": "inline",
    "code": "async function entry() {\n    const { app, form, requestUrl, Notice } = this.$context;\n    \n    try {\n        const zhipuKey = form['智谱AI密钥'];\n        const amapKey = form['高德API密钥'];\n        const city = form['城市'];\n        const mood = form['心情'];\n        const keywords = form['今日关键�?];\n        const aiTask = form['AI任务'];\n        const enableAI = form['启用AI功能'];\n        \n        // 生成日期时间\n        const today = new Date();\n        const dateStr = today.toISOString().split('T')[0];\n        const timeStr = today.toTimeString().split(' ')[0].substring(0, 5);\n        \n        let weatherInfo = '天气信息获取�?..';\n        let aiContent = '';\n        \n        // 获取天气信息\n        if (amapKey && amapKey !== '请填入你的高德API密钥') {\n            try {\n                const geoUrl = `https://restapi.amap.com/v3/geocode/geo?key=${amapKey}&address=${encodeURIComponent(city)}`;\n                const geoResponse = await requestUrl(geoUrl);\n                \n                if (geoResponse.json.status === '1' && geoResponse.json.geocodes.length > 0) {\n                    const adcode = geoResponse.json.geocodes[0].adcode;\n                    const weatherUrl = `https://restapi.amap.com/v3/weather/weatherInfo?key=${amapKey}&city=${adcode}`;\n                    const weatherResponse = await requestUrl(weatherUrl);\n                    \n                    if (weatherResponse.json.status === '1') {\n                        const weather = weatherResponse.json.lives[0];\n                        weatherInfo = `${weather.weather} 🌡�?${weather.temperature}°C�?{weather.winddirection}�?{weather.windpower}级，湿度${weather.humidity}%`;\n                    } else {\n                        weatherInfo = '天气信息获取失败';\n                    }\n                } else {\n                    weatherInfo = '城市信息获取失败';\n                }\n            } catch (e) {\n                weatherInfo = '天气API调用失败';\n            }\n        } else {\n            weatherInfo = '未配置天气API';\n        }\n        \n        // 根据选择决定是否调用AI\n        if (enableAI === '�? && zhipuKey && zhipuKey !== '请填入你的智谱AI密钥') {\n            try {\n                let prompt = `请帮�?{aiTask}。背景信息：\\n- 日期�?{dateStr}\\n- 城市�?{city}\\n- 天气�?{weatherInfo}\\n- 心情�?{mood}\\n- 关键词：${keywords || '日常生活'}\\n\\n请用温暖、真实的语调生成内容，结合天气和心情的关系，不要太正式，控制�?00字以内。`;\n                \n                // 添加延迟避免频率限制\n                await new Promise(resolve => setTimeout(resolve, 2000));\n                \n                const aiResponse = await requestUrl({\n                    url: 'https://open.bigmodel.cn/api/paas/v4/chat/completions',\n                    method: 'POST',\n                    headers: {\n                        'Authorization': `Bearer ${zhipuKey}`,\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        model: \"glm-4-flash\",\n                        messages: [{ role: \"user\", content: prompt }],\n                        temperature: 0.7,\n                        max_tokens: 200\n                    })\n                });\n                \n                if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n                    aiContent = aiResponse.json.choices[0].message.content;\n                } else {\n                    aiContent = '今天天气不错，心情也很好，是记录生活的好时光�?;\n                }\n            } catch (e) {\n                if (e.message.includes('429')) {\n                    aiContent = `今天�?{dateStr}，天�?{weatherInfo}，心�?{mood}。虽然AI暂时繁忙，但这不影响记录美好的一天！建议稍后再试AI功能，或者暂时手动填写内容。`;\n                } else {\n                    aiContent = `今天天气很棒�?{weatherInfo}），心情${mood}，是个值得记录的好日子！AI暂时不可用，但生活依然美好。`;\n                }\n            }\n        } else if (enableAI === '�?) {\n            aiContent = `今天�?{dateStr}�?{city}的天气是${weatherInfo}，心�?{mood}。这是一�?{keywords || '平凡而美�?}的日子，值得用心记录。`;\n        } else {\n            aiContent = '未配置AI密钥，请手动填写内容或配置密钥后重试�?;\n        }\n        \n        // 生成日记内容\n        const content = `# ${dateStr} 的日记\\n\\n## 📅 基本信息\\n- **日期**: ${dateStr}\\n- **时间**: ${timeStr}\\n- **城市**: ${city}\\n- **天气**: ${weatherInfo}\\n- **心情**: ${mood}\\n- **关键�?*: ${keywords || '日常生活'}\\n\\n## ${enableAI === '�? ? '🤖 AI ' + aiTask : '📝 日记开�?}\\n${aiContent}\\n\\n## 📝 详细记录\\n<!-- 在这里记录今天的详细内容 -->\\n\\n\\n## 🌟 今日感悟\\n<!-- 今天的思考和感悟 -->\\n\\n\\n## 🎯 明日计划\\n<!-- 明天的主要计�?-->\\n\\n\\n## 🏷�?标签\\n#日记 #${dateStr.replace(/-/g, '/')} #${mood.replace(/😊|😐|😔|😤|😴|🤔/g, '').trim()} ${keywords ? keywords.split(/[,，\\s]+/).map(k => '#' + k.trim()).join(' ') : ''}\\n\\n---\\n*记录�?${dateStr} ${timeStr} | ${enableAI === '�? ? 'AI助手: 智谱GLM-4-Flash' : '纯天气模�?} | 天气: 高德地图*\\n`;\n\n        // 创建文件\n        const fileName = `${dateStr}.md`;\n        const filePath = `工作室/肌肉/生成笔记/AI日记/${fileName}`;\n        \n        // 检查并创建目录\n        const folderPath = '工作室/肌肉/生成笔记/AI日记';\n        const folder = app.vault.getAbstractFileByPath(folderPath);\n        if (!folder) {\n            await app.vault.createFolder(folderPath);\n        }\n        \n        // 检查文件是否已存在\n        const existingFile = app.vault.getAbstractFileByPath(filePath);\n        if (existingFile) {\n            new Notice(`文件 ${fileName} 已存在，正在打开...`);\n            const leaf = app.workspace.getLeaf();\n            await leaf.openFile(existingFile);\n            return;\n        }\n        \n        // 创建新文件\n        const file = await app.vault.create(filePath, content);\n        new Notice(`${enableAI === '�? ? 'AI智能' : '天气'}日记创建成功: ${fileName}`);\n        \n        // 打开新创建的文件\n        const leaf = app.workspace.getLeaf();\n        await leaf.openFile(file);\n        \n    } catch (error) {\n        console.error('创建日记失败:', error);\n        new Notice('创建日记失败: ' + error.message);\n    }\n}\n\nexports.default = {\n    entry: entry\n};"
  }
}
