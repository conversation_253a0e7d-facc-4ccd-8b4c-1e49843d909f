{"id": "daily-compliance-thinking-form", "fields": [{"id": "date", "label": "日期", "type": "text", "defaultValue": "{{date:YYYY-MM-DD}}", "description": "思考日期"}, {"id": "thinking-topic", "label": "合规思考主题", "type": "text", "description": "今日合规思考的主题"}, {"id": "trigger-event", "label": "触发事件", "type": "textarea", "rows": 2, "description": "引发思考的具体事件"}, {"id": "compliance-conflict", "label": "合规冲突", "type": "textarea", "rows": 2, "description": "法规vs业务需求的冲突点"}, {"id": "related-precedent-1", "label": "相关先例1", "type": "text", "description": "第一个相关先例的链接"}, {"id": "related-precedent-2", "label": "相关先例2", "type": "text", "description": "第二个相关先例的链接"}, {"id": "core-risk", "label": "核心合规风险", "type": "textarea", "rows": 2, "description": "识别的核心合规风险"}, {"id": "solution-gap", "label": "现有解决方案不足", "type": "textarea", "rows": 2, "description": "现有方案的不足之处"}, {"id": "innovative-solution", "label": "我的创新方案", "type": "textarea", "rows": 3, "description": "提出的创新解决方案"}, {"id": "implementation-steps", "label": "可实施步骤", "type": "textarea", "rows": 3, "description": "具体的实施步骤"}, {"id": "risk-control", "label": "风险控制点", "type": "textarea", "rows": 2, "description": "需要控制的风险点"}, {"id": "follow-up", "label": "后续跟踪", "type": "text", "description": "后续跟踪事项的链接"}], "action": {"id": "generate-daily-compliance-thinking", "type": "runScript", "targetFolder": "", "fileName": "", "scriptSource": "inline", "code": "async function entry() {\n  const { form, app, requestUrl, Notice } = this.$context;\n  \n  try {\n    // 生成AI优化的内容\n    let aiEnhancedContent = '';\n    const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec';\n    \n    const prompt = `你是经验丰富的合规顾问。请基于以下每日合规思考，提供专业洞察：\n\n思考主题：${form['thinking-topic']}\n触发事件：${form['trigger-event']}\n合规冲突：${form['compliance-conflict']}\n核心风险：${form['core-risk']}\n方案不足：${form['solution-gap']}\n创新方案：${form['innovative-solution']}\n实施步骤：${form['implementation-steps']}\n风险控制：${form['risk-control']}\n\n请提供：\n1. 对这个合规思考的深度点评\n2. 可能遗漏的风险点和机会点\n3. 创新方案的可行性分析和改进建议\n4. 行业最佳实践参考\n\n要求：实用、前瞻、可操作。`;\n    \n    try {\n      const aiResponse = await requestUrl({\n        url: 'https://api.deepseek.com/v1/chat/completions',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: \"deepseek-chat\",\n          messages: [{ role: \"user\", content: prompt }],\n          temperature: 0.7,\n          max_tokens: 1500\n        })\n      });\n      \n      if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n        aiEnhancedContent = aiResponse.json.choices[0].message.content;\n      }\n    } catch (e) {\n      aiEnhancedContent = '(AI分析暂时不可用，请手动补充深度分析)';\n    }\n    \n    // 生成完整内容\n    const template = `### ${form.date}-合规思考：${form['thinking-topic']}\n**触发事件**：${form['trigger-event']}\n- 合规冲突：${form['compliance-conflict']}\n- 相关先例：[[${form['related-precedent-1']}]] [[${form['related-precedent-2']}]]\n**分析过程**：\n1. 核心合规风险：${form['core-risk']}\n2. 现有解决方案不足：${form['solution-gap']}\n3. 我的创新方案：${form['innovative-solution']}\n**落地验证**：\n- 可实施步骤：${form['implementation-steps']}\n- 风险控制点：${form['risk-control']}\n→ 关联：[[${form['follow-up']}]]\n\n---\n\n## 🤖 AI专业洞察\n\n${aiEnhancedContent}\n\n---\n\n## 📝 实践反馈\n<!-- 记录后续实践中的反馈和调整 -->\n\n\n## 🏷️ 标签\n#每日合规思考 #合规创新 #数据合规 #${form.date}\n\n---\n*生成时间：${new Date().toLocaleString()} | AI助手：DeepSeek*`;\n    \n    // 创建文件\n    const fileName = `每日合规思考-${form['thinking-topic']}-${form.date}.md`;\n    const filePath = `工作室/肌肉/生成笔记/每日合规思考/${fileName}`;\n    \n    // 检查并创建目录\n    const folderPath = '工作室/肌肉/生成笔记/每日合规思考';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    // 创建文件\n    const file = await app.vault.create(filePath, template);\n    new Notice(`每日合规思考已创建: ${fileName}`);\n    \n    // 打开新创建的文件\n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 每日合规思考已生成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('生成每日合规思考失败:', error);\n    new Notice('生成失败: ' + error.message);\n    return '❌ 生成失败，请检查配置';\n  }\n}\n\nexports.default = {\n  entry: entry,\n};"}, "title": "每日合规思考表单"}