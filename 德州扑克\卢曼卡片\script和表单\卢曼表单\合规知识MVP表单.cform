{"id": "compliance-knowledge-mvp-form", "fields": [{"id": "innovation-point", "label": "微创新点", "type": "text", "description": "合规知识的微创新点"}, {"id": "gdpr-article", "label": "GDPR法条", "type": "text", "description": "相关的GDPR法条链接"}, {"id": "pipl-article", "label": "PIPL法条", "type": "text", "description": "相关的PIPL法条链接"}, {"id": "actual-case", "label": "实际案例", "type": "text", "description": "相关实际案例的链接"}, {"id": "combination-hypothesis", "label": "组合假设", "type": "textarea", "rows": 3, "description": "跨法域的合规创新思路"}, {"id": "minimum-verification", "label": "最小验证", "type": "textarea", "rows": 2, "description": "内部分享或行业交流的验证方式"}, {"id": "peer-feedback", "label": "同行反馈", "type": "textarea", "rows": 3, "description": "收到的同行反馈"}, {"id": "iteration-direction", "label": "迭代方向", "type": "textarea", "rows": 2, "description": "后续的迭代方向"}, {"id": "project-application", "label": "实际项目应用", "type": "textarea", "rows": 2, "description": "在实际项目中的应用情况"}, {"id": "risk-avoidance", "label": "避免合规风险", "type": "textarea", "rows": 2, "description": "避免的具体合规风险"}, {"id": "standardization-potential", "label": "可标准化推广", "type": "textarea", "rows": 2, "description": "标准化推广的可能性"}], "action": {"id": "generate-compliance-knowledge-mvp", "type": "runScript", "targetFolder": "", "fileName": "", "scriptSource": "inline", "code": "async function entry() {\n  const { form, app, requestUrl, Notice } = this.$context;\n  \n  try {\n    // 生成AI优化的内容\n    let aiEnhancedContent = '';\n    const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec';\n    \n    const prompt = `你是合规知识产品专家。请基于以下MVP信息，提供专业分析：\n\n创新点：${form['innovation-point']}\nGDPR法条：${form['gdpr-article']}\nPIPL法条：${form['pipl-article']}\n实际案例：${form['actual-case']}\n组合假设：${form['combination-hypothesis']}\n最小验证：${form['minimum-verification']}\n同行反馈：${form['peer-feedback']}\n迭代方向：${form['iteration-direction']}\n项目应用：${form['project-application']}\n风险避免：${form['risk-avoidance']}\n推广潜力：${form['standardization-potential']}\n\n请提供：\n1. 对这个合规知识产品的价值评估\n2. MVP验证方法的优化建议\n3. 商业化和推广策略建议\n4. 持续迭代和改进方向\n\n要求：商业化思维、实用性强、可执行。`;\n    \n    try {\n      const aiResponse = await requestUrl({\n        url: 'https://api.deepseek.com/v1/chat/completions',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: \"deepseek-chat\",\n          messages: [{ role: \"user\", content: prompt }],\n          temperature: 0.7,\n          max_tokens: 1500\n        })\n      });\n      \n      if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n        aiEnhancedContent = aiResponse.json.choices[0].message.content;\n      }\n    } catch (e) {\n      aiEnhancedContent = '(AI分析暂时不可用，请手动补充深度分析)';\n    }\n    \n    // 生成完整内容\n    const template = `### 合规知识产品：${form['innovation-point']}\n**原料法条**：[[${form['gdpr-article']}]] + [[${form['pipl-article']}]] + [[${form['actual-case']}]]\n**组合假设**：${form['combination-hypothesis']}\n**最小验证**：${form['minimum-verification']}\n**同行反馈**：${form['peer-feedback']}\n**迭代方向**：${form['iteration-direction']}\n**ROI计算**：\n- 实际项目应用：${form['project-application']}\n- 避免合规风险：${form['risk-avoidance']}\n- 可标准化推广：${form['standardization-potential']}\n\n---\n\n## 🤖 AI商业分析\n\n${aiEnhancedContent}\n\n---\n\n## 📝 产品迭代记录\n<!-- 记录产品的迭代历程和效果 -->\n\n\n## 🏷️ 标签\n#合规知识产品 #MVP验证 #商业化 #数据合规 #${new Date().toISOString().split('T')[0]}\n\n---\n*生成时间：${new Date().toLocaleString()} | AI助手：DeepSeek*`;\n    \n    // 创建文件\n    const today = new Date();\n    const dateStr = today.toISOString().split('T')[0];\n    const timeStr = today.toTimeString().split(' ')[0].substring(0, 5).replace(':', '');\n    const fileName = `合规知识MVP-${form['innovation-point']}-${dateStr}-${timeStr}.md`;\n    const filePath = `工作室/肌肉/生成笔记/合规知识MVP/${fileName}`;\n    \n    // 检查并创建目录\n    const folderPath = '工作室/肌肉/生成笔记/合规知识MVP';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    // 创建文件\n    const file = await app.vault.create(filePath, template);\n    new Notice(`合规知识MVP已创建: ${fileName}`);\n    \n    // 打开新创建的文件\n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 合规知识MVP已生成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('生成合规知识MVP失败:', error);\n    new Notice('生成失败: ' + error.message);\n    return '❌ 生成失败，请检查配置';\n  }\n}\n\nexports.default = {\n  entry: entry,\n};"}, "title": "合规知识MVP表单"}